package org.befun.community.similarity;

import lombok.SneakyThrows;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.befun.adminx.constant.similarity.FeatureDataType;
import org.befun.adminx.similarity.FeatureSchema;
import org.befun.adminx.similarity.SurveySimhash;
import org.junit.jupiter.api.Test;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;




/**
 * The class description
 *
 * <AUTHOR>
 */
public class SimilarityTest {
    public static final String ANSI_RED = "\u001B[31m";
    public static final String ANSI_GREEN = "\u001B[32m";
    public static final String ANSI_RESET = "\u001B[0m";

    @SneakyThrows
    private List<Map<String, Object>> loadFromExcel(String path) {
        FileInputStream file =new FileInputStream(path);
        XSSFWorkbook wb=new XSSFWorkbook(file);
        XSSFSheet sheet=wb.getSheetAt(0);
        Iterator<Row> itr = sheet.iterator();    //iterating over excel file
        List<Map<String, Object>> result = new ArrayList<>();
        List<String> headers = new ArrayList<>();
        int index = 0;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss", Locale.ENGLISH);

        while (itr.hasNext())
        {
            Row row = itr.next();
            Map<String, Object> tmp = new HashMap<>();
            if (index == 0) {
                for (Cell cell: row) {
                    headers.add(cell.getStringCellValue());
                }
                index++;
                continue;
            }
            for (int j = 0; j < headers.size(); j++) {
                Cell cell = row.getCell(j);
                String header = "";
                if (index > 0) {
                    header = headers.get(j);
                }
                if (cell == null) {
                    tmp.put(header, null);
                } else {
                    switch (cell.getCellType()) {
                        case STRING:
                            if (header.contains("start_time")) {
                                Date date = formatter.parse(cell.getStringCellValue());
                                tmp.put(header, date.getTime() / (1000 * 60));
                            } else {
                                tmp.put(header, cell.getStringCellValue());
                            }
                            break;
                        case NUMERIC:
                            if (header.contains("duration_seconds")) {
                                tmp.put(header, ((int) cell.getNumericCellValue()));
                            } else {
                                tmp.put(header, ((int) cell.getNumericCellValue()));
                            }
                            break;
                        default:
                            System.out.println("unexpected...");
                            break;
                    }
                }
            }
            if (index > 0) {
                result.add(tmp);
            }
            index++;
        }
//        System.out.println("header->");
//        System.out.println(headers);
//        System.out.println(result);
        return result;
    }

    private String buildFingerprintStr(Dictionary<String, Object> item, List<String> features) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < features.size(); i++) {
            sb.append(String.format("%s:%s ", features.get(i), item.get(features.get(i))));
        }
        return sb.toString().trim();
    }

    private List<Map<String, Object>> findNearest(SurveySimhash left, List<Map<String, Object>> items) {
        List<Map<String, Object>> result = new ArrayList<>(items);

        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> item = result.get(i);
            SurveySimhash other = (SurveySimhash) item.get("_hash");
            int distance = left.hammingDistance(other);
            item.put("_distance", distance);
        }

        Collections.sort(result, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> m1, Map<String, Object> m2) {
                int l = ((Integer) m1.get("_distance"));
                int r = ((Integer) m2.get("_distance"));
                return l - r;
            }
        });
        return result;
    }

    @Test
    public void loadFromDatabase() {
        String a = "Q1:0 Q2:2";
        String b = "Q1:0 Q2:3";

        int hashbits = 16;
        int maxbits = 4;

        SurveySimhash simb = new SurveySimhash(b, hashbits, maxbits);
        SurveySimhash sima = new SurveySimhash(a, hashbits, maxbits);

        System.out.printf("source: %s\n", sima.getStrFingerprint());
        System.out.printf("target: %s\n", simb.getStrFingerprint());
        System.out.printf("raw_distance: %d\n", simb.rawDistance(sima));
    }

    @Test
    public void simpleTestBit() {
        List<FeatureSchema> features = new ArrayList<>();
        features.add(FeatureSchema.builder().name("Q1").dataType(FeatureDataType.BIT).build());
        features.add(FeatureSchema.builder().name("Q2").dataType(FeatureDataType.BIT).build());
        features.add(FeatureSchema.builder().name("Q3").dataType(FeatureDataType.BIT).build());
        features.add(FeatureSchema.builder().name("Q4").dataType(FeatureDataType.BIT).build());
        HashMap<String, Object> a = new HashMap<>();
        a.put("Q1", 0);
        a.put("Q2", 0);
        a.put("Q3", 1);
        a.put("Q4", 0);
        HashMap<String, Object> b = new HashMap<>();
        b.put("Q1", 1);
        b.put("Q2", 0);
        b.put("Q3", 1);
        b.put("Q4", 1);

        int hashbits = 16;
        int maxbits = 4;

        SurveySimhash simb = new SurveySimhash(b, features, hashbits, maxbits);
        SurveySimhash sima = new SurveySimhash(a, features, hashbits, maxbits);

        System.out.printf("source: %s %s\n", sima.getStrFingerprint(), sima.getIntFingerprint().toString(2));
        System.out.printf("target: %s %s\n", simb.getStrFingerprint(), simb.getIntFingerprint().toString(2));
        System.out.printf("hamming_distance: %d\n", simb.hammingDistance(sima));
        System.out.printf("raw_distance: %d\n", simb.rawDistance(sima));
    }

    @Test
    public void simpleTestInt() {
        List<FeatureSchema> features = new ArrayList<>();
        features.add(FeatureSchema.builder().name("Q1").dataType(FeatureDataType.INTEGER).build());
        features.add(FeatureSchema.builder().name("Q2").dataType(FeatureDataType.INTEGER).build());
        features.add(FeatureSchema.builder().name("Q3").dataType(FeatureDataType.INTEGER).build());
        features.add(FeatureSchema.builder().name("Q4").dataType(FeatureDataType.INTEGER).build());
        HashMap<String, Object> a = new HashMap<>();
        a.put("Q1", 1);
        a.put("Q2", 2);
        a.put("Q3", 3);
        a.put("Q4", 4);
        HashMap<String, Object> b = new HashMap<>();
        b.put("Q1", 2);
        b.put("Q2", 2);
        b.put("Q3", 3);
        b.put("Q4", 4);

        int hashbits = 16;
        int maxbits = 4;

        SurveySimhash simb = new SurveySimhash(b, features, hashbits, maxbits);
        SurveySimhash sima = new SurveySimhash(a, features, hashbits, maxbits);

        System.out.printf("source: %s %s\n", sima.getStrFingerprint(), sima.getIntFingerprint().toString(2));
        System.out.printf("target: %s %s\n", simb.getStrFingerprint(), simb.getIntFingerprint().toString(2));
        System.out.printf("hamming_distance: %d\n", simb.hammingDistance(sima));
        System.out.printf("raw_distance: %d\n", simb.rawDistance(sima));
    }

    @Test
    public void localTestRH() {
        //
        String path = "/Users/<USER>/Workspace/surveyplus/data/工作情境调查问卷_问题数据_分组.xlsx";
        List<String> feature_names = List.of( "X1", "X2","X3","X4", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8","Ba1","Ba2","Ba3","Ba4", "C1", "C2","C3","C4","C5", "D1", "D2","D3","D4","D5", "D6","D7","D8", "D9", "Ea1","Ea2","Ea3", "Ea4", "Eb1","Eb2","Eb3", "Eb4");
        List<FeatureSchema> features = new ArrayList<>();
//        features.add(FeatureSchema.builder().name("start_time").offset(0).dataType(FeatureDataType.TIMESTAMP).build());
//        features.add(FeatureSchema.builder().name("ip_province").offset(32).dataType(FeatureDataType.STRING).build());
        for (String feature : feature_names) {
            features.add(FeatureSchema.builder().name(feature).dataType(FeatureDataType.INTEGER).build());
        }
//        features.add(FeatureSchema.builder().name("duration_seconds").offset(12).dataType(FeatureDataType.INTEGER).build());
        int k = 30;

        List<Map<String, Object>> result = loadFromExcel(path);
//        List<String> features = List.of( "start_time", "ip_province", "X1", "X2","X3","X4", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8","Ba1","Ba2","Ba3","Ba4", "C1", "C2","C3","C4","C5", "D1", "D2","D3","D4","D5", "D6","D7","D8", "D9", "Ea1","Ea2","Ea3", "Ea4", "Eb1","Eb2","Eb3", "Eb4");

        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> item = result.get(i);
            SurveySimhash simhash = new SurveySimhash(item, features);
            item.put("_hash", simhash);
            item.put("_finger", simhash.getStrFingerprint());
        }

        // 3998270332898304
//        String testId = "4192959635039232";
//        List<String> targetIds = List.of(
//                "4192961761551360",
//                "4192962921217024",
//                "4192963154715648",
//                "4192963188800512",
//                "4192963445963776",
//                "4192963625526272",
//                "4192964110236672",
//                "4192965218509824"
//        );
        String testId = "4193017095431168";
        List<String> targetIds = List.of(
                "4193017102187520",
                "4193017120209920",
                "4193017157827584",
                "4193017168837632",
                "4193017206258688",
                "4193017206645760",
                "4193017285491712",
                "4193017318128640",
                "4193019568759808",
                "4193019593407488",
                "4193019699307520",
                "4193019940676608",
                "4193025314700288"
        );
        List<String> missIds = new ArrayList<>();
        Map<String, Object> source = result.stream().filter(x -> x.get("id").equals(testId)).findFirst().get();

        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> target = result.get(i);
            int diffs = 0;
            if (i > 0) {
                for (String feature : feature_names) {
                    if (!source.get(feature).equals(target.get(feature))) {
                        diffs++;
                    }
                }
            }
            target.put("_diff", diffs);
        }

        SurveySimhash hash = (SurveySimhash) source.get("_hash");
        List<Map<String, Object>> sorted = findNearest(hash, result);
        System.out.printf("source id: %s %s %s\n", source.get("id"), source.get("_finger"), ((SurveySimhash)source.get("_hash")).getStrFingerprint());
        System.out.printf("Top %s nearest response\n", k);

        for (Map<String, Object> x : sorted.subList(0, k)) {
            if (!targetIds.contains(x.get("id"))) {
                missIds.add(x.get("id").toString());
            }
            pretty(source, x);
        }

        System.out.printf("Target compare\n");
        List<Map<String, Object>> target_result = sorted.stream().filter(x -> targetIds.contains(x.get("id"))).collect(Collectors.toList());
        for (Map<String, Object> x : target_result) {
            pretty(source, x);
        }

        System.out.printf("Missed compare\n");
        List<Map<String, Object>> miss_result = sorted.stream().filter(x -> missIds.contains(x.get("id"))).collect(Collectors.toList());
        for (Map<String, Object> x : miss_result) {
            pretty(source, x);
        }
    }

    private void pretty(Map<String, Object> source, Map<String, Object> target) {
        String[] source_finger = ((String) source.get("_finger")).split(" ");
        String[] target_finger = ((String) target.get("_finger")).split(" ");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < target_finger.length; i++) {
            String item = target_finger[i];
            sb.append(String.format("%s%s%s ", item.equals(source_finger[i]) ? ANSI_GREEN : ANSI_RED, item, ANSI_RESET));
        }
        System.out.println(String.format("id: %s\tdif: %s\tdis:%d\t%s", target.get("id"), target.get("_diff"), target.get("_distance"), sb));
    }
}
