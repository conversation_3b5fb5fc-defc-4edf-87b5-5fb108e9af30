package org.befun.community.similarity;

import cn.hanyi.expression.expression.ExpressionEvaluator;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.befun.adminx.constant.similarity.FeatureDataType;
import org.befun.adminx.similarity.FeatureSchema;
import org.befun.adminx.similarity.SurveySimhash;
import org.junit.jupiter.api.Test;
import org.springframework.util.Assert;

import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * The class description
 *
 * <AUTHOR>
 */
public class AuditTest {

    @Test
    public void simpleAuditExpressionCheck() {
        Map<String, Object> value = Map.of(
                "Q1", 1,
                "Q1001", 2
                );
        String expr = "Q1001 != null and !(Q1001 < Q1)";
        ExpressionEvaluator evaluator = new ExpressionEvaluator(expr, false);
        Object result = evaluator.evaluate(value);
        Assert.isTrue(result.equals(true), expr + " should be true");
    }

}
