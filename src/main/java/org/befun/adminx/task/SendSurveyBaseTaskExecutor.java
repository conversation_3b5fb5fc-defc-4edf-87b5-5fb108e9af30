package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.result.WxMpMassSendResult;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.FailMsg;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.SubmitStatus;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.sms.MessageSendResponseInfo;
import org.befun.adminx.dto.sms.SmsNotifyTextInfo;
import org.befun.adminx.dto.task.*;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.repository.SurveySendRecordRepository;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.MessageService;
import org.befun.adminx.service.SurveyLinkService;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.adminx.task.sms.SmsService;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.task.PageableTaskExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class SendSurveyBaseTaskExecutor<T extends SendSurveyTaskDto> extends PageableTaskExecutor<T> implements ISendSurveyBaseTaskExecutor<T> {

    @Autowired
    protected SmsService smsService;
    @Autowired
    protected WechatConfigureService weChatMpService;
    @Autowired
    private SurveyLinkService linkService;
    @Autowired
    protected MessageService messageService;
    @Autowired
    protected SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    protected CommunityUserService communityUserService;

    @Value("${survey.send-ignore:false}")
    private boolean ignoreSend;

    /**
     * 发送任务
     */
    protected void performTask(List<Long> sendIds, SendSelectedDto dto) {
        String taskId = getTaskId(dto.getTaskId());
        appendTotal(taskId, sendIds.size());

        // 计算定时发送的时间差
        Duration duration = null;
        LocalDateTime sendTime;
        if (dto.getSendTime() != null && (sendTime = DateHelper.parseAdjust(dto.getSendTime())) != null) {
            duration = Duration.between(LocalDateTime.now(), sendTime);
            if (duration.isNegative()) {
                duration = null;
            }
        }

        BiFunction<Integer, Integer, T> getPageDataDto = (page, size) -> {
            T d = buildDto(dto.getTemplate());
            d.setSurveyId(dto.getTask().getSid());
            d.setChannelId(dto.getTask().getChannelId());
            d.formatLogIds(splitPageList(sendIds, page, size));
            log.info("发送问卷任务：已创建任务 data = {}", JsonHelper.toJson(d));
            return d;
        };

        if (duration != null) {
            // 定时发送
            performPageDelay(taskId, sendIds.size(), duration, getPageDataDto);
        } else {
            // 立即发送
            performPage(taskId, sendIds.size(), getPageDataDto);
        }
    }

    /**
     * 构建问卷地址
     */
    public String buildSurveyUrl(String url, String clientId, Long cuid,Long trackId) {
        if(StringUtils.isEmpty(url)) return null;
        String surveyUrl = String.format("%s&clientId=%s&customerName=%d&trackId=%d", url, clientId, cuid,trackId);
        return linkService.toShortUrl(surveyUrl, true, null, null);
    }


    /**
     * 任务id
     */
    public String getTaskId(Long taskId) {
        return "delivery-task-" + taskId;
    }

    public void batchSend( List<Long> logIds, Function<SurveySendRecord, Boolean> send) {
        // TBD 校验channel
        Optional.ofNullable(logIds).ifPresent(ids -> {
            ids.forEach(logId -> {
                surveySendRecordRepository.findById(logId).ifPresent(i -> {
                    if (send.apply(i)) {
                        if(i.getType() == SendType.SEND_SCORE_NOTICE) return;
                        i.setSendStatus(SendStatus.SEND_SUCCESS);
                        i.setSendCount(i.getSendCount() == null ? 1 : (i.getSendCount() + 1));
                        if(i.getSendMethod() == TaskSendMethod.WECHAT_SERVICE) {
                            i.setSendWechatCount(i.getSendWechatCount() == null ? 1 : (i.getSendWechatCount() + 1));
                        } else {
                            i.setSendMessageCount(i.getSendMessageCount() == null ? 1 : (i.getSendMessageCount() + 1));
                        }
                        i.setSubmitStatus(SubmitStatus.NOT_VISIT);
                        i.setFailMsg(FailMsg.SUCCESS.toString());
                        i.setSendTime(new Date());
                        surveySendRecordRepository.save(i);
                    } else {
                        if(i.getType() == SendType.SEND_SCORE_NOTICE) return;
                        i.setSendStatus(SendStatus.SEND_FAIL);
                        i.setFailMsg(FailMsg.API_ERROR.toString());
                        i.setSendTime(new Date());
                        surveySendRecordRepository.save(i);
                    }
                });
            });
        });
    }

    /**
     * 重新发送
     */
    public void resend(Long taskId, List<SurveySendRecord> records, TemplateInfoDto template, Map<String, Object> parameters) {
        if (CollectionUtils.isEmpty(records) || taskId == null) {
            return;
        }
        List<Long> sendLogIds = updateContent(records,template,parameters);
        if(sendLogIds == null || sendLogIds.size() ==0) return;
        performPage(getTaskId(taskId), sendLogIds.size(), (page, size) -> {
            T dto = buildDto(template);
            dto.setTaskId(taskId.toString());
            dto.formatLogIds(sendLogIds);
            log.info("发送问卷任务：已创建重发任务 data = {}", JsonHelper.toJson(dto));
            return dto;
        });
    }

    /**
     * 发送微信
     */
    public boolean sendWeChat(SendSurveyByWechatTaskDto dto, SurveySendRecord i) {
        if (StringUtils.isEmpty(i.getAccount())) {
            log.warn("发送问卷(微信公众号)：失败, openId为空, logId={}, content={}", i.getId(), i.getContent());
            return false;
        }
        WxMpTemplateMessage message = new WxMpTemplateMessage();
        message.setToUser(i.getAccount());
        message.setTemplateId(dto.getTemplateId());
        message.setData(JsonHelper.toList(i.getContent(), WxMpTemplateData.class));
        message.setUrl(i.getSendUrl());
        boolean r = false;
        try {
            if (ignoreSend) { // 本地测试的时候，设置为true不真实发送短信和微信 SURVEY_SEND_IGNORE=true
                log.info("发送问卷(微信公众号)：已忽略, logId={}, openId={}, content={}, survey.send-ignore=true", i.getId(), i.getAccount(), i.getContent());
                r = true;
            } else {
                String result =  weChatMpService.sendTemplateMsg(message);
                if (result != null) {
                    log.info("发送问卷(微信公众号)：成功, logId={}, openId={}, content={}, result={}", i.getId(), i.getAccount(), i.getContent(), result);
                    r = true;
                }
            }
        } catch (Exception e) {
            log.error("发送问卷(微信公众号)：失败, logId={}, openId={}, content={}", i.getId(), i.getAccount(), i.getContent(), e);
        }
        return r;
    }
    /**
     * 发送微信图文消息
     */
    public boolean sendWeChatMsg(SendSelectedDto dto) {
        if (dto == null || dto.getUserList() == null || dto.getUserList().size() == 0) {
            log.warn("发送问卷(微信图文消息)：失败, openId为空, dto={}", dto);
            return false;
        }
        try {
            WxMpMassSendResult result =  weChatMpService.sendWechatMsg(dto.getTemplate().getWeChatTemplateId(),
                    dto.getUserList().stream().map(CommunityUserSearchDto::getOpenid).collect(Collectors.toList()));
            if ("0".equals(result.getErrorCode())) {
                log.info("发送问卷(微信图文消息)：成功");
                return true;
            } else {
                log.info("发送问卷(微信图文消息)：失败, code:{}, msg:{}",result.getErrorCode(),result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("发送问卷(微信图文消息)：失败, content={}", e.getMessage());
        }
        return false;
    }

    /**
     * 发送短信
     */
    public boolean sendSms(SendSurveyBySmsTaskDto sendSurveyTaskDto, SurveySendRecord i) {
        if (!RegularExpressionUtils.isMobile(i.getAccount())) {
            log.warn("发送问卷(短信)：失败，手机号不正确, logId={}, mobile={}, content={}", i.getId(), i.getAccount(), i.getContent());
            return false;
        }
        SmsNotifyTextInfo message = new SmsNotifyTextInfo();
        message.setContent(i.getContent());
        message.setMobile(i.getAccount());
        try {
            MessageSendResponseInfo info = smsService.sendMessage3(message);
            if (info.isSuccess()) {
                log.info("发送问卷(短信)：成功, logId={}, mobile={}, content={}", i.getId(), i.getAccount(), i.getContent());
                return true;
            } else {
                log.error("发送问卷(短信)：失败, logId={}, mobile={}, content={}", i.getId(), i.getAccount(),info.getContent());
            }
        } catch (Exception e) {
            log.error("发送问卷(短信)：失败, logId={}, mobile={}, content={}", i.getId(), i.getAccount(), i.getContent(), e);
        }
        return false;
    }

    public abstract T buildDto(TemplateInfoDto template);

    public abstract String buildContent(TemplateInfoDto template, Map<String, Object> parameters, String url);

    public abstract Long saveCustomerLog(SendSelectedDto dto, SurveySendRecord record);

    public abstract Optional<SurveySendRecord> findCustomerLogByTaskId(Long taskId, Long cuid);

    public abstract List<Long> updateContent(List<SurveySendRecord> records,TemplateInfoDto template, Map<String, Object> parameters);
}
