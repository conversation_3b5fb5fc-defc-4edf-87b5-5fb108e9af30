package org.befun.adminx.task.sms;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.adminx.constant.XPackAppType;
import org.befun.adminx.dto.sms.ChuanglanResponse;
import org.befun.adminx.dto.sms.SmsNotifyInfo;
import org.befun.adminx.dto.sms.SmsNotifyTextInfo;
import org.befun.adminx.property.sms.SmsProviderProperty;
import org.befun.core.utils.JsonHelper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("chuanglan")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-chuanglan"}, havingValue = "true")
public class ChuanglanSmsProvider implements ISmsProvider<SmsProviderProperty.SmsChuanglan> {

    private static final String URL_TEXT = "https://smssh1.253.com/msg/v1/send/json";
    private static final String URL_TEMPLATE = "https://smssh1.253.com/msg/variable/json";

    @Override
    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        String globalSignature = chuanglan.getSignature();
        String globalRealSignature = chuanglan.getRealSignature();
        config.getTemplates().forEach(t -> {
            if (StringUtils.isEmpty(t.getSignature())) {
                t.setSignature(globalSignature);
            }
            if (StringUtils.isEmpty(t.getRealSignature())) {
                t.setRealSignature(globalRealSignature);
            }
        });
    }

    @Override
    public SmsProviderProperty.SmsChuanglan requireConfig(SmsProviderProperty config) {
        Assert.notNull(config, "创蓝短信服务配置不能为空");
        Assert.notNull(config.getChuanglan(), "创蓝短信服务配置不能为空");
        return config.getChuanglan();
    }

    @Override
    public XPackAppType getTemplateConfigType() {
        return XPackAppType.SMS_TEMPLATE_CHUANGLAN;
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        log.info("send to chuanglan {} template:{}", info.getMobile(), info.getTemplateId());
        try {
            String realSignature = StringUtils.isEmpty(info.getRealSignature()) ? chuanglan.getRealSignature() : info.getRealSignature();
            // 【253云通讯】创蓝云通讯验证码是{$var}，祝您体验愉快
            String msg = realSignature + info.getOriginTemplate();
            // ***********,1234；***********,4321
            List<String> params = new ArrayList<>();
            params.add(info.getMobile());
            info.getVariableValues().forEach(i -> params.add(i.getValue()));

            Map<String, Object> data = new HashMap<>();
            data.put("account", chuanglan.getAppId());
            data.put("password", chuanglan.getAppSecret());
            data.put("msg", msg);
            data.put("params", String.join(",", params));
            data.put("report", "true");
            String body = JsonHelper.toJson(data);
            log.info(body);
            String response = Request.Post(URL_TEMPLATE)
                    .connectTimeout(1000 * 30)
                    .socketTimeout(1000 * 30)
                    .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                    .execute().returnContent().asString();
            info.setResponse(response);
            log.info(response);
            ChuanglanResponse chuanglanResponse = JsonHelper.toObject(response, ChuanglanResponse.class);
            if (chuanglanResponse.success()) {
                log.info("has sent to chuanglan {}", chuanglanResponse.getMsgId());
                return true;
            } else {
                log.error("failed to chuanglan code:{} message:{}", chuanglanResponse.getCode(), chuanglanResponse.getErrorMsg());
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }
    /*
             app-id: ${CHUANGLAN_APP_ID:N798909_N9266634}
            app-secret: ${CHUANGLAN_APP_SECRET:04HnT65FLhbb34}
            signature: ${CHUANGLAN_SIGNATURE:【体验家DEV】}
            real-signature: ${CHUANGLAN_REAL_SIGNATURE:【体验家DEV】}
     */

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("account", chuanglan.getAppId());
            data.put("password", chuanglan.getAppSecret());
            data.put("msg", info.getContent());
            data.put("phone", info.getMobile());
            String body = JsonHelper.toJson(data);
            log.debug(body);
            String response = Request.Post(URL_TEXT)
                    .connectTimeout(1000 * 30)
                    .socketTimeout(1000 * 30)
                    .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                    .execute().returnContent().asString();
            info.setResponse(response);
            log.info(response);
            ChuanglanResponse chuanglanResponse = JsonHelper.toObject(response, ChuanglanResponse.class);
            log.info("has sent to chuanglan {}", chuanglanResponse.getMsgId());
            return true;
//            if (chuanglanResponse.success()) {
//                log.info("has sent to chuanglan {}", chuanglanResponse.getMsgId());
//                return true;
//            } else {
//                log.error("failed to chuanglan code:{} message:{}", chuanglanResponse.getCode(), chuanglanResponse.getErrorMsg());
//            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }

}
