package org.befun.adminx.task.sms;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.adminx.constant.XPackAppType;
import org.befun.adminx.dto.sms.SmsNotifyInfo;
import org.befun.adminx.dto.sms.SmsNotifyTextInfo;
import org.befun.adminx.property.sms.SmsProviderProperty;

/**
 * The class description
 *
 * <AUTHOR>
 */
public interface ISmsProvider<CONFIG extends SmsProviderProperty.SmsProviderConfig> {

    void init(SmsProviderProperty config);

    CONFIG requireConfig(SmsProviderProperty config);

    default XPackAppType getTemplateConfigType() {
        throw new NotImplementedException();
    }

    /**
     * 使用短信模板 发送短信
     */
    default boolean sendMessageByTemplate(SmsProviderProperty providerConfiguration, SmsNotifyInfo info) {
        throw new NotImplementedException();
    }

    /**
     * 直接发送短信内容
     */
    default boolean sendMessageByText(SmsProviderProperty providerConfiguration, SmsNotifyTextInfo info) {
        throw new NotImplementedException();
    }
}