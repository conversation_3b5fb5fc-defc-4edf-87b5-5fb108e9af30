package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.order.OrderPayDto;
import org.befun.adminx.entity.OrganizationRecharge;
import org.befun.adminx.entity.OrganizationRechargeRefund;
import org.befun.adminx.entity.SampleOrder;
import org.befun.adminx.repository.OrganizationRechargeRefundRepository;
import org.befun.adminx.repository.OrganizationRechargeRepository;
import org.befun.adminx.repository.SampleOrderRepository;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Task("adminx-order-pay")
public class OrderPayExecutor extends BaseTaskExecutor<OrderPayDto> {

    @Autowired
    private SampleOrderRepository sampleOrderRepository;
    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;

    @Override
    public void run(OrderPayDto dto, TaskContextDto context) {
        if (dto.getSurveyId() != null && dto.getChannelId() != null) {
            SampleOrder sampleOrder = sampleOrderRepository.findFirstBySurveyIdAndChannelId(dto.getSurveyId(), dto.getChannelId());
            if (sampleOrder != null) {
                if ("paid".equals(dto.getStatus()) && dto.getRechargeId() != null) {
                    OrganizationRecharge recharge = organizationRechargeRepository.findById(dto.getRechargeId()).orElse(null);
                    if (recharge != null) {
                        sampleOrder.setPayType(recharge.getType());
                        sampleOrder.setPayTime(recharge.getPayTime());
                        sampleOrder.setPayNo(recharge.getPayNo());
                        sampleOrderRepository.save(sampleOrder);
                    }
                } else if ("refund".equals(dto.getStatus()) && dto.getRefundId() != null) {
                    OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findById(dto.getRefundId()).orElse(null);
                    if (refund != null) {
                        sampleOrder.setPayRefundNo(refund.getRefundNo());
                        sampleOrderRepository.save(sampleOrder);
                    }
                }
            }
        }
    }
}
