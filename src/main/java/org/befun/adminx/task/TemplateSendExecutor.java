package org.befun.adminx.task;

import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxMpErrorMsgEnum;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.SendStatus;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.dto.ext.SendTaskExDto;
import org.befun.adminx.dto.ext.TemplateSendTaskDto;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.entity.SendRecord;
import org.befun.adminx.repository.SendRecordRepository;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.SurveyLinkService;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Slf4j
@Task("template-send")
@Component
public class TemplateSendExecutor extends BaseTaskExecutor<TemplateSendTaskDto> {

    private final static int TEMPLATE_SEND_MAX_NUM = 100000;

    private static ThreadPoolExecutor templateSendExecutor;

    @Value("${befun.admin.front-route-url}")
    private String frontRouteUrl;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private WechatConfigureService wechatConfigureService;

    @Autowired
    private SurveyLinkService linkService;

    @Autowired
    private SendRecordRepository sendRecordRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @PostConstruct
    public void initThreadPoolExecutor() {
        templateSendExecutor = new ThreadPoolExecutor(0, 10, 30,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public void run(TemplateSendTaskDto detailDto, TaskContextDto context) {
        try {
            run2(detailDto, context);
        }catch (Exception e){
            e.printStackTrace();
            Optional<SendRecord> optionalSendRecord = sendRecordRepository.findById(detailDto.getSendRecordId());
            optionalSendRecord.ifPresent(sendRecord -> {
                sendRecord.setSendStatus(SendStatus.SEND_FAIL);
                sendRecord.setMessage(e.getMessage());
                sendRecordRepository.save(sendRecord);
            });
        }
    }

    public void run2(TemplateSendTaskDto detailDto, TaskContextDto context) {
        log.info("start to template-send, message body = {}", JsonHelper.toJson(detailDto));
        Optional<SendRecord> optionalSendRecord = sendRecordRepository.findById(detailDto.getSendRecordId());

        if (StringUtils.isEmpty(detailDto.getThirdPartTemplateId()) || StringUtils.isEmpty(detailDto.getSurveyUrl())) {
            log.error("模板id或问卷地址为空，templateId= {}, surveyUrl = {}", detailDto.getThirdPartTemplateId(), detailDto.getSurveyUrl());
            throw new BadRequestException("模板id或问卷地址为空");
        }
        if (!existTemplate(detailDto.getThirdPartTemplateId())) {
            throw new BadRequestException("模板id不存在");
        }
        String key = String.format("adminx:template-send:maxNum:%s", LocalDate.now());
        String maxNumKey = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(maxNumKey)) {
            redisTemplate.opsForValue().set(key, String.valueOf(TEMPLATE_SEND_MAX_NUM), 24, TimeUnit.HOURS);
        }
        List<String> openids = this.getOpenIds(detailDto);
        String sendUrl = String.format(frontRouteUrl, detailDto.getTaskId(), linkService.toShortUrl(detailDto.getSurveyUrl(), false, null, null));
        log.info("模板推送url：{},推送openid数量：{}", sendUrl, openids.size());
        if (templateSendExecutor.isShutdown()) {
            this.initThreadPoolExecutor();
        }
        CountDownLatch latch = new CountDownLatch(openids.size());
        AtomicInteger successNum = new AtomicInteger(0);

        ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        this.scheduleUpdateCompleteCount(scheduledExecutorService, optionalSendRecord, successNum);
        //更新状态
        optionalSendRecord.ifPresent(sendRecord -> {
            sendRecord.setSendStatus(SendStatus.SENDING);
            sendRecord.setCompleteCount(0);
            sendRecord.setTotalCount(openids.size());
            sendRecordRepository.save(sendRecord);
        });
        openids.stream().forEach(openid -> {
            templateSendExecutor.execute(() -> {
                try {
                    this.sendTemplateMsg(detailDto.getThirdPartTemplateId(), openid, sendUrl, buildTemplateData(detailDto.getParams()));
                    successNum.getAndIncrement();
                    redisTemplate.opsForValue().decrement(key);
                } catch (WxErrorException e) {
                    log.error("send template msg error,openid:{}", openid, e);
                    if (e.getError().getErrorCode() == WxMpErrorMsgEnum.CODE_45009.getCode()) {
                        log.error("=========模板推送当日调用上限=========");
                        optionalSendRecord.ifPresent(sendRecord -> {
                            sendRecord.setSendStatus(SendStatus.SEND_SUCCESS);
                            sendRecord.setCompleteCount(successNum.get());
                            sendRecordRepository.save(sendRecord);
                        });
                        templateSendExecutor.shutdownNow();
                        scheduledExecutorService.shutdown();
                    }
                }finally {
                    latch.countDown();
                }
            });
        });
        try {
            //等待任务发送完成
            latch.await();
            scheduledExecutorService.shutdownNow();
            //发送完成更新状态
            optionalSendRecord.ifPresent(sendRecord -> {
                sendRecord.setSendStatus(SendStatus.SEND_SUCCESS);
                sendRecord.setCompleteCount(successNum.get());
                sendRecordRepository.save(sendRecord);
            });
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("end template-send consumer,templateId:{},剩余推送数量:{},本次成功：{}",
                detailDto.getThirdPartTemplateId(), redisTemplate.opsForValue().get(key), successNum.get());
    }

    public List<String> getOpenIds(TemplateSendTaskDto detailDto) {
        List<String> openids = new ArrayList<>();
        if (detailDto.getSendType().equals(SendTaskType.ALL)) {
            //所有关注的用户
            openids = wechatConfigureService.getAllOpenid();
        } else if (detailDto.getSendType().equals(SendTaskType.DESIGN) || detailDto.getSendType().equals(SendTaskType.RDS)) {
            //根据条件获取用户
            SendTaskExDto task = new SendTaskExDto();
            task.setSendType(detailDto.getSendType());
            task.setSamples(detailDto.getSamples());
            List<CommunityUserSearchDto> users = communityUserService.searchSendTaskUser(task);
            if (CollectionUtils.isNotEmpty(users)) {
                openids = users.stream().map(CommunityUserSearchDto::getOpenid).collect(Collectors.toList());
            }
        }
        return openids;
    }


    /**
     * 每10秒统计线程池执行完成的任务
     */
    private void scheduleUpdateCompleteCount(ScheduledExecutorService scheduledExecutorService, Optional<SendRecord> optionalSendRecord, AtomicInteger successNum) {
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            //long completedTasks = templateSendExecutor.getCompletedTaskCount();
            optionalSendRecord.ifPresent(sendRecord -> {
                sendRecord.setCompleteCount(successNum.get());
                sendRecordRepository.save(sendRecord);
            });
        }, 0, 10, TimeUnit.SECONDS);
    }


    public void sendTemplateMsg(String templateId, String openid, String url, List<WxMpTemplateData> data) throws WxErrorException {
        WxMpTemplateMessage message = new WxMpTemplateMessage();
        message.setTemplateId(templateId);
        message.setToUser(openid);
        message.setUrl(url);
        message.setData(data);
        try {
            wechatConfigureService.getWeChatMpService().getTemplateMsgService().sendTemplateMsg(message);
            log.info("模板消息发送成功,templateId:{},openid:{}", templateId, openid);
        } catch (WxErrorException e) {
            log.error("模板消息发送失败,body:{},message:{},{}", JsonHelper.toJson(message), e.getMessage(), e.getError());
            throw e;
        }
    }

    /**
     * 模板id是否存在
     *
     * @param templateId
     * @return
     * @throws WxErrorException
     */
    public boolean existTemplate(String templateId)  {
        if (StringUtils.isEmpty(templateId)) {
            return false;
        }
        try {
            List<WxMpTemplate> templates = wechatConfigureService.getWeChatMpService().getTemplateMsgService().getAllPrivateTemplate();
            for (WxMpTemplate template : templates) {
                if (templateId.equals(template.getTemplateId())) {
                    return true;
                }
            }
            return false;
        }catch (WxErrorException e){
            e.printStackTrace();
            return false;
        }
    }

    public List<WxMpTemplateData> buildTemplateData(Map<String, Object> parameters) {
        if (Objects.isNull(parameters)) {
            return new ArrayList<>();
        }
        List<WxMpTemplateData> data = Optional.ofNullable(parameters)
                .stream()
                .flatMap(j -> j.entrySet().stream())
                .filter(m -> m.getKey() != null && m.getValue() != null)
                .map(k -> new WxMpTemplateData(k.getKey(), k.getValue().toString()))
                .collect(Collectors.toList());
        return data;
    }



    public static void main(String[] args) {
        test();
    }

    public static void test(){
        int sum = 1000;
        AtomicInteger num = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(sum);
        ThreadPoolExecutor executorService = (ThreadPoolExecutor) Executors.newFixedThreadPool(5);
        ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            //long completedTasks = executorService.getCompletedTaskCount();
            System.out.println("已执行的任务数量：" + num.get());
        }, 0, 3, TimeUnit.SECONDS); // 每隔5秒执行一次任务

        for (int i = 0; i < sum; i++) {
            int finalI = i;
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    boolean flag = false;
                    try {
                        ThreadUtil.sleep(100);
                        if (finalI % 10 == 0) {
                            throw new BadRequestException();
                        }
                        flag = true;
                    } catch (Exception e) {
                        System.out.println("===执行出错");
                        flag = false;
                    } finally {
                        if (flag) {
                            System.out.println("===执行成功");
                            num.getAndIncrement();
                        }
                        latch.countDown();
                    }
                }

            });
        }
        try {
            latch.await();
            System.out.println("任务执行完毕" + num.get());
            scheduledExecutorService.shutdown();
            executorService.shutdown();
            System.out.println("===================");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}

