package org.befun.adminx.task;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.event.EventResponseDto;
import org.befun.adminx.dto.event.SurveyResponseViewDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.service.DeliveryService;
import org.befun.adminx.service.SurveyBaseEntityService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/10/13 16:43
 */
@Slf4j
@Task("adminx-response-view")
@Component
public class ResponseViewExecutor extends BaseTaskExecutor<EventResponseDto> {

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private DeliveryService deliveryService;

    public void run(EventResponseDto dto, TaskContextDto context) {
        try {
            log.info("start to consumer response view, message body = {}", JsonHelper.toJson(dto));
            var response = surveyBaseEntityService.require(SurveyResponse.class, dto.getResponseId());
            if (response.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
                SurveyResponseViewDto responseViewDto = new SurveyResponseViewDto(response);
                deliveryService.surveyResponseViewConsumer(responseViewDto);
            }
            log.info("end response view consumer");
        } catch (Exception e) {
            log.error("consumer response view error:{}", e.getMessage());
            e.printStackTrace();
        }
    }
}
