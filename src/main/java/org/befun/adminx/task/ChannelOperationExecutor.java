package org.befun.adminx.task;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.event.SurveyChannelOperationDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.service.DeliveryService;
import org.befun.adminx.service.SampleOrderService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/10/13 16:43
 */
@Slf4j
@Task("adminx-channel-operation")
@Component
public class ChannelOperationExecutor extends BaseTaskExecutor<SurveyChannelOperationDto> {

    @Autowired
    private SampleOrderService orderService;

    @SneakyThrows
    public void run(SurveyChannelOperationDto detailDto, TaskContextDto context)
    {
        log.info("start to consumer channel operation, message body = {}", JsonHelper.toJson(detailDto));
        orderService.channelOperation(detailDto);
        log.info("end channel operation consumer");
    }
}
