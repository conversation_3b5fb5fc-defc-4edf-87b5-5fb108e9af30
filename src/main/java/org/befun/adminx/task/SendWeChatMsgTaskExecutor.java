package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.FailMsg;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.constant.WechatSubscribeStatus;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.task.SendSelectedDto;
import org.befun.adminx.dto.task.SendSurveyByWechatTaskDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@Task("send-community-user-survey-by-wechat-msg")
public class SendWeChatMsgTaskExecutor extends SendSurveyBaseTaskExecutor<SendSurveyByWechatTaskDto> implements ISendSurveyByWeChatTaskExecutor {


    @Override
    public <C> void runCoordinator(C coordinatorData) {
        SendSelectedDto dto = (SendSelectedDto) coordinatorData;
        log.info("开始分解发送问卷（接口请求）任务：data = {}", JsonHelper.toJson(dto));

        List<Long> sendIds = new ArrayList<>();
        //发送图文消息
        Boolean result = sendWeChatMsg(dto);
        //写入survey_send_record
        parseContacts(dto, sendIds, result);
        if (sendIds.isEmpty()) {
            return;
        }

    }

    /**
     * 图文消息重发
     * @param dto
     * @param dto
     */
    public void resend(List<SurveySendRecord> records, SendSelectedDto dto) {
        if (dto == null || dto.getUserList().isEmpty() || records.isEmpty()) {
            return;
        }
        //发送图文消息
        Boolean result = sendWeChatMsg(dto);
        if (result) {
            records.forEach(r -> {
                r.setSendStatus(SendStatus.SEND_SUCCESS);
            });
            surveySendRecordRepository.saveAll(records);
        } else {
            throw new BadRequestException("微信有发送限制");
        }
    }

    private void parseContacts(SendSelectedDto dto, List<Long> sendIds, Boolean result) {
        dto.getUserList().stream().filter(c -> !StringUtils.isEmpty(c.getOpenid())).forEach(c ->{
            SurveySendRecord record = new SurveySendRecord();
            record.setClientId(UUID.randomUUID().toString());
            record.setBaseLineSurveyId(dto.getBaseLineSurveyId());
            record.setSendUrl(buildSurveyUrl(dto.getTask().getSurveyUrl(), record.getClientId(), c.getCuid(),dto.getTask().getId()));
            record.setContent("");
            record.setName(c.getNickname());
            record.setAccount(c.getOpenid());
            record.setSurveyId(dto.getTask().getSid());
            record.setTaskId(dto.getTask().getId());
            record.setCuid(c.getCuid());
            record.setBaseLineResponseId(c.getResponseId());
            record.setType(dto.getType());
            record.setTaskType(dto.getTask().getTaskType());
            record.setSendMethod(TaskSendMethod.WECHAT_MSG);
            record.setSendTime(new Date());
            record.convertSubscribeStatus(c.getWechatSubscribe());
            record.setPhoneStatus(StringUtils.isNotEmpty(c.getMobile()) ? true:false);
            if(record.getSubscribeStatus() != WechatSubscribeStatus.SUBSCRIBE) {
                record.setSendStatus(SendStatus.UN_SEND);
                record.setFailMsg(FailMsg.UN_SUBSCRIBE.toString());
            } else if(result) {
                record.setSendStatus(SendStatus.SEND_SUCCESS);
            } else {
                record.setSendStatus(SendStatus.SEND_FAIL);
            }
            surveySendRecordRepository.save(record);
        });
    }

    @Override
    public SendSurveyByWechatTaskDto buildDto(TemplateInfoDto template) {
        return SendSurveyByWechatTaskDto.create(template);
    }

    @Override
    public String buildContent(TemplateInfoDto template, Map<String, Object> parameters, String url) {
       List<WxMpTemplateData> data = Optional.ofNullable(parameters)
                .stream()
                .flatMap(j -> j.entrySet().stream())
                .filter(m -> m.getKey() != null && m.getValue() != null)
                .map(k -> new WxMpTemplateData(k.getKey(), k.getValue().toString()))
                .collect(Collectors.toList());
        return JsonHelper.toJson(data);
    }

    @Override
    public Long saveCustomerLog(SendSelectedDto dto, SurveySendRecord record) {
        return null;
    }

    @Override
    public Optional<SurveySendRecord> findCustomerLogByTaskId(Long taskId, Long cuid) {
        return surveySendRecordRepository.findFirstByTaskIdAndCuidOrderByIdDesc(taskId,cuid);
    }

    @Override
    public List<Long> updateContent(List<SurveySendRecord> records,TemplateInfoDto template, Map<String, Object> parameters) {
        return null;
    }

    @Override
    public void run(SendSurveyByWechatTaskDto sendSurveyTaskDto, TaskContextDto taskContextDto) {
        return;
    }
}
