package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.surveyaudit.SurveyContentAuditDto;
import org.befun.adminx.service.SurveyAuditRecordService;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Task("adminx-survey-audit")
public class SurveyContentAuditExecutor extends BaseTaskExecutor<SurveyContentAuditDto> {

    @Autowired
    private SurveyAuditRecordService surveyAuditRecordService;

    @Override
    public void run(SurveyContentAuditDto detailDto, TaskContextDto context) {
        surveyAuditRecordService.addBySurveyContentAudit(detailDto);
    }
}
