package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.event.EventResponseDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.repository.SurveyResponseCellRepository;
import org.befun.adminx.service.DeliveryService;
import org.befun.adminx.service.SurveyBaseEntityService;
import org.befun.adminx.service.audit.ConsumerService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/10/13 16:43
 */
@Slf4j
@Task("adminx-response-submit")
@Component
public class ResponseSubmitExecutor extends BaseTaskExecutor<EventResponseDto> {

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private ConsumerService consumerService;

    public void run(EventResponseDto dto, TaskContextDto context) {
        try {
            log.info("start to consumer response submit, message body = {}", JsonHelper.toJson(dto));
            var response = surveyBaseEntityService.require(SurveyResponse.class, dto.getResponseId());
            if (response.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
                var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
                List<SurveyResponseCell> cells = surveyResponseCellRepository.findAllBySurveyIdAndResponseId(response.getSurveyId(), response.getId());
                SurveyResponseMessageDto responseMessageDto = new SurveyResponseMessageDto(survey, response, cells);
                log.info("SurveyResponseMessageDto:{}", JsonHelper.toJson(responseMessageDto));
                consumerService.consumer(responseMessageDto);
            }
            log.info("end response submit consumer,resposneId:{}", dto.getResponseId());
        } catch (Exception e) {
            log.error(" consumer response submit error:{}", e.getMessage());
            e.printStackTrace();
        }
    }
}