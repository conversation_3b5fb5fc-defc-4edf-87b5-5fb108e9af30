package org.befun.adminx.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.*;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.task.SendSelectedDto;
import org.befun.adminx.dto.task.SendSurveyBySmsTaskDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@Task("send-community-user-survey-by-phone-msg")
public class SendSurveyByPhoneMsgTaskExecutor extends SendSurveyBaseTaskExecutor<SendSurveyBySmsTaskDto> implements ISendSurveyByPhoneMsgTaskExecutor {

    @Override
    public <C> void runCoordinator(C coordinatorData) {
        SendSelectedDto dto = (SendSelectedDto) coordinatorData;
        log.info("开始分解发送问卷（接口请求）任务：data = {}", JsonHelper.toJson(dto));

        List<Long> sendIds = new ArrayList<>();
        parseContacts(dto, sendIds);
        if (sendIds.isEmpty()) {
            return;
        }
        performTask(sendIds, dto);
    }

    private void parseContacts(SendSelectedDto dto, List<Long> sendIds) {
        dto.getUserList().stream().filter(c -> !StringUtils.isEmpty(c.getOpenid())).forEach(c ->{
            SurveySendRecord record = new SurveySendRecord();
            record.setBaseLineSurveyId(dto.getBaseLineSurveyId());
            record.setTaskId(dto.getTask().getId());
            record.setSurveyId(dto.getTask().getSid());
            record.setClientId(UUID.randomUUID().toString());
            record.setName(c.getNickname());
            record.setAccount(c.getMobile());
            record.setSendUrl(buildSurveyUrl(dto.getTask().getSurveyUrl(), record.getClientId(), c.getCuid(),dto.getTask().getId()));
            record.setContent(buildContent(dto.getTemplate(), dto.getContentTemplate(), record.getSendUrl()));
            record.setType(dto.getType());
            record.setTaskType(dto.getTask().getTaskType());
            record.setCuid(c.getCuid());
            record.setBaseLineResponseId(c.getResponseId());
            record.setSendMethod(TaskSendMethod.PHONE_MSG);
            record.convertSubscribeStatus(c.getWechatSubscribe());
            record.setPhoneStatus(StringUtils.isNotEmpty(c.getMobile()) ? true:false);
            Long rid = saveCustomerLog(dto, record);
            if(rid != null && rid != 0l) sendIds.add(rid);
        });
    }

    @Override
    public SendSurveyBySmsTaskDto buildDto(TemplateInfoDto template) {
        return SendSurveyBySmsTaskDto.create(template);
    }

    @Override
    public String buildContent(TemplateInfoDto template, Map<String, Object> parameters, String url) {
        return messageService.buildMessage(template, parameters, url);
    }

    @Override
    public Long saveCustomerLog(SendSelectedDto dto, SurveySendRecord record) {
        if(StringUtils.isEmpty(record.getAccount()) && !record.getPhoneStatus()) {
            record.setAccount("");
            record.setSendStatus(SendStatus.UN_SEND);
            record.setFailMsg(FailMsg.UN_BIND_PHONE.toString());
            surveySendRecordRepository.save(record);
            return null;
        } else {
            record.setSendStatus(SendStatus.WAIT_SEND);
            surveySendRecordRepository.save(record);
            return record.getId();
        }
    }

    @Override
    public List<Long> updateContent(List<SurveySendRecord> records,TemplateInfoDto template, Map<String, Object> parameters) {
        List<Long> ids = new ArrayList<>();
        if(records == null || records.size() == 0) return ids;

        //cuid 列表
        List<Long> cuidList = records.stream().map(r -> r.getCuid()).collect(Collectors.toList());
        if(cuidList == null || cuidList.size() == 0) return ids;

        //获取社区用户的最新数据
        List<CommunityUserSearchDto> users = communityUserService.searchSendFollowTaskUser(cuidList);
        if(users == null || users.size() == 0) return ids;
        Map<Long,CommunityUserSearchDto> userMap = new HashMap<>();
        users.stream().forEach(i ->{
            userMap.put(i.getCuid(),i);
        });

        records.forEach(record -> {
            CommunityUserSearchDto userSearchDto = userMap.get(record.getCuid());
            if(userSearchDto != null) {
                record.setSendMethod(TaskSendMethod.PHONE_MSG);
                record.convertSubscribeStatus(userSearchDto.getWechatSubscribe());
                record.setPhoneStatus(StringUtils.isNotEmpty(userSearchDto.getMobile()) ? true:false);
                record.setAccount(userSearchDto.getMobile());
                record.setContent(buildContent(template, parameters, record.getSendUrl()));
                record.setType(template.getSendType());
                if(template.getSendType() == SendType.SEND_SURVEY) {
                    if (record.getPhoneStatus() == false) {
                        record.setSendStatus(SendStatus.UN_SEND);
                        record.setFailMsg(FailMsg.UN_BIND_PHONE.toString());
                    } else {
                        record.setSendStatus(SendStatus.WAIT_SEND);
                        ids.add(record.getId());
                    }
                } else {
                    ids.add(record.getId());
                }
            }
        });
        surveySendRecordRepository.saveAll(records);
        return ids;
    }

    @Override
    public Optional<SurveySendRecord> findCustomerLogByTaskId(Long taskId, Long cuid) {
        return surveySendRecordRepository.findFirstByTaskIdAndCuidOrderByIdDesc(taskId,cuid);
    }

    @Override
    public void run(SendSurveyBySmsTaskDto sendSurveyTaskDto, TaskContextDto taskContextDto) {
        batchSend(sendSurveyTaskDto.parseLogIds(),
                i -> sendSms(sendSurveyTaskDto, i));
    }
}
