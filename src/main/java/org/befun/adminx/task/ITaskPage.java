package org.befun.adminx.task;

import org.befun.task.dto.PageableTaskDetailDto;
import org.befun.task.dto.TimedTaskDto;

import java.time.Duration;
import java.util.List;
import java.util.function.BiFunction;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/21 17:51
 */
public interface ITaskPage<T extends PageableTaskDetailDto> {
    default void performPage(String taskId, long count, BiFunction<Integer, Integer, T> getPageDataDto) {
    }

    default void performPageDelay(String taskId, long count, Duration duration, BiFunction<Integer, Integer, T> getPageDataDto) {
    }

    default void performPageAt(String taskId, long count, TimedTaskDto timedTaskDto, BiFunction<Integer, Integer, T> getPageDataDto) {
    }

    default <R> List<R> splitPageList(List<R> originList, int page, int size) {
        return null;
    }

    default void reset(String taskId) {
    }

    default void updateTotal(String taskId, long total) {
    }

    default void appendTotal(String taskId, long appendTotal) {
    }
}
