//package org.befun.adminx.scheduled;
//
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.adminx.dto.event.SurveyChannelOperationDto;
//import org.befun.adminx.dto.event.SurveyResponseViewDto;
//import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
//import org.befun.adminx.service.DeliveryService;
//import org.befun.adminx.service.SampleOrderService;
//import org.befun.adminx.utils.DateFormatter;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * The class description
// *
// * @Author: Leo
// * @Date: 2022/8/2 15:26
// */
//@Component
//@Slf4j
//public class ScheduledRedisQueue {
//    @Autowired
//    private DeliveryService deliveryService;
//
//    @Autowired
//    private SampleOrderService orderService;
//
//    @Autowired
//    private StringRedisTemplate redisTemplate;
//
//    @Value("${befun.task.queue.response-submit-key}")
//    private String responseSubmitKey;
//
//    @Value("${befun.task.queue.response-view-key}")
//    private String responseViewKey;
//
//    @Value("${befun.task.queue.channel-operation-key}")
//    private String channelOperationKey;
//
//    /**
//     * *提交问卷 消费任务
//     * @throws InterruptedException
//     */
//    @Scheduled(fixedDelay = 1000) // 每隔1秒触发一次任务
//    public void responseSubmitConsumer() throws InterruptedException {
//        String message = redisTemplate.opsForList().rightPop(responseSubmitKey);
//        if (StringUtils.isNotEmpty(message)) {
//            // 处理获取到的消息
//            log.info("start to consumer response submit, message body = {}", message);
//            SurveyResponseMessageDto detailDto = JsonHelper.toObject(message, SurveyResponseMessageDto.class);
//            deliveryService.surveyResponseConsumer(detailDto);
//            log.info("end response submit consumer");
//        }
//    }
//
//    /**
//     * *打开问卷 消费任务
//     * @throws InterruptedException
//     */
//    @Scheduled(fixedDelay = 1000) // 每隔1秒触发一次任务
//    public void responseViewConsumer() throws InterruptedException {
//        String message = redisTemplate.opsForList().rightPop(responseViewKey);
//        if (StringUtils.isNotEmpty(message)) {
//            // 处理获取到的消息
//            log.info("start to consumer response view, message body = {}", message);
//            SurveyResponseViewDto detailDto = JsonHelper.toObject(message, SurveyResponseViewDto.class);
//            deliveryService.surveyResponseViewConsumer(detailDto);
//            log.info("end response view consumer");
//        }
//    }
//
//    /**
//     * *渠道创建 消费任务
//     * @throws InterruptedException
//     */
//    @Scheduled(fixedDelay = 1000) // 每隔1秒触发一次任务
//    public void channelOperationConsumer() throws InterruptedException {
//        String message = redisTemplate.opsForList().rightPop(channelOperationKey);
//        if (StringUtils.isNotEmpty(message)) {
//            // 处理获取到的消息
//            log.info("start to consumer channel operation, message body = {}", message);
//            SurveyChannelOperationDto detailDto = JsonHelper.toObject(message, SurveyChannelOperationDto.class);
//            orderService.channelOperation(detailDto);
//            log.info("end consumer channel operation consumer");
//        }
//    }
//}
