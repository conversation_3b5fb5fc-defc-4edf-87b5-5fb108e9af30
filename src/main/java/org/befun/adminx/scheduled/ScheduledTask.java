package org.befun.adminx.scheduled;

import org.befun.adminx.service.*;
import org.befun.adminx.utils.DateFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/2 15:26
 */
@Component
public class ScheduledTask {
    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private CustomerStatisticsService customerStatisticsService;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private OrganizationService organizationService;

    @Value("${community.enable-sync-all}")
    private Boolean enableSyncAll;

    @Value("${community.enable-sync-location}")
    private Boolean enableSyncLocation;

    @Scheduled(cron = "0 0 5 * * ?")
    public void scheduledTask() {
        System.out.println("任务执行时间：" + DateFormatter.getStringTime(new Date()));
        String time = statisticsService.syncData();
        System.out.println("同步日期：" + time);
        System.out.println("任务执行结束：" + DateFormatter.getStringTime(new Date()));
    }

    /**
     * 企业客户问卷 预警 看榜 数据统计
     * 每天凌晨4点执行
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void syncCustomerData() {
        System.out.println("任务执行时间：" + DateFormatter.getStringTime(new Date()));
        customerStatisticsService.syncData();
        System.out.println("任务执行结束：" + DateFormatter.getStringTime(new Date()));
    }

    /**
     * 检查任务的调查期限 自动暂停任务
     * 每天凌晨12点01分执行
     */
    @Scheduled(cron = "1 0 0 * * ?")
    public void myScheduledTask() {
        System.out.println("自动暂停已过调查期限的投放任务" + DateFormatter.getStringTime(new Date()));
        deliveryService.autoPauseDeliveryTask();
    }

    /**
     * 自动降级已过有效期的账号
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void downgrade() {
        // 在每天凌晨1点检查过期的账号 降级
        System.out.println("自动降级已过有效期的账号" + DateFormatter.getStringTime(new Date()));
        organizationService.downgrade();
    }
}
