package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.AppType;
import org.befun.adminx.constant.AppVersion;
import org.befun.adminx.entity.Organization;
import org.befun.adminx.entity.OrganizationDto;
import org.befun.adminx.repository.OrganizationRepository;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 19:32
 */
@Service
@Slf4j
public class OrganizationService extends BaseService<Organization, OrganizationDto, OrganizationRepository> {

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizationService self;

    @Autowired
    private XmPermissionService xmPermissionService;
//    public Optional<OrganizationDto> getOrganizationByCode(Long id) {
//        Optional<Organization> org = organizationRepository.findOneById(id);
//        if (org.isEmpty()) {
//            return Optional.empty();
//        }
//        OrganizationDto dto = new OrganizationDto();
//        dto.setId(org.get().getId());
//        dto.setCode(org.get().getCode());
//        dto.setName(org.get().getName());
//        return Optional.of(dto);
//    }

    // 降级版本
    public void downgrade() {
        log.info("企业降级任务开始");
        List<Organization> organizations = repository.findByAvailableDateEndBefore(new Date());
        organizations.forEach(organization -> {
            try{
             Date end = organization.getAvailableDateEnd();
             // 判断是否过期
                if (end != null && DateFormatter.getEndDate(end).before(new Date())) {
                    List<String> app = JsonHelper.toList(organization.getAppTypes(), String.class);
                    if(app != null && app.contains(AppType.cem.name())){
                        HashMap<String, Object> optionalLimit = organization.getOptionalLimit();
                        userService.parseOrgVersion(organization, AppVersion.FREE.getText());
                        userService.parseOrgOptionalLimit(organization, AppVersion.FREE.getText(), optionalLimit, false);
                        //免费版 增加50年时长
                        organization.setAvailableDateEnd(DateFormatter.addDays(new Date(), 50*365));
                        self.downgradeLogoLimit(organization, AppVersion.FREE.getText(), optionalLimit);
                        xmPermissionService.updateOrgCemVersions(organization.getId(), AppVersion.FREE.getText());
                    }
                }
            }catch (Exception e){
                log.error("企业Id:{}降级失败",organization.getId(), e);
            }
        });
        log.info("企业降级任务结束");
    }

    @Async
    public void downgradeLogoLimit(Organization org, String cemVersion, HashMap<String, Object> optionalLimit){
        optionalLimit.put("logo_change_limit", false);
        optionalLimit.put("bi_data_field_edit", false);
        optionalLimit.put("sys_manage_api_edit", false);
        optionalLimit.put("multi_language_limit", false);
        org.setOptionalLimit(optionalLimit);
        repository.save(org);
    }

    /**
     * 如果没有版本，则返回 {@link AppVersion#EMPTY}
     */
    public AppVersion parseOrgVersion(String version) {
        if (version != null) {
            return AppVersion.parseByText(version);
        }
        return AppVersion.EMPTY;
    }
}
