package org.befun.adminx.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.ast.Var;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.dto.MetaDto;
import org.befun.adminx.dto.grid.*;
import org.befun.adminx.dto.sample.SimpleUserDto;
import org.befun.adminx.dto.user.UserStatisticsDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.survey.SurveyCompletedRecord;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.*;
import org.befun.adminx.service.excel.ReadGridInspectorListener;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.befun.adminx.constant.SubmitStatus.SUBMIT;

@Slf4j
@Service
public class CommunityGridService extends BaseService<CommunityGrid, CommunityGridDto, CommunityGridRepository> {

	@Autowired
	private CommunityUserRepository communityUserRepository;

	@Autowired
	private CommunityGridRepository communityGridRepository;

	@Autowired
	private SurveyResponseRepository surveyResponseRepository;

	@Autowired
	private DeliveryTaskRepository taskRepository;

	@Autowired
	private SurveyCompletedRecordRepository completedRecordRepository;

	@Autowired
	private SurveySendRecordRepository surveySendRecordRepository;

	@Autowired
	private StatisticsService statisticsService;

	@Override
	public Page<CommunityGridDto> findAll(ResourceEntityQueryDto<CommunityGridDto> queryDto) {
		if(StringUtils.isEmpty(queryDto.getSort())){
			queryDto.setSorts(Sort.by("createTime").descending());
		}
		return super.findAll(queryDto);
	}

	@SneakyThrows
	public Boolean importGridInspec(MultipartFile file) {
		if(file == null){
			throw new BadRequestException("文件不存在");
		}
		EasyExcel.read(file.getInputStream(), ReadCommunityGridDto.class, new ReadGridInspectorListener(repository))
				.excelType(ExcelTypeEnum.CSV)
				.charset(Charset.forName("GBK"))
				.sheet()
				.doRead();
		return true;
	}

	public Boolean joinGrid(JoinGridDto dto) {
		CommunityUser user = communityUserRepository.findById(dto.getCuid()).orElseThrow();
		if(user.getGridId() != null){
			throw new BusinessException("已经加入过网格");
		}
		CommunityGrid grid = communityGridRepository.findFirstByGridId(dto.getGridId());
		if(grid == null){
			throw new BadRequestException("无效的网格");
		}
		user.setGridId(grid.getGridId());
		communityUserRepository.save(user);
		return true;
	}

	public List<SimpleUserDto> getAllUserByGrid(String gid) {
		return communityUserRepository.findByGridId(gid);
	}

	public GridDetailDto gridDetailStatistics(String gridId, Long taskId,int page,int limit) {
		GridDetailDto dto = new GridDetailDto();
		MetaDto meta = new MetaDto();

		DeliveryTask deliveryTask = taskRepository.findById(taskId).orElseThrow(()->new BadRequestException("任务不存在"));
		dto.setTitle(deliveryTask.getTitle());
		List<SurveySendRecord> surveySendRecords = surveySendRecordRepository.findByTaskId(taskId);

		//未完成cuid
		Set<Long> unComplete = surveySendRecords.stream().filter(x -> x.getSubmitStatus()!=SUBMIT).map(x->x.getCuid()).collect(Collectors.toSet());
		//完成记录
		List<SurveyCompletedRecord> surveyCompletedRecords = completedRecordRepository.findBySidAndGridId(deliveryTask.getSid(),gridId);
		//未完成cuid
		Set<Long> complete = surveyCompletedRecords.stream().map(SurveyCompletedRecord::getCuid).collect(Collectors.toSet());
		unComplete.removeAll(complete);

		Page<CommunityUser> users = communityUserRepository.findByIdInAndGridId(unComplete,gridId,PageRequest.of(page-1,limit));
		dto.setUnCompleteUser(users.getContent());
		meta.setLimit(limit);
		meta.setPage(page);
		meta.setTotal(users.getTotalElements());
		dto.setMeta(meta);
		//当前网格发送追访任务的cuid
		List<Long> rid = surveyCompletedRecords.stream().map(x -> x.getResponseId()).collect(Collectors.toList());

		List<SurveyResponse> surveyResponse = surveyResponseRepository.findAllById(rid);
		long passCount = surveyResponse.stream().filter(x -> x.getStatus() == ResponseStatus.FINAL_SUBMIT).count();
		dto.setPassAuditCount(passCount);
		long waitAuditCount = surveyResponse.stream().filter(x -> x.getStatus() == ResponseStatus.WAIT_AUDIT).count();
		dto.setWaitAuditCount(waitAuditCount);

		return dto;
	}

	/**
	 * 网格成员
	 * @param grid
	 * @param page
	 * @param limit
	 * @return
	 */
	public Page<SimpleGridMemberDto>  member(String grid,int page,int limit){
		Pageable pageable = PageRequest.of(page - 1, limit,Sort.by("joinGridTime").descending());
		Page<SimpleGridMemberDto> SimpleUserDto = communityUserRepository.findAllByGridId(grid,pageable);
		return SimpleUserDto;
	}

	/**
	 * 网格成员资料
	 * @param gridId
	 * @param cuid
	 * @return
	 */
	public GridMemberDetailDto memberDetail(String gridId,Long cuid) {
		GridMemberDetailDto dto = new GridMemberDetailDto();

		CommunityUser user = communityUserRepository.findById(cuid).orElseThrow(()->new BadRequestException("用户不存在"));
		if(!user.getGridId().equals(gridId)){
			throw new BadRequestException("用户不存在");
		}

		List<Long> responseIds = completedRecordRepository.findResponseIdsByOpenId(user.getOpenId());
		List<SurveyResponse> responses = surveyResponseRepository.findAllById(responseIds);

		dto.setWaitAuditCount((int)responses.stream().filter(x->x.getStatus().equals(ResponseStatus.WAIT_AUDIT)).count());
		dto.setWaitAuditCount((int)responses.stream().filter(x->x.getStatus().equals(ResponseStatus.FINAL_SUBMIT)).count());

		dto.setCuid(user.getId());
		dto.setNickName(user.getNickName());
		dto.setWechatSubscribe(user.getWechatSubscribe());
		dto.setRemark(user.getRemark());
		dto.setAvatar(user.getAvatar());

		return dto;
	}
}



















