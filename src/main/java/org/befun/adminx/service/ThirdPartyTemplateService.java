package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftList;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.entity.ThirdPartyTemplate;
import org.befun.adminx.entity.ThirdPartyTemplateDto;
import org.befun.adminx.repository.ThirdPartyTemplateRepository;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/5/28 14:48
 */
@Service
@Slf4j
public class ThirdPartyTemplateService extends BaseService<ThirdPartyTemplate, ThirdPartyTemplateDto, ThirdPartyTemplateRepository> {

    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    private WechatConfigureService wechatConfigureService;

    @Value("${befun.extension.wechat-mp.app-id}")
    private String appId;


    @Override
    public Page<ThirdPartyTemplateDto> findAll(ResourceEntityQueryDto<ThirdPartyTemplateDto> queryDto) {
        //删掉历史草稿
        thirdPartyTemplateRepository.deleteBySendMethod(TaskSendMethod.WECHAT_MSG);
        return super.findAll(queryDto);
    }

    @Override
    protected void afterMapToDto(List<ThirdPartyTemplate> entity, List<ThirdPartyTemplateDto> dto) {
        super.afterMapToDto(entity, dto);
        //获取最新草稿
        getWechatDraftList(dto);
    }

    public TemplateInfoDto getSmsTemplate(Long thirdTemplateId) {
        ThirdPartyTemplate template = thirdPartyTemplateRepository.findById(thirdTemplateId).orElse(null);
        if (template == null
                || StringUtils.isEmpty(template.getOpenId())
                || StringUtils.isEmpty(template.getSignatureId())) {
            throw new BadRequestException("短信模板不存在");
        }
        return TemplateInfoDto.createSms(template.getOpenId(), template.getSignatureId(), template.getExample(), template.getType());
    }

    public TemplateInfoDto getWeChatTemplate(Long thirdTemplateId) {
        ThirdPartyTemplate template = thirdPartyTemplateRepository.findById(thirdTemplateId).orElse(null);
        if (template == null || StringUtils.isEmpty(template.getOpenId())) {
            throw new BadRequestException("微信模板不存在");
        }

        return TemplateInfoDto.createWeChat(appId, template.getOpenId(), template.getType());
    }

    public TemplateInfoDto getSmsTemplateByType(SendType sendType) {
        ThirdPartyTemplate template = thirdPartyTemplateRepository.findFirstBySendMethodAndTypeAndStatusIsTrueOrderByIdDesc(TaskSendMethod.PHONE_MSG, sendType);
        if (template == null
                || StringUtils.isEmpty(template.getOpenId())
                || StringUtils.isEmpty(template.getSignatureId())) {
            throw new BadRequestException("短信模板不存在");
        }
        return TemplateInfoDto.createSms(template.getOpenId(), template.getSignatureId(), template.getExample(), template.getType());
    }

    /**
     * *获取微信草稿模板
     * @param dto
     * @return
     */
    public List<ThirdPartyTemplateDto> getWechatDraftList(List<ThirdPartyTemplateDto> dto) {
        List<ThirdPartyTemplateDto> list;
        WxMpDraftList draftList = wechatConfigureService.getDraftList();
        //草稿转换成模板
        if (draftList != null && draftList.getTotalCount() > 0) {
            long size = dto == null || dto.size() == 0 ? 1l: dto.get(dto.size() - 1).getId();
            AtomicLong count = new AtomicLong(size);
            draftList.getItems().forEach(item ->{
                long id = count.incrementAndGet();
                ThirdPartyTemplate template = new ThirdPartyTemplate();
                template.setId(id);
                template.setStatus(true);
                template.setOpenId(item.getMediaId());
                template.setName(item.getContent().getNewsItem().get(0).getTitle());
                template.setSendMethod(TaskSendMethod.WECHAT_MSG);
                dto.add(mapToDto(repository.save(template)));
            });
        }
        return dto;
    }
}
