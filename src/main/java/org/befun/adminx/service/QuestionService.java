package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.repository.SurveyQuestionColumnRepository;
import org.befun.adminx.repository.SurveyQuestionItemRepository;
import org.befun.adminx.repository.SurveyQuestionRepository;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static org.befun.adminx.utils.QuestionsUtils.questionsFilterGroup;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuestionService extends CustomEmbeddedService<SurveyQuestion, SurveyQuestionDto, SurveyQuestionRepository> {

    @Autowired
    SurveyQuestionRepository repository;
    @Autowired
    private SurveyQuestionColumnRepository columnRepository;
    @Autowired
    private SurveyQuestionItemRepository itemRepository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private SurveyService surveyService;


    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }

    @Override
    public Page<SurveyQuestionDto> findAllEmbeddedMany(Long entityId, String embeddedMapperBy, ResourceEntityQueryDto<SurveyQuestionDto> queryDto) {
        if (queryDto.getSorts().isUnsorted()) {

            queryDto.setSorts(Sort.by(Sort.Direction.ASC, "sequence"));
        }
        return super.findAllEmbeddedMany(entityId, embeddedMapperBy, queryDto);
    }

    /**
     * 获取题型
     *
     * @param questionId
     * @return
     */
    public SurveyQuestion requireQuestion(Long questionId) {
        Optional<SurveyQuestion> questionOptional = repository.findById(questionId);
        if (questionOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return questionOptional.get();
    }

    /**
     * 手动获取题型
     *
     * @param survey
     */
    public void customLoadQuestion(Survey survey) {
        if (survey == null) {
            return;
        }

        entityManager.detach(survey);

        ConcurrentHashMap<Long, Long> qIds = new ConcurrentHashMap<>();
        List<SurveyQuestion> questions = questionsFilterGroup(survey.getQuestions());
        questions.parallelStream().forEach(i -> qIds.put(i.getId(), i.getId()));

        if (!qIds.isEmpty()) {
            List<SurveyQuestionColumn> columns = columnRepository.findAll((Specification<SurveyQuestionColumn>) (root, query, cb) -> root.get("question").in(qIds.keySet()), Sort.by(Sort.Direction.ASC, "sequence"));
            List<SurveyQuestionItem> items = itemRepository.findAll((Specification<SurveyQuestionItem>) (root, query, cb) -> root.get("question").in(qIds.keySet()), Sort.by(Sort.Direction.ASC, "sequence"));

            ConcurrentHashMap<Long, List<SurveyQuestionColumn>> columnMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<Long, List<SurveyQuestionItem>> itemMap = new ConcurrentHashMap<>();

            columns.parallelStream().forEach(i -> {
                columnMap.computeIfAbsent(i.getQuestion().getId(), k -> new ArrayList<>()).add(i);
            });
            items.parallelStream().forEach(i -> {
                itemMap.computeIfAbsent(i.getQuestion().getId(), k -> new ArrayList<>()).add(i);
            });

            questions.parallelStream().forEach(i -> {
                List<SurveyQuestionItem> is = itemMap.getOrDefault(i.getId(), new ArrayList<>());
                List<SurveyQuestionColumn> cs = columnMap.getOrDefault(i.getId(), new ArrayList<>());
                is.sort(Comparator.comparingInt(SurveyQuestionItem::getSequence));
                cs.sort(Comparator.comparingInt(SurveyQuestionColumn::getSequence));
                i.setItems(is);
                i.setColumns(cs);
            });
        }

    }

    /**
     * 手动保存
     */
    public SurveyQuestion save(SurveyQuestion surveyQuestion) {
        SurveyQuestion saved = repository.save(surveyQuestion);
        return saved;
    }

    public void graphQuestion(Survey survey){
        repository.findAllBySurvey(survey).forEach(i -> {
        });
    }

}
