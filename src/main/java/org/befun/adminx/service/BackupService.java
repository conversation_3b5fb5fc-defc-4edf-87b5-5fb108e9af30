package org.befun.adminx.service;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.text.StringSubstitutor;
import org.befun.adminx.constant.backup.BackupLogType;
import org.befun.adminx.dto.backup.BackupDiffResultDto;
import org.befun.adminx.dto.backup.BackupRecordDto;
import org.befun.adminx.dto.clone.CloneAccountDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.repository.BackupAccountRepository;
import org.befun.adminx.repository.BackupLogRepository;
import org.befun.adminx.repository.TemplateAccountRepository;
import org.befun.adminx.repository.UserRepository;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.generator.Snowflake;
import org.befun.core.service.BaseService;
import org.befun.task.annotation.TaskLock;
import org.hibernate.Session;
import org.hibernate.jdbc.ReturningWork;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 10:00
 */
@Service
@Slf4j
public class BackupService extends BaseService<BackupAccount, BackupAccountDto, BackupAccountRepository> {

    public class BackupDepartment {
        private Long id;
        private String title;

        // Getter 和 Setter
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }


    class BackupDepTableMeta {
        public String table;
        public String fieldId;
        public List<String> relatedTables;

        public BackupDepTableMeta(String table, String fieldId, List<String> relatedTables) {
            this.table = table;
            this.fieldId = fieldId;
            this.relatedTables = relatedTables;
        }
    }

    @Autowired
    private EntityManager em;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BackupLogRepository logRepository;

    @Autowired
    private TemplateAccountRepository templateAccountRepository;

    @Autowired
    private UserRepository userRepository;

    private final Snowflake generator = new Snowflake();

    private static String rootPath = "/var/data";
    private static String db = "cem_platform";
    private static List<String> surveyL1Tables = List.of("survey_logic", "survey_question");
    private static List<String> surveyL2Tables = List.of("survey_question_column", "survey_question_item");
    private static List<String> rootTables = List.of("survey_group", "survey", "journey_map", "bi_text_project", "event_monitor_rules", "event_result_group", "event_result_group_query", "event_result");
    private static List<String> journeyTables = List.of("journey", "journey_component", "element_event_stat", "element_persona", "element_linker",  "element_curve", "element_textbox", "experience_indicator");
    private List<BackupDepTableMeta> generalDepTables = List.of(
            new BackupDepTableMeta("survey_response", "r_id", List.of("survey_response_cell")),
            new BackupDepTableMeta("bi_dataset", "dataset_id", List.of("bi_dataset_field", "bi_dataset_join")),
            new BackupDepTableMeta("bi_dashboard", "dashboard_id", List.of("bi_dashboard_filter", "bi_dashboard_link")),
            new BackupDepTableMeta("bi_chart", "chart_id", List.of("bi_chart_drill", "bi_chart_filter", "bi_chart_measure", "bi_chart_dimension"))
    );

    @Override
    public <S extends BaseEntityDTO<BackupAccount>> BackupAccountDto create(S data) {
        return super.create(data);
    }

    /**
     * 备份指定Table
     */
    @SneakyThrows
    private List<BackupRecordDto> backupTable(Long orgId, String tableName, String query) {
        List<BackupRecordDto> records = new ArrayList();
        List<Map<String, Object>> result = jdbcTemplate.queryForList(query);
        log.info("start to backup for org {} table {}", orgId, tableName);

        for (Map<String, Object> item : result) {
            log.debug("found %s id: {} title: {}", tableName, item.get("id"), item.get("title"));
            StringBuilder sb = new StringBuilder();
            List<String> cols = item.keySet().stream().collect(Collectors.toList());
            List<String> colsNames = cols.stream().map(x -> String.format("`%s`", x)).collect(Collectors.toList());
            sb.append(String.format("INSERT INTO `%s`.`%s`(%s) VALUES (", db, tableName, String.join(", ", colsNames)));

            // 创建 raw 字典，用于保存列名和值的完整解析数据
            HashMap<String, Object> raw = new HashMap<>();

            for (int i = 0; i < cols.size(); i++) {
                String col = cols.get(i);
                Object val = item.get(col);
                if (val != null) {
                    if (val instanceof Integer || val instanceof Float || val instanceof Double || val instanceof Long || val instanceof Boolean) {
                        sb.append(val);
                    } else {
                        String mv = val.toString().replaceAll("'", "\\\\'");
                        sb.append(String.format("'%s'", mv));
                    }
                    raw.put(col, val);  // 将列名和值放入 raw 字典
                } else {
                    sb.append("NULL");
                    raw.put(col, null);
                }
                if (i != cols.size() - 1) {
                    sb.append(",");
                }
            }
            sb.append(String.format(");\n"));
//            os.write(sb.toString().getBytes());
            String sql = sb.toString();
            String checksum = DigestUtils.md5Hex(sql);
            records.add(BackupRecordDto.builder()
                            .id((Long) item.get("id"))
                            .t(tableName)
                            .s(sql)
                            .raw(raw)
                            .c(checksum)
                    .build());
        }
        log.info("finish to backup for org {} table {}", orgId, tableName);
        return records;
    }

    private String buildTableQuery(String template, String table, Long orgId) {
        return buildTableQuery(template, table, orgId, null);
    }

    private String buildTableQuery(String template, String table, Long orgId, Map<String, Object> params) {
        Map<String, Object> values = new HashMap<>();
        values.put("db", db);
        values.put("table", table);
        values.put("orgId", orgId);
        if (params != null) {
            values.putAll(params);
        }
        StringSubstitutor sub = new StringSubstitutor(values, "{", "}");
        return sub.replace(template);
    }

    private Long getRootRole(Long orgId) {
        String template = "" +
                "SELECT id " +
                "FROM cem_platform.role s WHERE s.org_id = ? and type = 1;";
        String q = buildTableQuery(template, "role", orgId);
        // 使用 queryForObject 获取查询结果
        Long result = jdbcTemplate.queryForObject(q, Long.class, orgId);
        return result != null ? result : 0L;
    }

    private BackupDepartment getRootDepartment(Long orgId) {
        String template = "" +
                "SELECT id, title " +
                "FROM cem_platform.department s WHERE s.org_id = ? and pid = 0;";
        String q = buildTableQuery(template, "department", orgId);

        // 使用 queryForObject 获取查询结果，并映射到 Department 对象
        BackupDepartment result = jdbcTemplate.queryForObject(q,
                (rs, rowNum) -> {
                    BackupDepartment department = new BackupDepartment();
                    department.setId(rs.getLong("id"));
                    department.setTitle(rs.getString("title"));
                    return department;
                }, orgId);

        return result != null ? result : new BackupDepartment(); // 如果没有找到结果，返回一个空的 Department 对象
    }


    private List<BackupRecordDto> backupInsertAllByOrg(Long orgId) throws IOException {
        List<BackupRecordDto> records = new ArrayList();
        for (BackupDepTableMeta tableMeta: generalDepTables) {
            String rootTable = tableMeta.table;
            for (String table: tableMeta.relatedTables) {
                String template = String.format("" +
                        "SELECT * FROM {db}.{table} WHERE %s IN (" +
                        "SELECT id FROM {db}.%s WHERE org_id = {orgId}" +
                        ");\n", tableMeta.fieldId, rootTable);
                String q = buildTableQuery(template, table, orgId);
                records.addAll(backupTable(orgId, table, q));
            }
            String q = String.format("SELECT * FROM `%s`.`%s` WHERE org_id = %d", db, rootTable, orgId);
            records.addAll(backupTable(orgId, rootTable, q));
        }

        for (String table : surveyL1Tables) {
            String template = "" +
                    "SELECT t.* from {db}.{table} AS t " +
                    "LEFT JOIN {db}.`survey` AS s ON s.id = t.s_id " +
                    "WHERE s.org_id = {orgId}" +
                    ";\n";
            String q = buildTableQuery(template, table, orgId);
            records.addAll(backupTable(orgId, table, q));
        }

        // insert new
        for (String table : surveyL2Tables) {
            String template = "" +
                    "SELECT t.* from {db}.{table} AS t " +
                    "LEFT JOIN {db}.survey_question AS q on q.id = t.q_id " +
                    "LEFT JOIN {db}.survey AS s ON q.s_id = s.id " +
                    "WHERE s.org_id = {orgId}" +
                    ";\n";
            String q = buildTableQuery(template, table, orgId);
            records.addAll(backupTable(orgId, table, q));
        }

        for (String table : rootTables) {
            String query = String.format("SELECT * FROM `%s`.`%s` WHERE org_id = %d", db, table, orgId);
            records.addAll(backupTable(orgId, table, query));
        }

        // add department manually
        {
            String query = String.format("SELECT * FROM `%s`.`%s` WHERE org_id = %d and pid != 0", db, "department", orgId);
            records.addAll(backupTable(orgId, "department", query));
        }

        // add event_result_rule_relation
        {
            String query = String.format("SELECT * FROM `%s`.`%s` WHERE rule_id in (select id from cem_platform.event_monitor_rules where org_id = %d) ", db, "event_result_rule_relation", orgId);
            records.addAll(backupTable(orgId, "event_result_rule_relation", query));
        }

        {
            String query = String.format("SELECT fe.* FROM cem_platform.bi_dataset_field_expression as fe LEFT JOIN cem_platform.bi_dataset_field AS f ON f.id = fe.field_id LEFT JOIN cem_platform.bi_dataset AS ds ON ds.id = f.dataset_id WHERE ds.org_id = %d", orgId);
            records.addAll(backupTable(orgId, "bi_dataset_field_expression", query));
        }

        for (String table : journeyTables) {
            String template = "" +
                    "SELECT * from {db}.{table} " +
                    "WHERE org_id = {orgId}" +
                    ";\n";
            String q = buildTableQuery(template, table, orgId);
            records.addAll(backupTable(orgId, table, q));

            String publish_table = table + "_publish";
            template = "" +
                    "SELECT * from {db}.{table} " +
                    "WHERE org_id = {orgId}" +
                    ";\n";
            q = buildTableQuery(template, publish_table, orgId);
            records.addAll(backupTable(orgId, publish_table, q));
        }

        return records;
    }

    /**
     * 从恢复点文件读取
     */
    private List<BackupRecordDto> readRecoveryPointFromFile(String file) throws IOException {
        Path p = Path.of(String.format("%s/%s", rootPath, file));

        if (!Files.exists(p)) {
            throw new RuntimeException("backup not exist");
        }

        return objectMapper.readerForListOf(BackupRecordDto.class).readValue(Files.readString(p));
    }

    /**
     * 比较两个恢复点文件 （json）
     */
    @SneakyThrows
    public BackupDiffResultDto diffRecoveryPoint(List<BackupRecordDto> s1, List<BackupRecordDto> s2) {
        BackupDiffResultDto diffResultDto = new BackupDiffResultDto();
        diffResultDto.setCount1(s1.size());
        diffResultDto.setCount2(s2.size());

        // 索引缓存
        Map<String, BackupRecordDto> is1 = s1.stream().collect(Collectors.toMap( x -> x.indexKey(), x -> x));
        Map<String, BackupRecordDto> is2 = s2.stream().collect(Collectors.toMap( x -> x.indexKey(), x -> x));

        // 遍历s1, 找出更新的或者相同，删除的
        for (int i = 0; i < s1.size(); i++) {
            BackupRecordDto r1 = s1.get(i);
            String key = r1.indexKey();
            if (is2.containsKey(key)) {
                BackupRecordDto r2 = is2.get(key);
                if (!r1.getC().equals(r2.getC())) {
                    diffResultDto.addUpdate(r1);
                }
            } else {
                diffResultDto.addDel(r1);
            }
        }

        // s2 出现 s1并未出现，说明是新增
        for (int i = 0; i < s2.size(); i++) {
            BackupRecordDto r2 = s2.get(i);
            String key = r2.indexKey();
            if (!is1.containsKey(key)) {
                diffResultDto.addNew(r2);
            }
        }

        return diffResultDto;
    }

    /**
     * 设置是否开启
     */
    public void toggle(BackupAccount account, Boolean enable){
        account.setEnabled(enable);
        repository.save(account);
    }

    public void purgeDataByOrg(Long orgId) {
        Session session = em.unwrap(Session.class);
        session.doReturningWork(new ReturningWork<Boolean>() {
            @SneakyThrows
            @Override
            public Boolean execute(final Connection connection) {

                // 禁用外键约束
                try (Statement stmt = connection.createStatement()) {
                    stmt.execute("SET foreign_key_checks = 0");
                    log.info("Foreign key checks disabled");
                }

                try {
                    // 这里列出所有要执行的删除 SQL 查询
                    String[] deleteSqls = {
                            "DELETE FROM cem_platform.survey_question WHERE s_id IN (SELECT id FROM cem_platform.survey WHERE org_id = %d)",
                            "DELETE FROM cem_platform.survey_logic WHERE s_id IN (SELECT id FROM cem_platform.survey WHERE org_id = %d)",
                            "DELETE FROM cem_platform.survey_response WHERE s_id IN (SELECT id FROM cem_platform.survey WHERE org_id = %d)",
                            "DELETE FROM cem_platform.survey_response_cell WHERE s_id IN (SELECT id FROM cem_platform.survey WHERE org_id = %d)",
                            "DELETE FROM cem_platform.event_result_rule_relation WHERE rule_id IN (SELECT id FROM cem_platform.event_result WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_dataset_field WHERE dataset_id IN (SELECT id FROM cem_platform.bi_dataset WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_dataset_join WHERE dataset_id IN (SELECT id FROM cem_platform.bi_dataset WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_dataset WHERE org_id = %d",
                            "DELETE FROM cem_platform.bi_chart_dimension WHERE chart_id IN (SELECT id FROM cem_platform.bi_chart WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_chart_measure WHERE chart_id IN (SELECT id FROM cem_platform.bi_chart WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_chart_filter WHERE chart_id IN (SELECT id FROM cem_platform.bi_chart WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_chart_drill WHERE chart_id IN (SELECT id FROM cem_platform.bi_chart WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_chart WHERE org_id = %d",
                            "DELETE FROM cem_platform.bi_dashboard_filter WHERE dashboard_id IN (SELECT id FROM cem_platform.bi_dashboard WHERE org_id = %d)",
                            "DELETE FROM cem_platform.bi_dashboard WHERE org_id = %d",
                            "DELETE FROM cem_platform.survey WHERE org_id = %d",
                            "DELETE FROM cem_platform.survey_group WHERE org_id = %d",
                            "DELETE FROM cem_platform.department WHERE org_id = %d AND pid != 0",
                            "DELETE FROM cem_platform.event_monitor_rules WHERE org_id = %d",
                            "DELETE FROM cem_platform.event_result WHERE org_id = %d",
                            "DELETE FROM cem_platform.event_result_group WHERE org_id = %d",
                            "DELETE FROM cem_platform.event_result_group_query WHERE org_id = %d",
                            "DELETE FROM cem_platform.journey WHERE org_id = %d",
                            "DELETE FROM cem_platform.journey_map WHERE org_id = %d",
                            "DELETE FROM cem_platform.journey_component WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_event_stat WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_persona WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_linker WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_curve WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_textbox WHERE org_id = %d",
                            "DELETE FROM cem_platform.experience_indicator WHERE org_id = %d",
                            "DELETE FROM cem_platform.journey_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.journey_component_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_event_stat_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_persona_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_linker_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_curve_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.element_textbox_publish WHERE org_id = %d",
                            "DELETE FROM cem_platform.experience_indicator_publish WHERE org_id = %d"
                    };

                    try (Statement stmt = connection.createStatement()) {
                        // 批量执行每个删除语句
                        for (String sql : deleteSqls) {
                            String finalSql = String.format(sql, orgId);  // 拼接 SQL 语句
                            log.info("Executing SQL: {}", finalSql);
                            stmt.executeUpdate(finalSql);  // 执行删除操作
                        }
                    }

                } finally {
                    // 重新启用外键约束
                    try (Statement stmt = connection.createStatement()) {
                        stmt.execute("SET foreign_key_checks = 1");
                        log.info("Foreign key checks re-enabled");
                    }
                }

                return true;
            }
        });
    }


    public void cloneAccount(CloneAccountDto cloneAccountDto){

        Optional<TemplateAccount> templateAccount = templateAccountRepository.findFirstByIsDefaultTrue();
        if (templateAccount.isEmpty()) {
            throw new BadRequestException("Template Account Not found");
        }
        Long sourceOrgId = templateAccount.get().getOrganization().getId();
        Long targetOrgId = cloneAccountDto.getTargetOrgId();
        if (targetOrgId == null || targetOrgId.equals(sourceOrgId)) {
            throw new BadRequestException("Cannot clone it self");
        }
        Optional<XmPlusUser> xmplusUser = userRepository.findFirstByOrganization_IdAndIsAdminOrderByIdDesc(targetOrgId, 1);
        if (xmplusUser.isEmpty()) {
            throw new BadRequestException("Cannot clone it self");
        }
        Long userId = xmplusUser.get().getId();
        BackupDepartment sourceRootDep = getRootDepartment(sourceOrgId);
        BackupDepartment targetRootDep = getRootDepartment(targetOrgId);
        Long targetRoleId = getRootRole(targetOrgId);

        log.info("clone from {} to {} root depart {} to {} target role {}", sourceOrgId, targetOrgId, sourceRootDep.id, targetRootDep.id, targetRoleId);

        try {
            List<BackupRecordDto> records = backupInsertAllByOrg(sourceOrgId);
            log.info("total clone records {}", records.size());

            // do the real clone for each record
            HashMap<String, HashMap<Long, Long>> tableIdMap = new HashMap<>();
            for (int i = 0; i < records.size(); i++) {
                BackupRecordDto record = records.get(i);
                String table = record.getT();
                Long _id = record.getId();

                if (!tableIdMap.containsKey(table)) {
                    tableIdMap.put(table, new HashMap<>());
                }

                if (!tableIdMap.get(table).containsKey(_id)) {
                    Long nextId = generator.nextId();
                    tableIdMap.get(table).put(_id, nextId);
                }
            }

//            records = records.subList(0,100);
            applyTemplateRecords(sourceOrgId, targetOrgId, sourceRootDep, targetRootDep, xmplusUser.get(), targetRoleId, tableIdMap, records);
        } catch (Exception ex) {
            log.error("backup failed due to exception {}", ex);
            throw new BadRequestException(ex.getMessage());
        }
    }


    /**
     * 模版数据应用到帐号
     * @param sourceOrgId
     * @param targetOrgId
     * @param sourceDep
     * @param targetDep
     * @param targetUser
     * @param roleId
     * @param tableIdMap
     * @param records
     */
    private void applyTemplateRecords(Long sourceOrgId, Long targetOrgId, BackupDepartment sourceDep, BackupDepartment targetDep, XmPlusUser targetUser, Long roleId, HashMap<String, HashMap<Long, Long>> tableIdMap, List<BackupRecordDto> records) {
        Session session = em.unwrap(Session.class);
        session.doReturningWork(new ReturningWork<Boolean>() {
            @SneakyThrows
            @Override
            public Boolean execute(final Connection connection) {
                // 禁用外键约束
                try (Statement stmt = connection.createStatement()) {
                    stmt.execute("SET foreign_key_checks = 0");
                }

                try {
                    // 遍历 records
                    for (int i = 0; i < records.size(); i++) {
                        BackupRecordDto record = records.get(i);

                        // 获取原始数据（raw 字段）
                        HashMap<String, Object> raw = record.getRaw();

                        String table = record.getT();
                        Long id = record.getId();
                        // 获取原始 SQL 查询
                        String template = "{insert_sql}";
                        String q = buildTableQuery(template, table, sourceOrgId, Map.of("id", record.getId(), "insert_sql", record.getS()));
                        log.debug("execute sql {}", q);

                        String new_sql = "";
                        boolean converted = true;
                        raw.put("id", tableIdMap.get(table).get(id).toString());

                        // 根据表类型直接修改 raw 字典
                        switch (table) {
                            case "department": {
                                raw.put("org_id", targetOrgId.toString());
                                Long pid = Long.valueOf(raw.get("pid").toString());

                                String parents_list = raw.get("parents_list").toString();
                                parents_list = parents_list.replace(sourceDep.id.toString(), targetDep.id.toString());
                                parents_list = parents_list.replace(id.toString(), tableIdMap.get(table).get(id).toString());
                                raw.put("parents_list", parents_list);

                                String parents_name_list = raw.get("parents_name_list").toString();
                                parents_name_list = parents_name_list.replace(sourceDep.title, targetDep.title);
                                raw.put("parents_name_list", parents_name_list);

                                Long targetPid = tableIdMap.get("department").get(pid);
                                if (targetPid == null) {
                                    targetPid = targetDep.id;
                                }
                                raw.put("pid", targetPid.toString());
                            }
                            break;
                            case "bi_dataset": {
                                raw.put("org_id", targetOrgId.toString());
                                if (raw.get("type").equals(4)) {
                                    // if survey
                                    Long s_id = Long.valueOf(raw.get("table_name").toString());
                                    raw.put("table_name", tableIdMap.get("survey").get(s_id).toString());
                                }
                            }
                            break;
                            case "bi_dataset_join": {
                                Long dataset_id = Long.valueOf(raw.get("dataset_id").toString());
                                Long target_dataset_id = Long.valueOf(raw.get("target_dataset_id").toString());
                                raw.put("dataset_id", tableIdMap.get("bi_dataset").get(dataset_id).toString());
                                raw.put("target_dataset_id", tableIdMap.get("bi_dataset").get(target_dataset_id).toString());

                                if (raw.containsKey("drive_dataset_id") && raw.get("drive_dataset_id") != null) {
                                    Long drive_dataset_id = Long.valueOf(raw.get("drive_dataset_id").toString());
                                    raw.put("drive_dataset_id", tableIdMap.get("bi_dataset").get(drive_dataset_id).toString());
                                }

                                ArrayList<Long> target_field_ids = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("target_field_ids", "[]").toString()), ArrayList.class);
                                if (target_field_ids.size() > 0) {
                                    ArrayList<Long> new_target_field_ids = new ArrayList<>();
                                    target_field_ids.forEach(x -> {
                                        new_target_field_ids.add(tableIdMap.get("bi_dataset_field").get(x));
                                    });
                                    raw.put("target_field_ids", objectMapper.writeValueAsString(new_target_field_ids));
                                }
                            }
                            break;
                            case "bi_dataset_field_expression": {
                                Long field_id = Long.valueOf(raw.get("field_id").toString());
                                raw.put("field_id", tableIdMap.get("bi_dataset_field").get(field_id).toString());
                            }
                            break;

                            case "bi_dataset_field": {
                                String raw_sql = "";
                                if (raw.containsKey("raw_sql") && raw.get("raw_sql") != null) {
                                    raw_sql = raw.get("raw_sql").toString();
                                }
                                String origin_name = raw.get("origin_name").toString();
                                String origin_id = raw.get("origin_id").toString();
                                String data_type = raw.get("data_type").toString();
                                String[] ids = origin_id.split("_");
                                Long dataset_id = Long.valueOf(raw.get("dataset_id").toString());
                                if (tableIdMap.get("bi_dataset").containsKey(dataset_id)) {
                                    raw.put("dataset_id", tableIdMap.get("bi_dataset").get(dataset_id).toString());
                                }

                                if (raw.containsKey("exp_reference_field_id") && raw.get("exp_reference_field_id") != null) {
                                    Long exp_reference_field_id = Long.valueOf(raw.get("exp_reference_field_id").toString());
                                    raw.put("exp_reference_field_id", tableIdMap.get("bi_dataset_field").get(exp_reference_field_id).toString());
                                }

                                if (origin_name.startsWith("Q") && ids.length > 0 && ids[0].matches("-?\\d+")) {
                                    if (ids.length == 2) {
                                        // q1_q2, pattern ,多选题专用
                                        Long qid = Long.valueOf(ids[0].toString());
                                        Long target_qid = tableIdMap.get("survey_question").get(qid);
                                        if (ids[1].matches("\\d+")) {
                                            // q1_item1
                                            // q1_2b
                                            Long item_id = Long.valueOf(ids[1].toString());
                                            if (item_id > 10000L) {
                                                Long target_item_id = tableIdMap.get("survey_question_item").get(item_id);
                                                origin_id = String.format("%s_%s", target_qid, target_item_id);
                                                raw.put("origin_id", origin_id);
                                            } else {
                                                origin_id = origin_id.replace(qid.toString(), target_qid.toString());
                                                raw.put("origin_id", origin_id);
                                                if (raw_sql.length() > 0) {
                                                    raw_sql = raw_sql.replace(qid.toString(), target_qid.toString());
                                                    raw.put("raw_sql", raw_sql);
                                                }
                                            }
                                        } else {
                                            // q1_npstype
                                            origin_id = origin_id.replace(qid.toString(), target_qid.toString());
                                            raw.put("origin_id", origin_id);
                                            if (raw_sql.length() > 0) {
                                                raw_sql = raw_sql.replace(qid.toString(), target_qid.toString());
                                                raw.put("raw_sql", raw_sql);
                                            }
                                        }
                                    } else {
                                        for (int i1 = 0; i1 < ids.length; i1++) {
                                            String i11 = ids[i1];
                                            if (i11.matches("\\d+")) {
                                                Long _id = Long.valueOf(i11);
                                                if (tableIdMap.get("survey_question").containsKey(_id)) {
                                                    String target_id = tableIdMap.get("survey_question").get(_id).toString();
                                                    origin_id = origin_id.replace(_id.toString(), target_id);
                                                    raw.put("origin_id", origin_id);
                                                    if (raw_sql.length() > 0) {
                                                        raw_sql = raw_sql.replace(_id.toString(), target_id);
                                                        raw.put("raw_sql", raw_sql);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                }
                                if (origin_id.startsWith("f_") && ids.length > 0) {
                                    Long source_id = Long.valueOf(ids[1]);
                                    if (tableIdMap.get("bi_dataset_field").containsKey(source_id)) {
                                        String target_id = tableIdMap.get("bi_dataset_field").get(source_id).toString();
                                        origin_id = origin_id.replace(source_id.toString(), target_id);
                                        raw.put("origin_id", origin_id);
                                    }
                                }
                            }
                            break;
                            case "bi_dashboard":
                                // bi_dataset_field", "bi_dataset_join
                                //("bi_chart", "chart_id", List.of("bi_chart_drill", "bi_chart_filter", "bi_chart_measure", "bi_chart_dimension"))
                            {
                                Long dashboard_type = Long.valueOf(raw.get("type").toString());
                                if (dashboard_type == 2) {
                                    // skip 工作台 dashboard
                                    continue;
                                }
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                                if (raw.containsKey("parent_id") && raw.get("parent_id") != null) {
                                    Long parent_id = Long.valueOf(raw.get("parent_id").toString());
                                    if (parent_id != 0L && tableIdMap.get(table).containsKey(parent_id)) {
                                        raw.put("parent_id", tableIdMap.get(table).get(parent_id).toString());
                                    }
                                }
                                String layout = raw.getOrDefault("layout", "[]").toString();
                                JsonNode jsonNode = objectMapper.readTree((String) layout);
                                ArrayList<Map<String, Object>> layoutJson = objectMapper.convertValue(jsonNode, ArrayList.class);
                                layoutJson.forEach(item -> {
                                    if (item.get("category").equals("CHART") || item.get("category").equals("CONTROL")) {
                                        Long chart_id = Long.valueOf(item.get("i").toString());
                                        item.put("i", tableIdMap.get("bi_chart").get(chart_id));
                                    }
                                    if (item.get("category").equals("FILTER")) {
                                        Long filter_id = Long.valueOf(item.get("i").toString());
                                        item.put("i", tableIdMap.get("bi_dashboard_filter").get(filter_id));
                                    }
                                });
                                raw.put("layout", objectMapper.writeValueAsString(layoutJson));
                            }
                            break;
                            case "bi_dashboard_filter": {
                                Long dashboard_id = Long.valueOf(raw.get("dashboard_id").toString());
                                Long dataset_id = Long.valueOf(raw.get("dataset_id").toString());

                                raw.put("dataset_id", tableIdMap.get("bi_dataset").get(dataset_id).toString());
                                raw.put("dashboard_id", tableIdMap.get("bi_dashboard").get(dashboard_id).toString());

                                String[] field_ids = new String[0];
                                if (raw.containsKey("field_ids") && raw.get("field_ids") != null) {
                                    field_ids = raw.getOrDefault("field_ids", "").toString().split(",");
                                }
                                ArrayList<String> new_field_ids = new ArrayList();
                                for (String field_id : field_ids) {
                                    new_field_ids.add(tableIdMap.get("bi_dataset_field").get(Long.valueOf(field_id)).toString());
                                }
                                raw.put("field_ids", String.join(",", new_field_ids));

                                String linkage_fields = raw.getOrDefault("linkage_fields", "[]").toString();
                                JsonNode jsonNode = objectMapper.readTree((String) linkage_fields);
                                ArrayList<Map<String, Object>> linkage_fields_json = objectMapper.convertValue(jsonNode, ArrayList.class);
                                linkage_fields_json.forEach(item -> {
                                    if (item.containsKey("datasetId")) {
                                        Long l_dataset_id = Long.valueOf(item.get("datasetId").toString());
                                        item.put("datasetId", tableIdMap.get("bi_dataset").get(l_dataset_id));
                                    }
                                    if (item.containsKey("fieldId")) {
                                        Long l_field_id = Long.valueOf(item.get("fieldId").toString());
                                        item.put("fieldId", tableIdMap.get("bi_dataset_field").get(l_field_id));
                                    }
                                });
                            }
                            break;
                            case "bi_chart":
                            case "bi_chart_dimension":
                            case "bi_chart_drill":
                            case "bi_chart_filter":
                            case "bi_chart_measure": {
                                if (table == "bi_chart") {
                                    Map<String, Object> setting = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("setting", "[]").toString()), HashMap.class);
                                    if (setting.containsKey("linkageCharts")) {
                                        ArrayList<Long> linkage_charts = (ArrayList<Long>) setting.get("linkageCharts");
                                        ArrayList<Long> new_linkage_charts = new ArrayList<>();
                                        linkage_charts.forEach(x -> {
                                            if (tableIdMap.get("bi_chart").containsKey(x)) {
                                                new_linkage_charts.add(tableIdMap.get("bi_chart").get(x));
                                            }
                                        });
                                        setting.put("linkageCharts", new_linkage_charts);
                                        raw.put("setting", objectMapper.writeValueAsString(setting));
                                    }

                                    if (setting.containsKey("markLine") && setting.get("markLine") != null) {
                                        ArrayList<Map<String, Object>> mark_line = (ArrayList<Map<String, Object>>) setting.get("markLine");
                                        mark_line.forEach(x -> {
                                            if (x.get("target") != null) {
                                                Long target = Long.valueOf(x.get("target").toString());
                                                if (tableIdMap.get("bi_chart_measure").containsKey(target)) {
                                                    x.put("target", tableIdMap.get("bi_chart_measure").get(target));
                                                }
                                            }
                                        });
                                        setting.put("markLine", mark_line);
                                        raw.put("setting", objectMapper.writeValueAsString(setting));
                                    }

                                    if (setting.containsKey("extraColorTable") && setting.get("extraColorTable") != null) {
                                        Map<String, Object> extra_color_table = (Map<String, Object>) setting.get("extraColorTable");
                                        Map<String, Object> new_extra_color_table = new HashMap<>();
                                        extra_color_table.forEach((key, value) -> {
                                            if (key.startsWith("m_")) {
                                                Long mid = Long.valueOf(key.split("_")[1]);
                                                if (tableIdMap.get("bi_chart_measure").containsKey(mid)) {
                                                    new_extra_color_table.put(String.format("m_%s", tableIdMap.get("bi_chart_measure").get(mid)), value);
                                                }
                                            } else if (key.startsWith("d_")) {
                                                Long mid = Long.valueOf(key.split("_")[1]);
                                                if (tableIdMap.get("bi_chart_dimension").containsKey(mid)) {
                                                    new_extra_color_table.put(String.format("d_%s", tableIdMap.get("bi_chart_dimension").get(mid)), value);
                                                }
                                            } else {
                                                new_extra_color_table.put(key, value);
                                            }
                                        });
                                        setting.put("extraColorTable", new_extra_color_table);
                                        raw.put("setting", objectMapper.writeValueAsString(setting));
                                    }
                                }
                                if (raw.containsKey("org_id") && raw.get("org_id") != null) {
                                    raw.put("org_id", targetOrgId.toString());
                                }
                                if (raw.containsKey("dataset_id") && raw.get("dataset_id") != null) {
                                    Long dataset_id = Long.valueOf(raw.get("dataset_id").toString());
                                    raw.put("dataset_id", tableIdMap.get("bi_dataset").get(dataset_id));
                                }
                                if (raw.containsKey("field_id") && raw.get("field_id") != null) {
                                    Long field_id = Long.valueOf(raw.get("field_id").toString());
                                    raw.put("field_id", tableIdMap.get("bi_dataset_field").get(field_id));
                                }
                                if (raw.containsKey("chart_id") && raw.get("chart_id") != null) {
                                    Long chart_id = Long.valueOf(raw.get("chart_id").toString());
                                    raw.put("chart_id", tableIdMap.get("bi_chart").get(chart_id));
                                }
                                if (table == "bi_chart_measure" && raw.containsKey("config") && raw.get("config") != null) {
                                    Map<String, Object> config = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("config", "{}").toString()), HashMap.class);
                                    if (config != null && config.containsKey("relatedDatasetFieldId") && config.get("relatedDatasetFieldId") != null) {
                                        Long related_dataset_field_id = Long.valueOf(config.get("relatedDatasetFieldId").toString());
                                        config.put("relatedDatasetFieldId", tableIdMap.get("bi_dataset_field").get(related_dataset_field_id));
                                        raw.put("config", objectMapper.writeValueAsString(config));
                                    }
                                }
                            }
                            break;
                            case "journey_map": {
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                                raw.put("modify_user_id", targetUser.getId().toString());
                            }
                            break;

                            case "journey":
                            case "journey_component":
                            case "element_event_stat":
                            case "element_persona":
                            case "element_linker":
                            case "element_curve":
                            case "element_textbox":
                            case "experience_indicator":
                            case "journey_publish":
                            case "journey_component_publish":
                            case "element_event_stat_publish":
                            case "element_persona_publish":
                            case "element_linker_publish":
                            case "element_curve_publish":
                            case "element_textbox_publish":
                            case "experience_indicator_publish": {
                                String journey_component_table = "journey_component";
                                String journey_table = "journey";
                                if (table.contains("_publish")) {
                                    journey_component_table = "journey_component_publish";
                                    journey_table = "journey_publish";
                                }
                                raw.put("org_id", targetOrgId.toString());
                                if (raw.containsKey("journey_map_id")) {
                                    Long journey_map_id = Long.valueOf(raw.get("journey_map_id").toString());
                                    if (tableIdMap.get("journey_map").containsKey(journey_map_id)) {
                                        raw.put("journey_map_id", tableIdMap.get("journey_map").get(journey_map_id).toString());
                                    }
                                }
                                if (raw.containsKey("component_id")) {
                                    Long component_id = Long.valueOf(raw.get("component_id").toString());
                                    if (tableIdMap.get(journey_component_table).containsKey(component_id) && tableIdMap.get(journey_component_table).get(component_id) != null) {
                                        raw.put("component_id", tableIdMap.get(journey_component_table).get(component_id).toString());
                                    }
                                }
                                if (raw.containsKey("journey_id") && raw.get("journey_id") != null) {
                                    Long journey_id = Long.valueOf(raw.get("journey_id").toString());
                                    if (tableIdMap.get(journey_table).containsKey(journey_id)) {
                                        raw.put("journey_id", tableIdMap.get(journey_table).get(journey_id).toString());
                                    }
                                }
                                if (raw.containsKey("parent_id") && raw.get("parent_id") != null) {
                                    Long parent_id = Long.valueOf(raw.get("parent_id").toString());
                                    if (parent_id != 0L && tableIdMap.get(table).containsKey(parent_id)) {
                                        raw.put("parent_id", tableIdMap.get(table).get(parent_id).toString());
                                    }
                                }

                                if (table.contains("experience_indicator")) {
                                    //sids, qids
                                    ArrayList<Long> sids = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("sids", "[]").toString()), ArrayList.class);
                                    ArrayList<Long> qids = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("qids", "[]").toString()), ArrayList.class);
                                    if (sids.size() > 0) {
                                        ArrayList<Long> new_sids = new ArrayList<>();
                                        sids.forEach(x -> {
                                            new_sids.add(tableIdMap.get("survey").get(x));
                                        });
                                        raw.put("sids", objectMapper.writeValueAsString(new_sids));
                                    }
                                    if (qids.size() > 0) {
                                        ArrayList<Long> new_qids = new ArrayList<>();
                                        qids.forEach(x -> {
                                            new_qids.add(tableIdMap.get("survey_question").get(x));
                                        });
                                        raw.put("qids", objectMapper.writeValueAsString(new_qids));
                                    }
                                }
                            }
                            break;
                            case "event_monitor_rules": {
                                Long sid = Long.valueOf(raw.get("s_id").toString());
                                String receiver = String.format("[{\"roleIds\":[%s],\"roleId\":null,\"userIds\":[],\"notifyMoment\":\"IMMEDIATE\",\"delayInterval\":null,\"delayUnit\":null,\"delayCondition\":null,\"notifyChannel\":[\"EMAIL\"],\"level\":null}]", roleId);
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("s_id", tableIdMap.get("survey").get(sid).toString());
                                raw.put("user_id", targetUser.getId().toString());
                                raw.put("receiver", receiver);
                            }
                            break;
                            case "event_result_group": {
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                            }
                            break;
                            case "event_result": {
                                Long sid = Long.valueOf(raw.get("survey_id").toString());
                                Long rid = Long.valueOf(raw.get("response_id").toString());
                                Long department_id = Long.valueOf(raw.get("department_id").toString());

                                raw.put("org_id", targetOrgId.toString());
                                raw.put("survey_id", tableIdMap.get("survey").get(sid).toString());
                                raw.put("department_id", tableIdMap.get("department").getOrDefault(department_id, targetDep.id).toString());
                                raw.put("response_id", tableIdMap.get("survey_response").get(rid).toString());
                                raw.put("customer_id", null);

                                // detail data
                                ArrayList<Map<String, Object>> warnings = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("warnings", "[]").toString()), ArrayList.class);
                                warnings.forEach(x -> {
                                    Long response_id = Long.valueOf(x.get("responseId").toString());
                                    Long rule_id = Long.valueOf(x.get("ruleId").toString());
                                    x.put("responseId", tableIdMap.get("survey_response").get(response_id));
                                    x.put("ruleId", tableIdMap.get("event_monitor_rules").get(rule_id));
                                });
                                raw.put("warnings", objectMapper.writeValueAsString(warnings));

                                ArrayList<Map<String, Object>> questions = objectMapper.convertValue(objectMapper.readTree(raw.getOrDefault("questions", "[]").toString()), ArrayList.class);
                                questions.forEach(x -> {
                                    Long question_id = Long.valueOf(x.get("questionId").toString());
                                    x.put("questionId", tableIdMap.get("survey_question").get(question_id));
                                    x.put("eventMonitorRules", null);
                                });
                                raw.put("questions", objectMapper.writeValueAsString(questions));
                            }
                            break;
                            case "event_result_rule_relation": {
                                Long event_id = Long.valueOf(raw.get("event_id").toString());
                                Long response_id = Long.valueOf(raw.get("response_id").toString());
                                Long rule_id = Long.valueOf(raw.get("rule_id").toString());
                                raw.put("event_id", tableIdMap.get("event_result").get(event_id).toString());
                                raw.put("response_id", tableIdMap.get("survey_response").get(response_id).toString());
                                raw.put("rule_id", tableIdMap.get("event_monitor_rules").get(rule_id).toString());
                            }
                            break;
                            case "event_result_group_query": {
                                Long group_id = Long.valueOf(raw.get("group_id").toString());
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                                raw.put("group_id", tableIdMap.get("event_result_group").get(group_id).toString());

                                String property = raw.get("property").toString();
                                JsonNode jsonNode = objectMapper.readTree((String) property);
                                Map<String, Object> propertyJson = objectMapper.convertValue(jsonNode, Map.class);
                                String propertyName = propertyJson.get("propertyName").toString();
                                String queryValue = propertyJson.get("queryValue").toString();
                                switch (propertyName) {
                                    case "id":
                                        propertyJson.put("queryValue", tableIdMap.get("event_monitor_rules").get(Long.valueOf(queryValue)).toString());
                                        break;
                                    case "survey_id":
                                        propertyJson.put("queryValue", tableIdMap.get("survey").get(Long.valueOf(queryValue)).toString());
                                        break;
                                    default:
                                        break;
                                }
                                raw.put("property", objectMapper.writeValueAsString(propertyJson));
                            }
                            break;
                            case "survey_group": {
                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                                raw.put("editor_id", targetUser.getId().toString());
                            }
                            break;
                            case "survey": {
                                if (raw.get("user_id") == null) {
                                    log.warn("skip invalid survey data missing user_id");
                                    continue;
                                }

                                raw.put("org_id", targetOrgId.toString());
                                raw.put("user_id", targetUser.getId().toString());
                                raw.put("editor_id", targetUser.getId().toString());

                                if (raw.containsKey("group_id") && raw.get("group_id") != null) {
                                    Long group_id = Long.valueOf(raw.get("group_id").toString());
                                    if (tableIdMap.get("survey_group").containsKey(group_id)) {
                                        raw.put("group_id", tableIdMap.get("survey_group").get(group_id).toString());
                                    }
                                }
                            }
                            break;
                            case "survey_question":
                            case "survey_logic": {
                                Long s_id = Long.valueOf(raw.get("s_id").toString());
                                if (s_id < 10000L) {
                                    log.warn("skip invalid due to legacy id {}", s_id);
                                } else {
                                    raw.put("s_id", tableIdMap.get("survey").get(s_id).toString());
                                }
                            }
                            break;
                            case "survey_question_item":
                            case "survey_question_column": {
                                Long q_id = Long.valueOf(raw.get("q_id").toString());
                                raw.put("q_id", tableIdMap.get("survey_question").get(q_id).toString());
                            }
                            break;
                            case "survey_response": {
                                raw.put("org_id", targetOrgId.toString());
                                Long s_id = Long.valueOf(raw.get("s_id").toString());
                                raw.put("s_id", tableIdMap.get("survey").get(s_id).toString());
                                break;
                            }
                            case "survey_response_cell": {
                                Long s_id = Long.valueOf(raw.get("s_id").toString());
                                Long r_id = Long.valueOf(raw.get("r_id").toString());
                                Long q_id = Long.valueOf(raw.get("q_id").toString());
                                raw.put("s_id", tableIdMap.get("survey").get(s_id).toString());
                                raw.put("r_id", tableIdMap.get("survey_response").get(r_id).toString());
                                raw.put("q_id", tableIdMap.get("survey_question").get(q_id).toString());
                                break;
                            }

                            default:
                                converted = false;
                                break;
                        }

                        // 如果数据已成功转换
                        if (converted) {
                            // 构建 SQL 语句
                            StringBuilder sb = new StringBuilder();
                            List<String> cols = new ArrayList<>(raw.keySet());  // 获取所有列名
                            List<String> colsNames = cols.stream().map(x -> String.format("`%s`", x)).collect(Collectors.toList());
                            sb.append(String.format("INSERT INTO `%s`.`%s`(%s) VALUES (", db, table, String.join(", ", colsNames)));

                            StringBuilder values = new StringBuilder();
                            for (int j = 0; j < cols.size(); j++) {
                                String col = cols.get(j);
                                Object val = raw.get(col);

                                if (val != null) {
                                    if (val instanceof Integer || val instanceof Float || val instanceof Double || val instanceof Long || val instanceof Boolean) {
                                        values.append(val);
                                    } else {
                                        String mv = val.toString().replaceAll("'", "\\\\'");
                                        mv = mv.replaceAll("\"", "\\\\\"");
                                        values.append(String.format("'%s'", mv));
                                    }
                                } else {
                                    values.append("NULL");
                                }

                                if (j != cols.size() - 1) {
                                    values.append(",");
                                }
                            }
                            sb.append(values.toString());
                            sb.append(");\n");

                            // 打印生成的 SQL 语句（可选）
                            new_sql = sb.toString();
                            log.debug("Generated new SQL: {}", new_sql);

                            // 执行 SQL
                            try (Statement stmt = connection.createStatement()) {
                                stmt.execute(new_sql);
                            } catch (Exception ex) {
                                log.error("failed to restore new sql");
                                log.error(ex.getMessage());
                                throw ex;
                            }
                        } else {
                            log.warn("skip table {}", table);
                        }
                    }
                } finally {
                    // 确保重新启用外键约束
                    try (Statement stmt = connection.createStatement()) {
                        stmt.execute("SET foreign_key_checks = 1");  // 重新启用外键约束
                    }
                }
                return true;
            }
        });
    }

    /**
     * 开始备份
     */
    public void backupAccount(BackupAccount account){
        if (!account.getEnabled()) {
            throw new BadRequestException("Account Disable");
        }
        if (account.getOrganization() == null) {
            throw new BadRequestException("Account Invalid");
        }
        Long orgId = account.getOrganization().getId();
        log.info("backup for org {}", orgId);

        try {
            Long id = DateTime.now().getTime();
            List<BackupRecordDto> records = backupInsertAllByOrg(orgId);
            String fileName = String.format("%d_%s.json", orgId, id);
            String fullPath = String.format("%s/%s", rootPath, fileName);
            objectMapper.writeValue(new File(fullPath), records);
            account.setFile(fileName);
            repository.save(account);
            logRepository.save(BackupLog.builder()
                    .account(account)
                    .type(BackupLogType.BACKUP)
                    .file(fileName)
                    .success(true)
                    .build());
        } catch (Exception ex) {
            log.error("backup failed due to exception {}", ex);
            throw new BadRequestException(ex.getMessage());
        }
    }

    /**
     * diff结果恢复
     * @param orgId
     * @param diff
     */
    private void recoveryFromDiff(Long orgId, BackupDiffResultDto diff) {
        Session session = em.unwrap(Session.class);
        session.doReturningWork(new ReturningWork<Boolean>() {
            @Override public Boolean execute(final Connection connection) {

                // 更新，先删除后添加
                for (int i = 0; i < diff.getUpdates().size(); i++) {
                    BackupRecordDto ru = diff.getUpdates().get(i);
                    String template = "" +
                            "DELETE FROM {db}.{table} WHERE id = {id};" +
                            "";
                    String q = buildTableQuery(template, ru.getT(), orgId, Map.of("id", ru.getId()));
                    log.debug("execute sql {}", q);
                    jdbcTemplate.execute(q);

                    log.debug("execute sql {}", ru.getS());
                    jdbcTemplate.execute(ru.getS());
                }

                // 删除的部分需要重新添加
                for (int i = 0; i < diff.getDeletes().size(); i++) {
                    BackupRecordDto du = diff.getDeletes().get(i);
                    String template = "" +
                            "{insert_sql}" +
                            "";
                    String q = buildTableQuery(template, du.getT(), orgId, Map.of("id", du.getId(), "insert_sql", du.getS()));
                    log.debug("execute sql {}", q);
                    jdbcTemplate.execute(q);
                }

                // 新增的部分需要删除
                for (int i = 0; i < diff.getNews().size(); i++) {
                    BackupRecordDto nu = diff.getNews().get(i);
                    String template = "" +
                            "DELETE FROM {db}.{table} WHERE id = {id};\n" +
                            "";
                    String q = buildTableQuery(template, nu.getT(), orgId, Map.of("id", nu.getId(), "insert_sql", nu.getS()));
                    log.debug("execute sql {}", q);
                    jdbcTemplate.execute(q);
                }
                return true;
            }
        });
    }

    /***
     * 还原点恢复
     * @param account
     */
    @Transactional
    public void recoveryAccount(BackupAccount account, boolean is_auto){
        if (!account.getEnabled()) {
            throw new BadRequestException("Account Disable");
        }
        if (account.getOrganization() == null) {
            throw new BadRequestException("Account Invalid");
        }
        if (account.getFile().isEmpty() || account.getId() == null) {
            throw new BadRequestException("Account backup not ready");
        }

        Long orgId = account.getOrganization().getId();
        log.info("start recovery for org {}", orgId);

        try {
            List<BackupRecordDto> s1 = this.readRecoveryPointFromFile(account.getFile());
            List<BackupRecordDto> s2 = this.backupInsertAllByOrg(orgId);

            BackupDiffResultDto diff = this.diffRecoveryPoint(s1, s2);
            recoveryFromDiff(orgId, diff);

            log.info("finished to recovery for org {}", orgId);

            account.setLastBackupTime(DateTime.now());
            repository.save(account);
            String msg = String.format("新增 %d 更新 %d 删除 %d", diff.getNews().size(), diff.getUpdates().size(), diff.getDeletes().size());
            logRepository.save(BackupLog.builder()
                    .account(account)
                    .type(is_auto ? BackupLogType.AUTO_RECOVERY : BackupLogType.MANUAL_RECOVERY)
                    .file(account.getFile())
                    .message(msg)
                    .success(true)
                    .build());
        } catch (Exception ex) {
            log.error("recovery failed due to exception {}", ex);
            logRepository.save(BackupLog.builder()
                    .account(account)
                    .type(is_auto ? BackupLogType.AUTO_RECOVERY : BackupLogType.MANUAL_RECOVERY)
                    .file(account.getFile())
                    .success(false)
                    .message(ex.getMessage())
                    .build());
        }
    }

    @SneakyThrows
    @Scheduled(cron = "0 0 2 * * *")
    @TaskLock(
            key = "auto_recovery",
            seconds = 300
    )
    public void autoRecoveryDaily() {
        log.info("auto recovery task start");
        List<BackupAccount> accounts = repository.findAllByEnabled(true);
        for (int i = 0; i < accounts.size(); i++) {
            BackupAccount account = accounts.get(i);
            try {
                log.info("try recovery for org {}", account.getOrganization().getId());
                recoveryAccount(account, true);
                log.info("recovery for org {} success", account.getOrganization().getId());
            } catch (Exception ex) {
                log.error("recovery for org {} failed due to {}", account.getOrganization().getId(), ex.getMessage());
            }
        }
        log.info("auto recovery task finished total {}", accounts.size());
    }
}
