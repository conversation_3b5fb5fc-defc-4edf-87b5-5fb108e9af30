package org.befun.adminx.service;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.entity.survey.SurveyLink;
import org.befun.adminx.entity.survey.SurveyLinkDto;
import org.befun.adminx.repository.SurveyLinkRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.generator.SysConvert;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.CreateLinkDto;
import org.befun.extension.dto.CreateLinkResponseDto;
import org.befun.extension.property.ShortUrlRootProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class SurveyLinkService extends BaseService<SurveyLink, SurveyLinkDto, SurveyLinkRepository> {

    @Autowired
    private ShortUrlRootProperty shortUrlRootProperty;

    @Autowired
    private SurveyLinkRepository linkRepository;

    public String findUrlById(Long id) {
        Optional<SurveyLink> link = linkRepository.findById(id);
        if (link.isPresent()) {
            return link.get().getUrl();
        }
        return null;
    }

    /**
     * 转换为短链
     *
     * @param url         原始地址
     * @param forceCreate true 每次都创建一个新的短链； false 先检查原始地址是否存在，不存在则创建，否则直接返回已存在的短链
     * @return 短链地址
     */
    public String toShortUrl(String url, boolean forceCreate, Long surveyId, Map<String, Object> params) {
        String root = Optional.ofNullable(shortUrlRootProperty.getRoot())
                .filter(StringUtils::isNotEmpty)
                .orElseThrow(() -> new BadRequestException("shortUrl配置信息为空"));
        SurveyLink surveyLink;
        if (forceCreate) {
            surveyLink = new SurveyLink();
//            surveyLink.setUrl(url);
//            linkRepository.save(surveyLink);
        } else if ((surveyLink = linkRepository.findFirstByUrl(url).orElse(null)) == null) {
            surveyLink = new SurveyLink();
//            surveyLink.setUrl(url);
//            linkRepository.save(surveyLink);
        }
        // 我不理解上面的new 我只是把共同的提取到外面
        surveyLink.setParams(JsonHelper.toJson(params));
        surveyLink.setSurveyId(surveyId);
        surveyLink.setUrl(url);
        linkRepository.save(surveyLink);
        String shortId = SysConvert.toX(surveyLink.getId());
        String shortUrl = String.format("%s/%s", root, shortId);
        log.debug("短链创建成功：originUrl={}, shortUrl={}, id={}", url, shortUrl, surveyLink.getId());
        return shortUrl;
    }

    public CreateLinkResponseDto generateShortUrl(CreateLinkDto dto) {
        String url = dto.getUrl();
        CreateLinkResponseDto response = new CreateLinkResponseDto();

        Optional<SurveyLink> link = linkRepository.findFirstByUrl(url);
        if (!link.isPresent()) {
            SurveyLink newlink = new SurveyLink();
            newlink.setUrl(dto.getUrl());
            linkRepository.save(newlink);
            link = Optional.ofNullable(newlink);
        }
        String resultId = SysConvert.toX(link.get().getId());//将主键id转化为自定义字符串id

        if (StringUtils.isNotEmpty(shortUrlRootProperty.getRoot())) {//对root配置信息判空
            response.setId(resultId);
            response.setUrl(shortUrlRootProperty.getRoot() + "/" + resultId);
        } else throw new BadRequestException("root配置信息为空");
        return response;
    }

    public String encodeUrl(String url) {
        //正则表达式匹配参数中的中文
        Pattern pattern = Pattern.compile("([\u4e00-\u9fa5]+)");
        Matcher matcher = pattern.matcher(url);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {  //匹配有中文，进行编码并替换
            String resultStr = null;
            try {
                resultStr = URLEncoder.encode(matcher.group(1), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            matcher.appendReplacement(sb, resultStr);
        }

        matcher.appendTail(sb);
        String resultUrl = sb.toString();
        return resultUrl;
    }
}
