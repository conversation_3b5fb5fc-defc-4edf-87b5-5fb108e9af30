package org.befun.adminx.service.download;

import cn.hanyi.common.ip.resolver.IpResolverService;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.service.download.dto.DownloadColumnResponse;
import org.befun.adminx.service.download.dto.DownloadColumnResponseGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
public class DownloadResponseParser {

    @Autowired
    private IpResolverService ipResolverService;

    public void buildGroup(ResponseDownloadContext context) {
        DownloadColumnResponseGroup group = new DownloadColumnResponseGroup();
        List<DownloadColumnResponse> columns = group.getColumns();
        context.setResponseGroup(group);
        buildColumn(context, columns, false, false, "id", "id", SurveyResponse::getId);
        buildColumn(context, columns, false, false, "社区任务id", "track_id", SurveyResponse::getTrackId);
        buildColumn(context, columns, false, false, "分享人", "share_id", surveyResponse -> surveyResponse.getAdditionData().get("invite"));
        buildColumn(context, columns, false, false, "微信openId", "openID", SurveyResponse::getOpenid);
        buildColumn(context, columns, true, false, "答卷编号", "QID", SurveyResponse::getSequence);
        buildColumn(context, columns, false, false, "回收类型", "send_type", r -> r.getCollectorMethod().getText());
        buildColumn(context, columns, false, true, "姓名", "name", SurveyResponse::getName);
        buildColumn(context, columns, false, true, "手机号码", "phone", SurveyResponse::getPhone);
        buildColumn(context, columns, false, false, "提交时间", "end_time", r -> DateHelper.formatDateTime(r.getFinishTime()));
        buildColumn(context, columns, false, false, "开始时间", "start_time", r -> DateHelper.formatDateTime(r.getCreateTime()));
        buildColumn(context, columns, false, false, "答题状态", "result_code", r -> r.getStatus().name());
        buildColumn(context, columns, false, false, "审核方式", "audit_type", r -> r.getAuditResult() == null ? null : r.getAuditResult(r.getAuditResult()).getAuditType());
        buildColumn(context, columns, false, false, "积分奖励", "points", r -> r.getAuditResult() == null ? null : r.getAuditResult(r.getAuditResult()).getPoints());
        buildColumn(context, columns, false, false, "审核得分", "audit_score", r -> r.getAuditResult() == null ? null : r.getAuditResult(r.getAuditResult()).getAuditScore());
        buildColumn(context, columns, false, false, "失败规则", "fail_rule", r -> r.getAuditResult() == null ? null : r.getAuditResult(r.getAuditResult()).getFailRule());
        buildColumn(context, columns, false, false, "ip", "ip", SurveyResponse::getIp);
        buildColumn(context, columns, true, false, "答题时长(秒)", "duration_seconds", SurveyResponse::getDurationSeconds);
        buildColumn(context, columns, true, false, "答题总时长(秒)", "duration_total", r -> (r.getFinishTime() != null && r.getCreateTime() != null) ? (r.getFinishTime().getTime() - r.getCreateTime().getTime()) / 1000 : null);
        buildColumn(context, columns, false, false, "国家(ip)", "ip_country", SurveyResponse::getCountry);
        buildColumn(context, columns, false, false, "省份(ip)", "ip_province", SurveyResponse::getProvince);
        buildColumn(context, columns, false, false, "城市(ip)", "ip_city", SurveyResponse::getCity);
        buildColumn(context, columns, false, false, "答题质控", "similarity_check", SurveyResponse::getSimilarityCheck);
        buildColumn(context, columns, false, false, "相似程度", "similarity_percent", SurveyResponse::getSimilarityPercent);
        buildColumn(context, columns, false, false, "相似答题组", "similarity_no", SurveyResponse::getSimilarityNo);
        buildColumn(context, columns, false, true, "外部客户ID", "externalUserId", SurveyResponse::getExternalUserId);
        buildColumn(context, columns, false, true, "外部参数", "external_parameters", r -> {
            if (r.getParameters() != null && r.getParameters().size() > 0) {
                return JsonHelper.toJson(r.getParameters());
            } else {
                return null;
            }
        });
    }

    private void buildColumn(ResponseDownloadContext context, List<DownloadColumnResponse> columns, boolean numberValue, boolean deleteIfEmpty, String label, String code, Function<SurveyResponse, Object> getValue) {
        columns.add(new DownloadColumnResponse(context.getColumnSize(), label, code, numberValue, deleteIfEmpty, getValue));
        context.plusColumnSize();
        context.addCodeHeader(code);
        context.addLabelHeader(label);
    }

}
