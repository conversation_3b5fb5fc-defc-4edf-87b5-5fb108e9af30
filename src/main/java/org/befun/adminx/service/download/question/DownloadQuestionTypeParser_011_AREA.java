package org.befun.adminx.service.download.question;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_011_AREA implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.AREA;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        Arrays.stream(question.getAreaType().getFormat().split("-")).forEach(i -> {
            int itemIndex = index.getAndIncrement();
            group.getColumns().add(new DownloadColumnQuestion_AREA_item(context, group,
                    group.getCode() + "_" + group.getTitle() + "_" + i,
                    group.getCode() + "_" + itemIndex,
                    itemIndex));
        });
        return group;
    }

    public static class DownloadColumnQuestion_AREA_item extends DownloadColumnQuestion {

        private final int itemIndex;

        public DownloadColumnQuestion_AREA_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, String label, String code, int itemIndex) {
            super(context, group, label, code);
            this.itemIndex = itemIndex;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            String s = cell.getStrValue();
            if (StringUtils.isNotEmpty(s)) {
                int index = itemIndex - 1;
                String[] ss = s.split(";");
                if (ss.length > index) {
                    return ss[index];
                }
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
