package org.befun.adminx.service.download.ext;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.SurveySendRecordRepository;
import org.befun.adminx.service.SurveySendRecordService;
import org.befun.adminx.service.download.IDownloadExtHelper;
import org.befun.adminx.service.download.dto.*;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Component
public class DownloadSendRecordHelper implements IDownloadExtHelper<ResponseExportSendRecordFile> {

    @Autowired
    private SurveySendRecordService surveySendRecordService;

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;

    @Override
    public DownloadExtType extType() { return DownloadExtType.survey_send_record; }

    @Override
    public ResponseExportSendRecordFile buildFile(ResponseDownloadContext context) {
        if (context.getTaskType() == TaskType.FOLLOW && surveySendRecordRepository.findFirstByTaskId(context.getTaskId()).isPresent()) {
            return new ResponseExportSendRecordFile(context.getSafeTitle() + "_发送记录" + context.getDownloadType().getSuffix());
        }
        return null;
    }

    @Override
    public void download(ResponseDownloadContext context, ResponseExportSendRecordFile file, List<SurveyResponse> responseList, boolean sequential) {
        if (context.getTaskType() == TaskType.DELIVERY) return;
        buildSendRecordGroup(file);

        List<SurveySendRecord> records = getRecordByTaskId(context.getTaskId());
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(i -> fillRecordValue(file, i));
        }

        file.getSendRecordGroup().getColumns().clear();
    }

    @Override
    public void copyColumns(ResponseDownloadContext context, ResponseExportSendRecordFile file) {

    }


    private void fillRecordValue(ResponseExportSendRecordFile file, SurveySendRecord record) {
        if (record.getTaskId() == null && record.getClientId() != null) {
            return;
        }
        List<Object> row = new ArrayList<>();
        file.getSendRecordGroup().getColumns().forEach(originRow -> {
            row.add(originRow.getLabel(record));
        });
        file.getRows().add(row);
    }

    private List<SurveySendRecord> getRecordByTaskId(Long taskId) {
        return surveySendRecordRepository.findByTaskId(taskId);
    }

    public void buildSendRecordGroup(ResponseExportSendRecordFile fileSendRecord) {
        DownloadColumnSendRecordGroup group = new DownloadColumnSendRecordGroup();
        List<DownloadColumnSendRecord> columns = group.getColumns();
        fileSendRecord.setSendRecordGroup(group);
        buildHeader(fileSendRecord, columns, false, false, "base_line_sid",  SurveySendRecord::getBaseLineSurveyId);
        buildHeader(fileSendRecord, columns, false, false, "base_line_rid", SurveySendRecord::getBaseLineResponseId);
        buildHeader(fileSendRecord, columns, false, false, "task_id", SurveySendRecord::getTaskId);
//        buildHeader(fileSendRecord, columns, false, false, "result_code", SurveySendRecord::getResponseStatus);
//        buildHeader(fileSendRecord, columns, false, false, "points", SurveySendRecord::getPoints);
        buildHeader(fileSendRecord, columns, false, false, "cuid", SurveySendRecord::getCuid);
        buildHeader(fileSendRecord, columns, false, false, "wechat_status", r -> r.getSubscribeStatus() == null ? null : r.getSubscribeStatus().getText());
        buildHeader(fileSendRecord, columns, false, false, "phone_status", r -> r.getPhoneStatus() == true ? "绑定":"未绑定");
//        buildHeader(fileSendRecord, columns, false, false, "phone_number", r -> StringUtils.isNumeric(r.getAccount()) ? r.getAccount() : null);
        buildHeader(fileSendRecord, columns, false, false, "rmdr_sent", SurveySendRecord::getSendCount);
        buildHeader(fileSendRecord, columns, false, false, "rmdr_wechat_sent", SurveySendRecord::getSendWechatCount);
        buildHeader(fileSendRecord, columns, false, false, "rmdr_SMS_sent", SurveySendRecord::getSendMessageCount);
        buildHeader(fileSendRecord, columns, false, false, "send_type_last", r -> r.getSendMethod() == null ? null : r.getSendMethod().getText());
        buildHeader(fileSendRecord, columns, false, false, "send_time_last", r -> DateHelper.formatDateTime(r.getSendTime()));
        buildHeader(fileSendRecord, columns, false, false, "send_status_last", r -> r.getSendStatus() == null ? null : r.getSendStatus().getText());
        buildHeader(fileSendRecord, columns, false, false, "failure_rule_last", SurveySendRecord::getFailMsg);
//        buildHeader(fileSendRecord, columns, false, false, "response_status", r -> r.getResponseStatus() == null ? null : r.getResponseStatus().getText());
    }

    private void buildHeader(ResponseExportSendRecordFile fileSendRecord, List<DownloadColumnSendRecord> columns, boolean numberValue, boolean deleteIfEmpty, String label, Function<SurveySendRecord, Object> getValue) {
        fileSendRecord.getHeaders().add(List.of(label));
        columns.add(new DownloadColumnSendRecord(fileSendRecord.getColumnSize(), label, null, numberValue, deleteIfEmpty, getValue));
        fileSendRecord.plusColumnSize();
    }

    public void buildGroup(ResponseDownloadContext context) {
        if(context.getTaskType() == TaskType.DELIVERY) return;
        DownloadColumnSendRecordGroup group = new DownloadColumnSendRecordGroup();
        List<DownloadColumnSendRecord> columns = group.getColumns();
        Map<Long, SurveySendRecord> sendRecordByTaskIdMap = surveySendRecordService.getSendRecordByTaskId(context.getTaskId());
        if(sendRecordByTaskIdMap != null){
            context.setSendRecordMap(sendRecordByTaskIdMap);
        }
        context.setSendRecordGroup(group);
        buildColumn(context, columns,false,false,"基线问卷ID","qnaire_id_baseline", SurveySendRecord::getBaseLineSurveyId);
        buildColumn(context, columns,false,false,"基线答卷编号","QID_baseline", SurveySendRecord::getBaseLineResponseId);
        buildColumn(context, columns,false,false,"追访问卷ID","qnaire_id", SurveySendRecord::getSurveyId);
        buildColumn(context, columns,false,false,"最后一次发送关注状态","wechat_status", r -> r.getSubscribeStatus() == null ? null : r.getSubscribeStatus().getText());
        buildColumn(context, columns,false,false,"手机号状态","phone_status", r -> r.getPhoneStatus() == true ? "绑定":"未绑定");
//        buildColumn(context, columns,false,false,"手机号码","phone_number", r -> StringUtils.isNumeric(r.getAccount()) ? r.getAccount() : null);
        buildColumn(context, columns,false,false,"发送成功总次数","rmdr_sent", SurveySendRecord::getSendCount);
        buildColumn(context, columns,false,false,"微信发送成功次数","rmdr_wechat_sent", SurveySendRecord::getSendWechatCount);
        buildColumn(context, columns,false,false,"短信发送成功次数","rmdr_SMS_sent", SurveySendRecord::getSendMessageCount);
        buildColumn(context, columns,false,false,"最后一次发送方式","send_type_last", r -> r.getSendMethod() == null ? null : r.getSendMethod().getText());
        buildColumn(context, columns,false,false,"最后一次发送时间","send_time_last", r -> DateHelper.formatDateTime(r.getSendTime()));
        buildColumn(context, columns,false,false,"最后一次发送状态","send_status_last", r -> r.getSendStatus() == null ? null : r.getSendStatus().getText());
        buildColumn(context, columns,false,false,"最后一次失败原因","failure_rule_last", SurveySendRecord::getFailMsg);
    }

    private void buildColumn(ResponseDownloadContext context, List<DownloadColumnSendRecord> columns, boolean numberValue, boolean deleteIfEmpty,  String label, String code,Function<SurveySendRecord, Object> getValue) {
        columns.add(new DownloadColumnSendRecord(context.getColumnSize(), label, code, numberValue, deleteIfEmpty, getValue));
        context.plusColumnSize();
        context.addCodeHeader(code);
        context.addLabelHeader(label);
    }
}
