package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.service.download.IDownloadColumn;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

@Setter
@Getter
@NoArgsConstructor
public class DownloadColumnResponse implements IDownloadColumn<SurveyResponse> {

    private AtomicInteger countValue = new AtomicInteger();

    private int index;
    private String label;
    private String code;
    private boolean numberValue;
    private boolean deleteIfEmpty;
    private Function<SurveyResponse, Object> getValue;

    public DownloadColumnResponse(int index, String label, String code, boolean numberValue, boolean deleteIfEmpty, Function<SurveyResponse, Object> getValue) {
        this.index = index;
        this.label = label;
        this.code = code;
        this.numberValue = numberValue;
        this.deleteIfEmpty = deleteIfEmpty;
        this.getValue = getValue;
    }

    @Override
    public Object getCode(SurveyResponse response) {
        Object v = getLabel(response);
        if (v != null) {
            countValue.getAndIncrement();
        }
        return v;
    }

    @Override
    public Object getLabel(SurveyResponse response) {
        Object v = getValue.apply(response);
        if (v != null && !numberValue && !(v instanceof String)) {
            // 此列数据为字符串，但是实际类型不是字符串，调用一次 toString()
            return v.toString();
        } else {
            return v;
        }
    }
}
