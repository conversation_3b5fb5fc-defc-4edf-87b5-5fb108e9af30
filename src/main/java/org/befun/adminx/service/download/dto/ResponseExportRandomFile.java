package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

@Getter
@Setter
public class ResponseExportRandomFile extends ResponseExportFile {

    private int randomSize = 1;
    private DownloadColumnRandomGroup randomGroup;
    private final LinkedHashMap<Long, Object[]> randomRowMap = new LinkedHashMap<>();

    public ResponseExportRandomFile(String fileName) {
        super(fileName);
    }

    public void plusRandomSize() {
        randomSize++;
    }

}
