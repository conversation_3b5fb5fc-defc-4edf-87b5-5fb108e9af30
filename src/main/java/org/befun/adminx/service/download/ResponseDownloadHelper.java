package org.befun.adminx.service.download;

import cn.hanyi.common.file.storage.FileStorageService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.adminx.constant.DownLoadType;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.constant.survey.DownloadType;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.dto.survey.SimpleResponse;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.repository.SurveyResponseCellRepository;
import org.befun.adminx.repository.SurveyResponseRepository;
import org.befun.adminx.service.ChannelService;
import org.befun.adminx.service.ResponseService;
import org.befun.adminx.service.SurveyService;
import org.befun.adminx.service.download.dto.*;
import org.befun.adminx.service.download.ext.DownloadSendRecordHelper;
import org.befun.adminx.utils.DownloadUtils;
import org.befun.core.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Component
public class ResponseDownloadHelper {

    @Value("${survey.download.batch:500}")
    private int downloadBatch;
    @Autowired
    private SurveyService surveyService;
    @Autowired
    private ChannelService channelService;
    @Autowired
    private ResponseService responseService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;
    @Autowired
    private DownloadResponseParser downloadResponseParser;
    @Autowired
    private DownloadQuestionParser downloadQuestionParser;
    @Autowired
    private DownloadCommunityUserParser downloadCommunityUserParser;
    @Autowired
    private DownloadSendRecordHelper downloadSendRecordHelper;
    @Autowired(required = false)
    private final List<IDownloadExtHelper<?>> downloadExtHelpers = new ArrayList<>();
    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private EntityManager entityManager;

    public void downloadToResponse(HttpServletResponse response, Long surveyId, DownLoadType type, TaskType taskType, Long taskId) {
        List<SimpleResponse> simpleResponseList = responseService.getSurveyResponseIds(surveyId,type);
        List<Long> responseIds = simpleResponseList.stream().map(SimpleResponse::getId).collect(Collectors.toList());
        List<String> openIds = simpleResponseList.stream().map(SimpleResponse::getOpenid).collect(Collectors.toList());
        downloadToResponse(surveyId, responseIds.size(), DownloadType.EXCEL, responseIds, openIds ,response, taskType, taskId);
    }

    public void downloadToResponse(Long surveyId, Integer batchSize, DownloadType downloadType, List<Long> responseIds, List<String> openIds, HttpServletResponse response, TaskType taskType, Long taskId) {
        ResponseDownloadFile zip = download(taskId, surveyId, batchSize, downloadType, responseIds, openIds, taskType);
        DownloadUtils.responseRequest(response, zip.getFileName(), zip.getBytes());
    }

    /**
     * 进度计算
     * total 答卷数
     * 数据查询 50%
     * 数据导出 50%
     */
    public ResponseDownloadFile download(Long taskId, Long surveyId, Integer batchSize, DownloadType downloadType, List<Long> responseIds, List<String> openIds, TaskType taskType) {
        batchSize = batchSize == null ? downloadBatch : batchSize;
        ResponseDownloadContext context = buildContext(taskId, surveyId, downloadType, openIds, taskType);
        if(responseIds.size() > 0){
            if (CollectionUtils.isNotEmpty(responseIds)) {
                List<SurveyResponse> list = findResponseByIds(surveyId, responseIds);
                download0(context, list, false);
            } else if (batchSize <= 0) {
                List<SurveyResponse> list = findAllResponse(surveyId);
                download0(context, list, true);
            } else {
                pageableDownload(context, batchSize);
            }
        }
        // delete empty column | copy
        List<Integer> deleteColumns = context.getResponseGroup().deleteColumns();
        copyColumns(context, deleteColumns, context.getLabelHeaders(), context.getLabelRowMap().values(), context.getFileLabel().getHeaders(), context.getFileLabel().getRows());
        copyColumns(context, deleteColumns, context.getCodeHeaders(), context.getCodeRowMap().values(), context.getFileCode().getHeaders(), context.getFileCode().getRows());

        // copy ext data
        downloadExtHelpers.forEach(extHelper -> {
            extHelper.copyColumns(context);
        });

        context.clearTemp();

        // write csv|excel
        ResponseDownloadFile file = write(context);
        progressEnd(context);
        return file;
    }

    private void pageableDownload(ResponseDownloadContext context, int batchSize) {
        Long minId = 0L;
        boolean hasNext = true;
        List<SurveyResponse> list;
        do {
            list = findResponse(context.getSurveyId(), minId, batchSize);
            if (CollectionUtils.isNotEmpty(list)) {
                download0(context, list, true);
                minId = list.get(list.size() - 1).getId();
                progressUpdateQuery(context, list.size() / 2);
            } else {
                hasNext = false;
            }
        } while (hasNext);
    }


    /**
     * 查询数据时更新进度
     */
    private void progressUpdateQuery(ResponseDownloadContext context, int append) {
        context.appendProgressed(append);
        log.info("答卷下载：{}，总数：{}，进度数：{}", context.getSurveyId(), context.getTotal(), context.getProgressed());
        Long taskId = context.getTaskId();
        if (taskId != null && taskId > 0) {
//            userTaskService.updateTaskSuccessSize(taskId, context.getProgressed());
        }
    }

    /**
     * 导出数据时初始化总行数
     */
    private void progressStartWrite(ResponseDownloadContext context) {
        List<ResponseExportFile> list = new ArrayList<>();
        list.add(context.getFileCode());
        list.add(context.getFileLabel());
        list.addAll(context.getExtFileMap().values());
        int totalRows = list.size();
        for (ResponseExportFile file : list) {
            totalRows += file.getRows().size();
        }
        context.setTotalRows(totalRows);
        context.setLogPointStep(totalRows * 1.0 / 50);
        context.setLogPoint(context.getLogPointStep());
        log.info("答卷下载：{}，初始化总行数：{}", context.getSurveyId(), totalRows);
    }

    /**
     * 导出数据时更新进度，每行数据都会调用一次，这里需要减少日志的打印和其他操作
     */
    private void progressUpdateWrite(ResponseDownloadContext context) {
        context.plusProgressedRows();
        if (context.getProgressedRows() >= context.getLogPoint()) {
            context.calcNextLogPointAndProgressed();
            log.info("答卷下载：{}，总数：{}，进度数：{}", context.getSurveyId(), context.getTotal(), context.getProgressed());
            Long taskId = context.getTaskId();
            if (taskId != null && taskId > 0) {
//                userTaskService.updateTaskSuccessSize(taskId, context.getProgressed());
            }
        }
    }

    private void progressEnd(ResponseDownloadContext context) {
        log.info("答卷下载：{}，总数：{}，已完成", context.getSurveyId(), context.getTotal());
        Long taskId = context.getTaskId();
        if (taskId != null && taskId > 0) {
//            userTaskService.updateTaskSuccessSize(taskId, context.getTotal());
        }
    }

    private ResponseDownloadContext buildContext(Long taskId, Long surveyId, DownloadType downloadType, List<String> openIds, TaskType taskType) {
        Survey survey = surveyService.requireSurvey(surveyId);
        List<SurveyChannel> channels = channelService.getChannelList(surveyId);
        ResponseDownloadContext context = new ResponseDownloadContext(taskId, surveyId, downloadType, survey, channels, openIds, taskType);
        // build file
        context.setFileLabel(new ResponseExportFile(context.getSafeTitle() + "_原始数据" + context.getDownloadType().getSuffix()));
        context.setFileCode(new ResponseExportFile(context.getSafeTitle() + "_编码数据" + context.getDownloadType().getSuffix()));
        // build headers
        downloadSendRecordHelper.buildGroup(context);
        downloadResponseParser.buildGroup(context);
        downloadCommunityUserParser.buildGroup(context);
        downloadQuestionParser.buildGroup(context);


        // build ext file
        downloadExtHelpers.forEach(extHelper -> {
            Optional.ofNullable(extHelper.buildFile(context)).ifPresent(file -> {
                context.getExtFileMap().put(extHelper.extType(), file);
            });
        });
        return context;
    }

    private void download0(ResponseDownloadContext context, List<SurveyResponse> responseList, boolean sequential) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        // 填充答卷基础数据和社区用户数据
        responseList.forEach(i -> {
            fillResponseValue(context, i);
            fillCommunityUserValue(context,i);
            fillSurveySendRecordValue(context,i);
        });

        // 获得答卷问题数据
        List<SurveyResponseCell> responseCells;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            responseCells = getCellByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            responseCells = getCellByRIds(context.getSurveyId(), rIds);
        }
        // 填充答卷问题数据
        if (CollectionUtils.isNotEmpty(responseCells)) {
            responseCells.forEach(i -> fillCellValue(context, i));
        }

        // download ext data
        downloadExtHelpers.forEach(extHelper -> {
            extHelper.download(context, responseList, sequential);
        });
        entityManager.clear();
    }

    /**
     * 填充答卷基础数据
     */
    private void fillResponseValue(ResponseDownloadContext context, SurveyResponse response) {
        Object[] labelRow = context.getEmptyRow();
        Object[] codeRow = context.getEmptyRow();
        context.getResponseGroup().getColumns().forEach(c -> {
            labelRow[c.getIndex()] = c.getLabel(response);
            codeRow[c.getIndex()] = c.getCode(response);
        });
        context.getLabelRowMap().put(response.getId(), labelRow);
        context.getCodeRowMap().put(response.getId(), codeRow);
    }

    private void fillCommunityUserValue(ResponseDownloadContext context, SurveyResponse response) {
        Object[] labelRow = context.getLabelRowMap().get(response.getId());
        Object[] codeRow = context.getCodeRowMap().get(response.getId());
        if (labelRow == null || codeRow == null || context.getCommunityUserMap() == null) {
            return;
        }
        CommunityUser communityUser = context.getCommunityUserMap().get(response.getOpenid());
        context.getCommunityUserGroup().getColumns().forEach(c ->{
            labelRow[c.getIndex()] = c.getLabel(communityUser);
            codeRow[c.getIndex()] = c.getCode(communityUser);
        });
        context.getLabelRowMap().put(response.getId(), labelRow);
        context.getCodeRowMap().put(response.getId(), codeRow);
    }

    private void  fillSurveySendRecordValue(ResponseDownloadContext context, SurveyResponse response) {
        if(context.getTaskType() == TaskType.DELIVERY) return;
        Object[] labelRow = context.getLabelRowMap().get(response.getId());
        Object[] codeRow = context.getCodeRowMap().get(response.getId());
        if (labelRow == null || codeRow == null || context.getSendRecordGroup() == null) {
            return;
        }
        CommunityUser communityUser = context.getCommunityUserMap().get(response.getOpenid());
        if (communityUser != null) {
            SurveySendRecord record = context.getSendRecordMap().get(communityUser.getId());
            context.getSendRecordGroup().getColumns().forEach(c ->{
                labelRow[c.getIndex()] = c.getLabel(record);
                codeRow[c.getIndex()] = c.getCode(record);
            });
        }
        context.getLabelRowMap().put(response.getId(), labelRow);
        context.getCodeRowMap().put(response.getId(), codeRow);
    }

    /**
     * 填充答卷问题数据
     */
    private void fillCellValue(ResponseDownloadContext context, SurveyResponseCell cell) {
        Object[] labelRow = context.getLabelRowMap().get(cell.getResponseId());
        Object[] codeRow = context.getCodeRowMap().get(cell.getResponseId());
        if (labelRow == null || codeRow == null) {
            return;
        }
        DownloadColumnQuestionGroup questionGroup = context.getQuestionGroupMap().get(cell.getQuestionId());
        if (questionGroup != null && questionGroup.getType() == cell.getType()) {
            questionGroup.getColumns().forEach(column -> {
                labelRow[column.getIndex()] = column.getLabel(cell);
                codeRow[column.getIndex()] = column.getCode(cell);
            });
        }
    }

    private void fillCommunityUserValue(ResponseDownloadContext context, CommunityUser communityUser) {


    }

    /**
     * 复制列，并删除无数据的列（deleteIfEmpty=true）
     */
    private void copyColumns(ResponseDownloadContext context, List<Integer> deleteColumns,
                             List<String> originHeaders, Collection<Object[]> originRows,
                             List<List<String>> formatHeaders, List<List<Object>> formatRows) {
        IntStream.range(0, context.getColumnSize()).filter(j -> !deleteColumns.contains(j)).forEach(i -> {
            String header = originHeaders.get(i);
            formatHeaders.add(List.of(header));
        });
        originRows.forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            IntStream.range(0, context.getColumnSize()).filter(j -> !deleteColumns.contains(j)).forEach(i -> {
                row.add(originRow[i]);
            });
            formatRows.add(row);
        });
    }

    private ResponseDownloadFile write(ResponseDownloadContext context) {
        try {
            progressStartWrite(context);
            WriteHandler handler = new RowWriteHandler() {
                @Override
                public void afterRowCreate(RowWriteHandlerContext c) {
                    progressUpdateWrite(context);
                }
            };
            List<ResponseDownloadFile> files = new ArrayList<>();
            writeFile(files, context.getFileLabel(), handler);
            writeFile(files, context.getFileCode(), handler);
            for (ResponseExportFile extFile : context.getExtFileMap().values()) {
                writeFile(files, extFile, handler);
            }
            if (!context.getAdditionalFiles().isEmpty()) {
//                context.getAdditionalFiles().values().forEach(l -> {
//                    Optional.ofNullable(l).ifPresent(i -> i.forEach(f -> writeAdditionalFile(files, f)));
//                });
            }
            return zipFile(context.getSafeTitle() + "_" + LocalDate.now() + ".zip", files);

        } catch (IOException e) {
            log.error("生成文件失败", e);
        }
        return null;
    }

    public void writeFile(List<ResponseDownloadFile> files, ResponseExportFile file, WriteHandler handler) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(bos).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }
        excelWriterBuilder.head(file.getHeaders()).sheet("导出").doWrite(file.getRows());
        bos.flush();
        files.add(new ResponseDownloadFile(file.getFileName(), bos.toByteArray()));
        file.clear();
    }

    public void writeAdditionalFile(List<ResponseDownloadFile> files, ResponseAdditionalFile file) {
        files.add(new ResponseDownloadFile(file.getFullFileName(), fileStorageService.download(file.getFileUrl()).bytes()));
    }

    public ResponseDownloadFile zipFile(String fileName, List<ResponseDownloadFile> files) throws IOException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(bos)) {
            byte[] bytes = new byte[2048];

            for (ResponseDownloadFile file : files) {
                try (InputStream fis = new ByteArrayInputStream(file.getBytes()); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    zos.putNextEntry(new ZipEntry(file.getFileName()));
                    int bytesRead;
                    while ((bytesRead = bis.read(bytes)) != -1) {
                        zos.write(bytes, 0, bytesRead);
                    }
                    zos.closeEntry();
                    file.setBytes(new byte[0]);
                }
            }
            zos.close();
            byte[] zipBytes = bos.toByteArray();
            return new ResponseDownloadFile(fileName, zipBytes);
        }
    }

    private List<SurveyResponse> findResponseByIds(Long surveyId, List<Long> responseIds) {
        return surveyResponseRepository.findBySurveyIdAndIdIn(surveyId, responseIds);
    }

    private int countAllResponse(Long surveyId) {
        return (int) surveyResponseRepository.countBySurveyIdAndStatus(surveyId, ResponseStatus.FINAL_SUBMIT);
    }

    private List<SurveyResponse> findAllResponse(Long surveyId) {
        return surveyResponseRepository.findBySurveyIdAndStatusOrderByIdAsc(surveyId, ResponseStatus.FINAL_SUBMIT);
    }

    private List<SurveyResponse> findResponse(Long surveyId, Long minId, int limit) {
        return surveyResponseRepository.findBySurveyIdAndStatusAndIdGreaterThan(surveyId, ResponseStatus.FINAL_SUBMIT, minId, PageRequest.of(0, limit, Sort.by("id")));
    }

    private List<SurveyResponseCell> getCellByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyResponseCellRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyResponseCell> getCellByRIds(Long surveyId, List<Long> rIds) {
        return surveyResponseCellRepository.findAllBySurveyIdAndResponseIdIn(surveyId, rIds);
    }
}
