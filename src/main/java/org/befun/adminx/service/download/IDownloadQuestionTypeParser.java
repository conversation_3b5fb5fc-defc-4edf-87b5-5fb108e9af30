package org.befun.adminx.service.download;


import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;

public interface IDownloadQuestionTypeParser {

    QuestionType type();

    DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question);
}
