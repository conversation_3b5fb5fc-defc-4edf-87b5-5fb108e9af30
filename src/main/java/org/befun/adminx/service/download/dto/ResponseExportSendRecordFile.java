package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

@Getter
@Setter
public class ResponseExportSendRecordFile extends ResponseExportFile {

    private Integer columnSize = 0;

    private DownloadColumnSendRecordGroup sendRecordGroup = new DownloadColumnSendRecordGroup();

    public ResponseExportSendRecordFile(String fileName) {
        super(fileName);
    }

    public void plusColumnSize() {
        columnSize++;
    }
}
