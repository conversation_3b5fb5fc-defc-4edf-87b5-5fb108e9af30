package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DownloadQuestionTypeParser_003_SINGLE_CHOICE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.SINGLE_CHOICE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        // 增加选项列
        group.getColumns().add(new DownloadColumnQuestion_SINGLE_CHOICE_item(context, group));
        if (group.getQuestionItems().stream().anyMatch(i -> i.getEnableTextInput() != null && i.getEnableTextInput())) {
            // 增加选项中包含文本输入列
            group.getColumns().add(new DownloadColumnQuestion_SINGLE_CHOICE_comment(context, group));
        }
        return group;
    }

    public static class DownloadColumnQuestion_SINGLE_CHOICE_item extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SINGLE_CHOICE_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return group.getItemIndexMap().get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return group.getItemTextMap().get(cell.getStrValue());
        }
    }

    public static class DownloadColumnQuestion_SINGLE_CHOICE_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SINGLE_CHOICE_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_文本输入",
                    group.getCode() + "_text");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Map<String, Object> map = JsonHelper.toMap(cell.getCommentValue());
            return map == null ? null : map.get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }

    }
}
