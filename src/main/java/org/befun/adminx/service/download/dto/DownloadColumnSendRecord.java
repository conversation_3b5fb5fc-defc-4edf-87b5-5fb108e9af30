package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.service.download.IDownloadColumn;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

@Setter
@Getter
@NoArgsConstructor
public class DownloadColumnSendRecord implements IDownloadColumn<SurveySendRecord> {

    private AtomicInteger countValue = new AtomicInteger();

    private int index;
    private String label;
    private String code;
    private boolean numberValue;
    private boolean deleteIfEmpty;
    private Function<SurveySendRecord, Object> getValue;

    public DownloadColumnSendRecord(int index, String label, String code, boolean numberValue, boolean deleteIfEmpty, Function<SurveySendRecord, Object> getValue) {
        this.index = index;
        this.label = label;
        this.code  = code;
        this.numberValue = numberValue;
        this.deleteIfEmpty = deleteIfEmpty;
        this.getValue = getValue;
    }

    @Override
    public Object getCode(SurveySendRecord record) {
        Object v = getLabel(record);
        if (v != null) {
            countValue.getAndIncrement();
        }
        return v;
    }

    @Override
    public Object getLabel(SurveySendRecord record) {
        if(record == null){
            return null;
        }
        Object v = getValue.apply(record);
        if (v != null && !numberValue && !(v instanceof String)) {
            // 此列数据为字符串，但是实际类型不是字符串，调用一次 toString()
            return v.toString();
        } else {
            return v;
        }
    }
}
