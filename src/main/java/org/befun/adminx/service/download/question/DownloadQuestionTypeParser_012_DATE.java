package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.utils.DateFormatter;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.Date;

@Component
public class DownloadQuestionTypeParser_012_DATE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.DATE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_DATE(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_DATE extends DownloadColumnQuestion {

        private final DateTimeFormatter formatter;

        public DownloadColumnQuestion_DATE(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
            this.formatter = DateTimeFormatter.ofPattern(group.getQuestion().getAreaType().getFormat());
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Long ms = cell.getDateValue();
            if (ms != null) {
                return DateFormatter.format(new Date(ms), formatter);
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
