package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion_item;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_028_BLANK implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.BLANK;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        group.getQuestionItems().forEach(i -> {
            int itemIndex = index.getAndIncrement();
            // 增加选项列，每个选项作为一列
            group.getColumns().add(new DownloadColumnQuestion_BLANK_item(context, group, itemIndex, i));
        });
        return group;
    }

    public static class DownloadColumnQuestion_BLANK_item extends DownloadColumnQuestion_item {

        public DownloadColumnQuestion_BLANK_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group, itemIndex, item, group.getCode() + "_填空项" + itemIndex, group.getCode() + "_" + itemIndex);
        }

        @Override
        public Object getCode(SurveyResponseCell surveyResponseCell) {
            return getLabel(surveyResponseCell);
        }

        @Override
        public Object getLabel(SurveyResponseCell surveyResponseCell) {
            return  surveyResponseCell.getJsonValue().get(item.getValue());
        }
    }
}
