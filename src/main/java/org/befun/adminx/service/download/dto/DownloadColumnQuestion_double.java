package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyResponseCell;

@Getter
@Setter
public class DownloadColumnQuestion_double extends DownloadColumnQuestion {

    public DownloadColumnQuestion_double(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getDoubleValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getDoubleValue();
    }
}