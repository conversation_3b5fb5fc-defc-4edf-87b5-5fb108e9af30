package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_023_COMBOBOX implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.COMBOBOX;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        // 增加选项列
        group.getColumns().add(new DownloadColumnQuestion_COMBOBOX_item(context, group));
        if (group.getQuestionItems().stream().anyMatch(i -> i.getEnableTextInput() != null && i.getEnableTextInput())) {
            // 增加选项中包含文本输入列
            group.getColumns().add(new DownloadColumnQuestion_COMBOBOX_comment(context, group));
        }
        return group;
    }

    public static class DownloadColumnQuestion_COMBOBOX_item extends DownloadColumnQuestion {

        public DownloadColumnQuestion_COMBOBOX_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return group.getItemIndexMap().get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return group.getItemTextMap().get(cell.getStrValue());
        }
    }

    public static class DownloadColumnQuestion_COMBOBOX_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_COMBOBOX_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_文本输入",
                    group.getCode() + "_text");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

    }

}
