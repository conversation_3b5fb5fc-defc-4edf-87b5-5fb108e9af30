package org.befun.adminx.service.download.question;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.survey.SurveyFileUploadDto;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseAdditionalFile;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class DownloadQuestionTypeParser_016_FILE implements IDownloadQuestionTypeParser {

    private static final String BASE_PATH = "上传文件/";

    @Override
    public QuestionType type() {
        return QuestionType.FILE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_FILE(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_FILE extends DownloadColumnQuestion {

        public DownloadColumnQuestion_FILE(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            List<SurveyFileUploadDto> files = JsonHelper.toList(cell.getStrValue(), SurveyFileUploadDto.class);
            if (CollectionUtils.isNotEmpty(files)) {
                List<ResponseAdditionalFile> additionalFiles = context.getAdditionalFiles().computeIfAbsent(cell.getResponseId(), k -> new ArrayList<>());
                files.forEach(file -> {
                    if (StringUtils.isNotEmpty(file.getPath())) {
                        // path: ${BASE_PATH}/${rId}
                        // name: ${rId}_${qCode}_${filename}
                        String fileName = cell.getResponseId() + "_" + group.getCode() + "_" + file.getFileName();
                        String filePath = BASE_PATH + cell.getResponseId();
                        // 同名文件数量
                        int sameIndex = (int) additionalFiles.stream().filter(i -> fileName.equals(i.getFileName())).count();
                        additionalFiles.add(new ResponseAdditionalFile(fileName, sameIndex, filePath, file.getPath()));
                    }
                });
            }
//            return cell.getResponseId() + "_" + group.getCode();
            //TODO 图片下载地址
            return files == null || files.size() == 0 ? null : files.get(0).getPath();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            //            return cell.getResponseId() + "_" + group.getCode();
            //TODO 图片下载地址
            List<SurveyFileUploadDto> files = JsonHelper.toList(cell.getStrValue(), SurveyFileUploadDto.class);
            return files == null || files.size() == 0 ? null : files.get(0).getPath();
        }
    }

}
