package org.befun.adminx.service.download;


import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.service.download.dto.ResponseExportFile;
import org.befun.adminx.service.download.ext.DownloadExtType;

import java.util.List;
import java.util.Optional;

public interface IDownloadExtHelper<F extends ResponseExportFile> {

    DownloadExtType extType();

    F buildFile(ResponseDownloadContext context);

    default void download(ResponseDownloadContext context, List<SurveyResponse> responseList, boolean sequential) {
        Optional.ofNullable(getFile(context)).ifPresent(file -> download(context, file, responseList, sequential));
    }

    void download(ResponseDownloadContext context, F file, List<SurveyResponse> responseList, boolean sequential);

    default void copyColumns(ResponseDownloadContext context) {
        Optional.ofNullable(getFile(context)).ifPresent(file -> copyColumns(context, file));
    }

    void copyColumns(ResponseDownloadContext context, F file);

    @SuppressWarnings("unchecked")
    default F getFile(ResponseDownloadContext context) {
        return (F) context.getExtFileMap().get(extType());
    }
}
