package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.service.download.IDownloadColumnGroup;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/13 16:23
 */
@Getter
@Setter
public class DownloadColumnCommunityUserGroup implements IDownloadColumnGroup<CommunityUser, DownloadColumnCommunityUser> {
    private final List<DownloadColumnCommunityUser> columns = new ArrayList<>();

    public List<Integer> deleteColumns() {
        return columns.stream().filter(i -> i.getCountValue().get() == 0).map(DownloadColumnCommunityUser::getIndex).collect(Collectors.toList());
    }
}
