package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionColumn;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadColumnGroup;
import org.befun.adminx.utils.RegularExpressionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class DownloadColumnQuestionGroup implements IDownloadColumnGroup<SurveyResponseCell, DownloadColumnQuestion> {

    private Long id;
    private QuestionType type;
    private String code;
    private String title;
    private SurveyQuestion question;
    private List<SurveyQuestionItem> questionItems;
    private List<SurveyQuestionColumn> questionColumns;
    private Map<String, SurveyQuestionItem> itemMap = new HashMap<>();
    private Map<String, Integer> itemIndexMap = new HashMap<>();
    private Map<String, String> itemTextMap = new HashMap<>();
    private Map<String, SurveyQuestionColumn> columnMap = new HashMap<>();
    private Map<String, Integer> columnIndexMap = new HashMap<>();
    private Map<String, String> columnTextMap = new HashMap<>();
    private final List<DownloadColumnQuestion> columns = new ArrayList<>();

    public DownloadColumnQuestionGroup(SurveyQuestion question, List<SurveyQuestionItem> questionItems, List<SurveyQuestionColumn> questionColumns) {
        this.question = question;
        this.id = question.getId();
        this.type = question.getType();
        this.code = question.getCode();
        this.title = RegularExpressionUtils.replaceHtml(question.getTitle());
        if (CollectionUtils.isNotEmpty(questionItems)) {
            this.questionItems = questionItems;
            AtomicInteger index = new AtomicInteger();
            questionItems.forEach(i -> {
                itemMap.put(i.getValue(), i);
                itemIndexMap.put(i.getValue(), index.incrementAndGet());
                itemTextMap.put(i.getValue(), RegularExpressionUtils.replaceHtml(i.getText()));
            });
        } else {
            this.questionItems = new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(questionColumns)) {
            this.questionColumns = questionColumns;
            AtomicInteger index = new AtomicInteger();
            questionColumns.forEach(i -> {
                columnMap.put(i.getValue(), i);
                columnIndexMap.put(i.getValue(), index.incrementAndGet());
                columnTextMap.put(i.getValue(), RegularExpressionUtils.replaceHtml(i.getText()));
            });
        } else {
            this.questionColumns = new ArrayList<>();
        }
    }
}
