package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_024_SCORE_EVALUATION implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.SCORE_EVALUATION;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_SCORE_EVALUATION_score(context, group));
        group.getColumns().add(new DownloadColumnQuestion_SCORE_EVALUATION_item(context, group));
        group.getColumns().add(new DownloadColumnQuestion_SCORE_EVALUATION_tag(context, group));
        group.getColumns().add(new DownloadColumnQuestion_SCORE_EVALUATION_comment(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_SCORE_EVALUATION_score extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SCORE_EVALUATION_score(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_打分",
                    group.getCode() + "_1");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCellScore();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCellScore();
        }
    }

    public static class DownloadColumnQuestion_SCORE_EVALUATION_item extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SCORE_EVALUATION_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_评价",
                    group.getCode() + "_2");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return group.getItemIndexMap().get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return group.getItemTextMap().get(cell.getStrValue());
        }
    }

    public static class DownloadColumnQuestion_SCORE_EVALUATION_tag extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SCORE_EVALUATION_tag(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_标签",
                    group.getCode() + "_3");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getTags();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getTags();
        }
    }

    public static class DownloadColumnQuestion_SCORE_EVALUATION_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SCORE_EVALUATION_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_评价文本",
                    group.getCode() + "_4");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

    }


}
