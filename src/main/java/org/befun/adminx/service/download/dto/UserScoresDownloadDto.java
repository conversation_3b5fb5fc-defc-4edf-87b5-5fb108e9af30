package org.befun.adminx.service.download.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.CommunityUserScoresDto;
import org.befun.core.utils.DateHelper;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserScoresDownloadDto {
    @ExcelProperty(index = 0, value = "cuid")
    private String cuid;
    @ExcelProperty(index = 1, value = "openid")
    private String openid;
    @ExcelProperty(index = 2, value = "微信昵称")
    private String nickName;
    @ExcelProperty(index = 3, value = "手机号")
    private String mobile;
    @ExcelProperty(index = 4, value = "问卷id")
    private String surveyId;
    @ExcelProperty(index = 5, value = "问卷标题")
    private String surveyTitle;
    @ExcelProperty(index = 6, value = "积分奖励")
    private String score;
    @ExcelProperty(index = 7, value = "积分变动")
    private String title;
    @ExcelProperty(index = 8, value = "奖励类型")
    private String type;
    @ExcelProperty(index = 9, value = "操作人")
    private String operator;
    @ExcelProperty(index = 10, value = "时间")
    private String createTime;

    public UserScoresDownloadDto(CommunityUserScoresDto score) {
        this.cuid = score.getUser() == null || score.getUser().getId() == null ? "" : score.getUser().getId().toString();
        this.openid = score.getUser() == null || score.getUser().getOpenId() == null  ? "" : score.getUser().getOpenId();
        this.nickName = score.getUser() == null || score.getUser().getNickName() == null  ? "" : score.getUser().getNickName();
        this.mobile = score.getUser() == null || score.getUser().getMobile() == null  ? "" : score.getUser().getMobile();
        this.surveyId = score.getSurveyId() == null ? "" : score.getSurveyId();
        this.surveyTitle = score.getSurveyTitle() == null ? "" : score.getSurveyTitle();
        this.score = score.getScore() == null ? "" : score.getScoreChange().toString();
        this.title = score.getTitle() == null ? "" : score.getTitle();
        this.type = score.getType() == null ? "" : convertScoreType(score.getType());
        this.operator = score.getOperator();
        this.createTime = Objects.toString(DateHelper.formatDateTime(score.getCreateTime()), "");
    }

    public String convertScoreType(String type) {
        String typeName = "";
        switch(type) {
            case "U":
                typeName = "等级奖励";break;
            case "A":
                typeName = "完善个人资料奖励";break;
            case "F":
                typeName = "待审核奖励";break;
            case "F-P":
                typeName = "问卷答题奖励";break;
            case "F-N":
                typeName = "审核不通过";break;
            case "I-V":
                typeName = "10%佣金奖励";break;
            case "I-A":
                typeName = "邀请积分奖励";break;
            case "D-C":
                typeName = "积分提现-成功";break;
            case "D-F":
                typeName = "积分提现-失败";break;
            case "D-H":
                typeName = "后台提现积分-成功";break;
            case "D-A":
                typeName = "后台增加积分-成功";break;
            default:
        }
        return typeName;
    }
}
