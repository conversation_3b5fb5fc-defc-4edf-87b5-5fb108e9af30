package org.befun.adminx.service.download;

import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.download.dto.DownloadColumnCommunityUser;
import org.befun.adminx.service.download.dto.DownloadColumnCommunityUserGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.utils.DateFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/10 16:04
 */
@Component
public class DownloadCommunityUserParser {

    @Autowired
    private CommunityUserService communityUserService;

    public void buildGroup(ResponseDownloadContext context) {
        DownloadColumnCommunityUserGroup group = new DownloadColumnCommunityUserGroup();
        List<DownloadColumnCommunityUser> columns = group.getColumns();
        context.setCommunityUserMap(communityUserService.getUserByOpenIds(context.getOpenIdList()));
        context.setCommunityUserGroup(group);
        buildColumn(context, columns,"CUID","cuid", CommunityUser::getId);
        buildColumn(context, columns,"推荐人","invite_id", CommunityUser::getInviteId);
        buildColumn(context, columns,"关注状态","wechat_subscribe", CommunityUser::getWechatSubscribe);
//        buildColumn(context, columns,"手机号","phone_number", CommunityUser::getMobile);
        buildColumn(context, columns,"性别","gender", i -> i.getAdditional() == null ? null : i.getAdditional().getGender());
        buildColumn(context, columns,"年龄","age", i -> i.getAdditional() == null ? null : DateFormatter.getAge(i.getAdditional().getBirthday()));
        buildColumn(context, columns,"学历","education", i -> i.getAdditional() == null ? null : i.getAdditional().getEducation());
        buildColumn(context, columns,"学历更新时间","education_update_time", i -> i.getAdditional() == null ? null : i.getAdditional().getEducationModified());
        buildColumn(context, columns,"省份","_province", i -> i.getAdditional() == null ? null : i.getAdditional().getProvince());
        buildColumn(context, columns,"城市","_city", i -> i.getAdditional() == null ? null : i.getAdditional().getCity());
        buildColumn(context, columns,"县/区","_area", i -> i.getAdditional() == null ? null : i.getAdditional().getArea());
    }

    private void buildColumn(ResponseDownloadContext context, List<DownloadColumnCommunityUser> columns, String label, String code, Function<CommunityUser, Object> getValue) {
        columns.add(new DownloadColumnCommunityUser(context.getColumnSize(), label, code, getValue));
        context.plusColumnSize();
        context.addCodeHeader(code);
        context.addLabelHeader(label);
    }
}
