package org.befun.adminx.service.download.question;

import cn.hanyi.common.file.storage.FileStorageService;
import com.alibaba.excel.metadata.data.HyperlinkData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.DownloadType;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.survey.SurveyFileUploadDto;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.service.download.dto.ResponseDownloadFile;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class DownloadQuestionTypeParser_029_SIGNATURE implements IDownloadQuestionTypeParser {

    private static final String CACHE_FILE_ZIP_KEY = "cache:file:zip";

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public QuestionType type() {
        return QuestionType.SIGNATURE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_SIGNATURE(context, group));
        return group;
    }

    private static ResponseDownloadFile zipFile(String fileName, List<ResponseDownloadFile> files) throws IOException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(bos)) {
            byte[] bytes = new byte[2048];

            AtomicInteger count = new AtomicInteger(1);
            for (ResponseDownloadFile file : files) {
                try (InputStream fis = new ByteArrayInputStream(file.getBytes()); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    zos.putNextEntry(new ZipEntry(count.getAndIncrement() + file.getFileName()));
                    int bytesRead;
                    while ((bytesRead = bis.read(bytes)) != -1) {
                        zos.write(bytes, 0, bytesRead);
                    }
                    zos.closeEntry();
                    file.setBytes(new byte[0]);
                }
            }
            zos.close();
            byte[] zipBytes = bos.toByteArray();
            return new ResponseDownloadFile(fileName, zipBytes);
        }
    }

    @SneakyThrows
    private Object hyperlinkFile(SurveyResponseCell cell, DownloadType downloadType, String code) {
        List<SurveyFileUploadDto> files = JsonHelper.toList(cell.getStrValue(), SurveyFileUploadDto.class);
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }

        String url = files.get(0).getPath();
        String name = String.format("%s_%s_%s", cell.getResponseId(), code, files.get(0).getFileName());

        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(name)) {
            return null;
        }

        if (DownloadType.EXCEL.equals(downloadType)) {
            // 设置超链接
            WriteCellData<String> hyperlink = new WriteCellData<>(name);
            HyperlinkData hyperlinkData = new HyperlinkData();
            hyperlink.setHyperlinkData(hyperlinkData);
            hyperlinkData.setAddress(this.encodeUrl(url));
            hyperlinkData.setHyperlinkType(HyperlinkData.HyperlinkType.URL);

            // 设置超链接样式下划线 斜体 蓝色
            WriteCellStyle hyperlinkStyle = new WriteCellStyle();
            WriteFont hyperlinkFont = new WriteFont();
            hyperlinkFont.setUnderline((byte) 1);
            hyperlinkFont.setColor((short) 12);
            hyperlinkStyle.setWriteFont(hyperlinkFont);
            hyperlink.setWriteCellStyle(hyperlinkStyle);


            return hyperlink;
        }
        return String.format("=HYPERLINK(\"%s\",\"%s\")", url, name);
    }

    private String cacheKey(Long cellId, String fileName) {
        return String.format("%s:%s:%s", CACHE_FILE_ZIP_KEY, cellId, fileName);
    }

    public class DownloadColumnQuestion_SIGNATURE extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SIGNATURE(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return hyperlinkFile(cell, context.getDownloadType(), group.getCode());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return hyperlinkFile(cell, context.getDownloadType(), group.getCode());
        }
    }

    /**
     * 超链接 需要对url特殊字符encode
     * @param url
     * @return
     * @throws UnsupportedEncodingException
     */
    private String encodeUrl(String url) throws UnsupportedEncodingException {
// 对整个 URL 进行编码
        String encodedUrl = URLEncoder.encode(url, "UTF-8").replace("+", "%20");

        // 手动保留某些字符不被编码
        encodedUrl = encodedUrl.replace("%3A", ":").replace("%2F", "/").replace("files%3Furl%3D", "files?url=");
//        encodedUrl = encodedUrl.replace("%5B", "[").replace("%5D", "]");
        return encodedUrl;
    }

}
