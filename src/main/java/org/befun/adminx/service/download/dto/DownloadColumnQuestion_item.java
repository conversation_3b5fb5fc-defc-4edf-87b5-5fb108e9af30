package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.adminx.utils.RegularExpressionUtils;

@Getter
@Setter
public abstract class DownloadColumnQuestion_item extends DownloadColumnQuestion {

    protected final SurveyQuestionItem item;
    protected final int itemIndex;
    protected final String itemText;

    public DownloadColumnQuestion_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
        super(context, group, context.getColumnSize(),
                group.getCode() + "_" + group.getTitle() + "_" + RegularExpressionUtils.replaceHtml(item.getText()),
                group.getCode() + "_" + itemIndex);
        this.itemIndex = itemIndex;
        this.item = item;
        this.itemText = RegularExpressionUtils.replaceHtml(item.getText());
    }

    public DownloadColumnQuestion_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item, String label, String code) {
        super(context, group, context.getColumnSize(), label, code);
        this.itemIndex = itemIndex;
        this.item = item;
        this.itemText = RegularExpressionUtils.replaceHtml(item.getText());
    }
}
