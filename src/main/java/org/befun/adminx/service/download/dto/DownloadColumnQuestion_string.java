package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyResponseCell;

@Getter
@Setter
public class DownloadColumnQuestion_string extends DownloadColumnQuestion {

    public DownloadColumnQuestion_string(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getStrValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getStrValue();
    }
}
