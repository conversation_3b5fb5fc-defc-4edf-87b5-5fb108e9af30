package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.service.download.IDownloadColumnGroup;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnResponseGroup implements IDownloadColumnGroup<SurveyResponse, DownloadColumnResponse> {

    private final List<DownloadColumnResponse> columns = new ArrayList<>();

    public List<Integer> deleteColumns() {
        return columns.stream().filter(i -> i.isDeleteIfEmpty() && i.getCountValue().get() == 0).map(DownloadColumnResponse::getIndex).collect(Collectors.toList());
    }
}
