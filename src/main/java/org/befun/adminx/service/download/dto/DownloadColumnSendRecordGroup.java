package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.service.download.IDownloadColumnGroup;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnSendRecordGroup implements IDownloadColumnGroup<SurveySendRecord, DownloadColumnSendRecord> {

    private final List<DownloadColumnSendRecord> columns = new ArrayList<>();

}
