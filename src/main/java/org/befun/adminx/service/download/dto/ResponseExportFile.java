package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ResponseExportFile {

    private String fileName;
    private List<List<String>> headers = new ArrayList<>();
    private List<List<Object>> rows = new ArrayList<>();

    public ResponseExportFile(String fileName) {
        this.fileName = fileName;
    }

    public void clear() {
        headers.clear();
        rows.clear();
    }
}
