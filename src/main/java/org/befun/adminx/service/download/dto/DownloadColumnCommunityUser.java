package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.service.download.IDownloadColumn;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/13 16:18
 */
@Getter
@Setter
public class DownloadColumnCommunityUser implements IDownloadColumn<CommunityUser> {

    private AtomicInteger countValue = new AtomicInteger();

    private int index;
    private String label;
    private String code;
    private Function<CommunityUser, Object> getValue;


    public DownloadColumnCommunityUser(int index, String label, String code, Function<CommunityUser, Object> getValue) {
        this.index = index;
        this.label = label;
        this.code = code;
        this.getValue = getValue;
    }

    @Override
    public Object getCode(CommunityUser user) {
        Object v = getLabel(user);
        if (v != null) {
            countValue.getAndIncrement();
        }
        return v;
    }

    @Override
    public Object getLabel(CommunityUser user) {
        if(user == null) return null;
        Object v = getValue.apply(user);
        if (v != null && !(v instanceof String)) {
            // 此列数据为字符串，但是实际类型不是字符串，调用一次 toString()
            return v.toString();
        } else {
            return v;
        }
    }
}
