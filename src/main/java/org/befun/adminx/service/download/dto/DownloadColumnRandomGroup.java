package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnRandomGroup {

    private final LinkedHashMap<Long, DownloadColumnRandom> columnMap = new LinkedHashMap<>();

    public List<Integer> deleteColumns() {
        return columnMap.values().stream().filter(i -> i.getCountValue().get() == 0).map(DownloadColumnRandom::getIndex).collect(Collectors.toList());
    }

    public List<String> originHeaders() {
        List<String> headers = columnMap.values().stream().map(DownloadColumnRandom::getHeader).collect(Collectors.toList());
        headers.add(0, "id");
        return headers;
    }
}
