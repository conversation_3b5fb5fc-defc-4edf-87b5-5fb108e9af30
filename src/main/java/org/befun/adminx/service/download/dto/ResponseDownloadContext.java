package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.constant.survey.DownloadType;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.service.download.ext.DownloadExtType;
import org.befun.adminx.utils.RegularExpressionUtils;

import java.util.*;

@Getter
@Setter
public class ResponseDownloadContext {

    private final long surveyId;
    private DownloadType downloadType;
    private Survey survey;
    private String safeTitle;
    private Map<Long, SurveyChannel> channelMap = new HashMap<>();
    private List<SurveyQuestion> originQuestions;
    private List<SurveyQuestion> originSortGroups = new ArrayList<>();
    private List<SurveyQuestion> originSortQuestions = new ArrayList<>();
    private List<SurveyQuestion> filterAndSortQuestions;

    // 进度相关
    private Long taskId;
    private TaskType taskType;
    private int total;
    private int progressed;
    private int totalRows;
    private int progressedRows;
    private double logPoint;
    private double logPointStep;

    // 构建答卷数据的临时数据
    private DownloadColumnResponseGroup responseGroup;
    private Map<Long, DownloadColumnQuestionGroup> questionGroupMap = new HashMap<>();
    private int columnSize = 0;
    private List<String> labelHeaders = new ArrayList<>();
    private List<String> codeHeaders = new ArrayList<>();
    private final LinkedHashMap<Long, Object[]> labelRowMap = new LinkedHashMap<>();
    private final LinkedHashMap<Long, Object[]> codeRowMap = new LinkedHashMap<>();

    private ResponseExportFile fileLabel;
    private ResponseExportFile fileCode;
    private Map<Long, List<ResponseAdditionalFile>> additionalFiles = new HashMap<>();
    private Map<DownloadExtType, ResponseExportFile> extFileMap = new HashMap<>();

    //社区信息
    private List<String> openIdList = new ArrayList<>();
    private DownloadColumnCommunityUserGroup communityUserGroup;
    private Map<String,CommunityUser> communityUserMap = new HashMap<>();

    //追访记录
    private DownloadColumnSendRecordGroup sendRecordGroup;
    private Map<Long, SurveySendRecord> sendRecordMap = new HashMap<>();


    public ResponseDownloadContext(Long taskId, long surveyId, DownloadType downloadType, Survey survey, List<SurveyChannel> channels, List<String> openIdList, TaskType taskType) {
        this.taskId = taskId;
        this.taskType = taskType;
        this.surveyId = surveyId;
        this.downloadType = downloadType;
        this.survey = survey;
        this.safeTitle = RegularExpressionUtils.replaceHtml(survey.getRealTitle());
        if (CollectionUtils.isNotEmpty(channels)) {
            channels.forEach(c -> channelMap.put(c.getId(), c));
        }
        this.openIdList = openIdList;
    }

    public Object[] getEmptyRow() {
        return new Object[columnSize];
    }

    public void plusColumnSize() {
        columnSize++;
    }

    public void addLabelHeader(String labelHeader) {
        labelHeaders.add(labelHeader);
    }

    public void addCodeHeader(String codeHeader) {
        codeHeaders.add(codeHeader);
    }

    public void appendProgressed(int append) {
        int i = this.progressed + append;
        this.progressed = Math.min(i, total);
    }

    public void plusProgressedRows() {
        progressedRows++;
    }

    public void calcNextLogPointAndProgressed() {
        if (this.logPoint > 0 && this.logPointStep > 0 && this.totalRows > 0) {
            do {
                this.logPoint += this.logPointStep;
            } while (this.logPoint < this.progressedRows);
            int relativeProgressed = (int) ((this.progressedRows * 1.0 / this.totalRows) * (this.total / 2));
            int append = relativeProgressed + (this.total / 2) - progressed;
            appendProgressed(append);
        }
    }

    public void clearTemp() {
        labelHeaders.clear();
        codeHeaders.clear();
        labelRowMap.clear();
        codeRowMap.clear();
    }

}
