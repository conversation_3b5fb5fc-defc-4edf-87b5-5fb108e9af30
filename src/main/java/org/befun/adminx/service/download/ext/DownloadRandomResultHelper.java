package org.befun.adminx.service.download.ext;
import org.apache.commons.collections.CollectionUtils;
import org.befun.adminx.constant.survey.QuestionRandomType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyRandomResult;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.SurveyRandomResultRepository;
import org.befun.adminx.service.download.IDownloadExtHelper;
import org.befun.adminx.service.download.dto.DownloadColumnRandom;
import org.befun.adminx.service.download.dto.DownloadColumnRandomGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.service.download.dto.ResponseExportRandomFile;
import org.befun.core.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
public class DownloadRandomResultHelper implements IDownloadExtHelper<ResponseExportRandomFile> {

    @Autowired
    private SurveyRandomResultRepository surveyRandomResultRepository;


    @Override
    public DownloadExtType extType() {
        return DownloadExtType.random_result;
    }

    @Override
    public ResponseExportRandomFile buildFile(ResponseDownloadContext context) {
        Optional<SurveyRandomResult> optional= surveyRandomResultRepository.findFirstBySurveyId(context.getSurveyId());
        if (optional.isPresent()) {
            ResponseExportRandomFile file = new ResponseExportRandomFile(context.getSafeTitle() + "_随机结果" + context.getDownloadType().getSuffix());
            DownloadColumnRandomGroup randomGroup = new DownloadColumnRandomGroup();
            buildGroup(context, file, randomGroup);
            file.setRandomGroup(randomGroup);
            return file;
        }
        return null;
    }

    private void buildGroup(ResponseDownloadContext context, ResponseExportRandomFile file, DownloadColumnRandomGroup randomGroup) {
        boolean existGroup = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.GROUP);
        if (existGroup) {
            for (SurveyQuestion group : context.getOriginSortGroups()) {
                randomGroup.getColumnMap().put(group.getId(), new DownloadColumnRandom(file.getRandomSize(), group.getCode() + "_group_display"));
                file.plusRandomSize();
            }
        }
        boolean existQuestion = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.QUESTION);
        if (existQuestion) {
            for (SurveyQuestion question : context.getOriginSortQuestions()) {
                randomGroup.getColumnMap().put(question.getId(), new DownloadColumnRandom(file.getRandomSize(), question.getCode() + "_display"));
                file.plusRandomSize();
            }
        }
    }

    @Override
    public void download(ResponseDownloadContext context, ResponseExportRandomFile file, List<SurveyResponse> responseList, boolean sequential) {
        List<SurveyRandomResult> records;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            records = getByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            records = getByRIds(context.getSurveyId(), rIds);
        }
        if (CollectionUtils.isNotEmpty(responseList)) {
            responseList.forEach(i -> fillId(file, i));
        }
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(i -> fillValue(file, i));
        }
    }

    private void fillId(ResponseExportRandomFile file, SurveyResponse response) {
        Object[] row = new Object[file.getRandomSize()];
        row[0] = response.getId().toString();
        file.getRandomRowMap().put(response.getId(), row);
    }

    private void fillValue(ResponseExportRandomFile file, SurveyRandomResult record) {
        if (record.getResponseId() == null && record.getQuestionId() != null) {
            return;
        }
        DownloadColumnRandom columnRandom = file.getRandomGroup().getColumnMap().get(record.getQuestionId());
        if (columnRandom == null) {
            return;
        }
        Object[] row = file.getRandomRowMap().get(record.getResponseId());
        if (row == null) {
            return;
        }
        row[columnRandom.getIndex()] = columnRandom.getCode(record);
    }

    @Override
    public void copyColumns(ResponseDownloadContext context, ResponseExportRandomFile fileRandom) {
        List<String> originHeaders = fileRandom.getRandomGroup().originHeaders();
        List<List<String>> formatHeaders = fileRandom.getHeaders();
        Collection<Object[]> originRows = fileRandom.getRandomRowMap().values();
        List<List<Object>> formatRows = fileRandom.getRows();
       //List<Integer> deleteColumns = fileRandom.getRandomGroup().deleteColumns();
        IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
            String header = originHeaders.get(i);
            formatHeaders.add(List.of(header));
        });
        originRows.forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
                Object v = originRow[i];
                row.add(v == null ? 1 : v);
            });
            formatRows.add(row);
        });

        fileRandom.getRandomRowMap().clear();
    }

    private List<SurveyRandomResult> getByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyRandomResult> getByRIds(Long surveyId, List<Long> rIds) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdIn(surveyId, rIds);
    }
}
