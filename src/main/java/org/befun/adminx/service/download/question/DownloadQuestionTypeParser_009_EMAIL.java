package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion_string;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_009_EMAIL implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.EMAIL;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_string(context, group));
        return group;
    }
}
