package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion_int;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_006_SCORE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.SCORE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_int(context, group));
        return group;
    }
}
