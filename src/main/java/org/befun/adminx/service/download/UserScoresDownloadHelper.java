package org.befun.adminx.service.download;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.befun.adminx.entity.CommunityUserScoresDto;
import org.befun.adminx.service.CommunityUserScoresService;
import org.befun.adminx.service.download.dto.UserScoresDownloadDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/8/30 11:07
 */
@Slf4j
@Component
public class UserScoresDownloadHelper {

    @Autowired
    private CommunityUserScoresService communityUserScoresService;


    public void download(HttpServletResponse response, ResourceEntityQueryDto<CommunityUserScoresDto> params) throws Exception {

        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        Date date = new Date();
        String fileName = URLEncoder.encode(String.format("积分日志_%s.xlsx", DateHelper.formatDate(date)), StandardCharsets.UTF_8.toString());
        response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);

        EasyExcel.write(response.getOutputStream(), UserScoresDownloadDto.class)
                .useDefaultStyle(false)
                .sheet("sheet")
                .doWrite(queryData(params));
    }

    private List<UserScoresDownloadDto> queryData(ResourceEntityQueryDto<CommunityUserScoresDto> params) {
        int pageSize = 100000;
        List<UserScoresDownloadDto> data = new ArrayList<>();
        params.setLimit(pageSize);
        params.setSort("id_desc");
        Page<CommunityUserScoresDto> list = communityUserScoresService.findAll(params);
        list.getContent().forEach(score -> {
            UserScoresDownloadDto dto = new UserScoresDownloadDto(score);
            data.add(dto);
        });
        return data;
    }

}
