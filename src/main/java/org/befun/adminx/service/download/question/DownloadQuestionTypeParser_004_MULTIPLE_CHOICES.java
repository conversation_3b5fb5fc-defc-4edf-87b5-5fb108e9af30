package org.befun.adminx.service.download.question;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion_item;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_004_MULTIPLE_CHOICES implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.MULTIPLE_CHOICES;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        List<DownloadColumnQuestion> columns = group.getColumns();
        group.getQuestionItems().forEach(i -> {
            int itemIndex = index.getAndIncrement();
            // 增加选项列，每个选项作为一列
            columns.add(new DownloadColumnQuestion_MULTIPLE_CHOICES_item(context, group, itemIndex, i));
            if (i.getEnableTextInput() != null && i.getEnableTextInput()) {
                // 增加选项中包含文本输入列
                columns.add(new DownloadColumnQuestion_MULTIPLE_CHOICES_comment(context, group, itemIndex, i));
            }
        });
        return group;
    }

    public static class DownloadColumnQuestion_MULTIPLE_CHOICES_item extends DownloadColumnQuestion_item {

        public DownloadColumnQuestion_MULTIPLE_CHOICES_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group, itemIndex, item);
        }

        private boolean hasItem(SurveyResponseCell cell) {
            String s = cell.getStrValue();
            if (StringUtils.isNotEmpty(s)) {
                String[] ss = s.split(";");
                return Arrays.stream(ss).anyMatch(i -> i.equals(item.getValue()));
            }
            return false;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return hasItem(cell) ? 1 : 0;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return hasItem(cell) ? itemText : null;
        }
    }

    public static class DownloadColumnQuestion_MULTIPLE_CHOICES_comment extends DownloadColumnQuestion {

        private final SurveyQuestionItem item;

        public DownloadColumnQuestion_MULTIPLE_CHOICES_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_" + item.getText() + "_文本输入",
                    group.getCode() + "_" + itemIndex + "_text");
            this.item = item;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Map<String, Object> map = JsonHelper.toMap(cell.getCommentValue());
            return map == null ? null : map.get(item.getValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }

    }
}
