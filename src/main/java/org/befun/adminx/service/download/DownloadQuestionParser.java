package org.befun.adminx.service.download;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.BaseQuestion;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.repository.SurveyQuestionRepository;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
public class DownloadQuestionParser {

    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;
    @Autowired(required = false)
    private final List<IDownloadQuestionTypeParser> typeParsers = new ArrayList<>();
    private final Map<QuestionType, IDownloadQuestionTypeParser> parserMap = new HashMap<>();

    @PostConstruct
    public void init() {
        typeParsers.forEach(p -> parserMap.put(p.type(), p));
    }

    public void buildGroup(ResponseDownloadContext context) {
        List<SurveyQuestion> questions = surveyQuestionRepository.findAllBySurvey(context.getSurvey());
        context.setOriginQuestions(questions);
        List<SurveyQuestion> filterAndSortQuestions = filterAndSortQuestions(questions, context.getOriginSortGroups(), context.getOriginSortQuestions());
        context.setFilterAndSortQuestions(filterAndSortQuestions);
        filterAndSortQuestions.forEach(question -> {
            Optional.ofNullable(parserMap.get(question.getType())).ifPresent(parser -> {
                DownloadColumnQuestionGroup group = parser.buildGroup(context, question);
                context.getQuestionGroupMap().put(question.getId(), group);
            });
        });
    }

    private List<SurveyQuestion> filterAndSortQuestions(List<SurveyQuestion> questions, List<SurveyQuestion> originSortGroups, List<SurveyQuestion> originSortQuestions) {
        List<QuestionType> excludeTypes = List.of(
                QuestionType.GROUP,
                QuestionType.MARK,
                QuestionType.SEPARATOR,
                QuestionType.EMPTY,
                QuestionType.MEDIA
        );
        List<SurveyQuestion> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(questions)) {
            List<SurveyQuestion> groupList = new ArrayList<>();
            Map<String, List<SurveyQuestion>> groupMap = new HashMap<>();
            questions.forEach(q -> {
                if (q.getType() == QuestionType.GROUP) {
                    groupList.add(q);
                } else {
                    String groupCode = StringUtils.isEmpty(q.getGroupCode()) ? "" : q.getGroupCode();
                    groupMap.computeIfAbsent(groupCode, k -> new ArrayList<>()).add(q);
                }
            });
            if (!groupList.isEmpty()) {
                groupList.stream().sorted(Comparator.comparing(BaseQuestion::getSequence)).forEach(g -> {
                    originSortGroups.add(g);
                    List<SurveyQuestion> list = groupMap.remove(g.getName());
                    if (CollectionUtils.isNotEmpty(list)) {
                        list.stream().sorted(Comparator.comparing(BaseQuestion::getSequence)).forEach(q -> {
                            originSortQuestions.add(q);
                            if (!excludeTypes.contains(q.getType())) {
                                result.add(q);
                            }
                        });
                    }
                });
            }
            // 没有分组的问题（旧的问卷没有分组），排序后添加到最后
            if (!groupMap.isEmpty()) {
                groupMap.values()
                        .stream()
                        .flatMap(Collection::stream)
                        .sorted(Comparator.comparing(BaseQuestion::getSequence))
                        .forEach(q -> {
                            result.add(q);
                            originSortQuestions.add(q);
                        });
            }
        }
        return result;
    }
}
