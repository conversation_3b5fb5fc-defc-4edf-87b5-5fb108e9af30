package org.befun.adminx.service.download.question;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.download.IDownloadQuestionTypeParser;
import org.befun.adminx.service.download.dto.DownloadColumnQuestion;
import org.befun.adminx.service.download.dto.DownloadColumnQuestionGroup;
import org.befun.adminx.service.download.dto.ResponseDownloadContext;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_027_NPS implements IDownloadQuestionTypeParser {


    @Override
    public QuestionType type() {
        return QuestionType.NPS;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_NPS_score(context, group));
        group.getColumns().add(new DownloadColumnQuestion_NPS_tag(context, group));
        group.getColumns().add(new DownloadColumnQuestion_NPS_comment(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_NPS_score extends DownloadColumnQuestion {

        public DownloadColumnQuestion_NPS_score(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle(),
                    group.getCode() + "_1");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getIntValue();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getIntValue();
        }
    }

    public static class DownloadColumnQuestion_NPS_tag extends DownloadColumnQuestion {

        public DownloadColumnQuestion_NPS_tag(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_标签",
                    group.getCode() + "_2");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return RegularExpressionUtils.replaceHtml(cell.getTags());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return RegularExpressionUtils.replaceHtml(cell.getTags());
        }
    }

    public static class DownloadColumnQuestion_NPS_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_NPS_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_评价文本",
                    group.getCode() + "_3");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

    }
}
