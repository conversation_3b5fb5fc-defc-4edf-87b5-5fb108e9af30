package org.befun.adminx.service.download.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.survey.SurveyResponseCell;

@Getter
@Setter
public class DownloadColumnQuestion_int extends DownloadColumnQuestion {

    public DownloadColumnQuestion_int(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getIntValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getIntValue();
    }
}