package org.befun.adminx.service.quota;

import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

public abstract class AbstractQuotaLimit implements QuotaLimit {


    private static final String QUOTA_SYNC_LOCK = "quota:sync:lock:%s";                 // string surveyId
    private static final String QUOTA_SYNC_PROGRESS = "quota:sync:progress:%s";         // hash surveyId
    private static final String QUOTA_TOKEN = "quota:token:%s:%s";                      // set surveyId quotaId
    private static final String QUOTA_STAT = "quota:stat:%s";                           // hash surveyId
    private static final String QUOTA_STAT_MAX_HASH_KEY = "max:%s";                     // 配额统计信息 QUOTA_STAT 的hashKey
    private static final String QUOTA_STAT_USE_HASH_KEY = "use:%s";                     // 配额统计信息 QUOTA_STAT 的hashKey

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    protected HashOperations<String, String, String> getHashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    protected ValueOperations<String, String> getStringOpt() {
        return stringRedisTemplate.opsForValue();
    }

    protected SetOperations<String, String> getSetOpt() {
        return stringRedisTemplate.opsForSet();
    }

    protected boolean syncLock(Long surveyId) {
        Boolean lock = getStringOpt().setIfAbsent(getQuotaSyncLockKey(surveyId), "1");
        return lock != null && lock;
    }

    protected void requireNotSync(Long surveyId) {
        Boolean lock = stringRedisTemplate.hasKey(getQuotaSyncLockKey(surveyId));
        if (lock != null && lock) {
            throw new BadRequestException(String.format("问卷(%d)正在同步历史数据", surveyId));
        }
    }

    protected void syncUnLock(Long surveyId) {
        stringRedisTemplate.delete(getQuotaSyncLockKey(surveyId));
    }


    protected void requireSurveyId(Long surveyId) {
        if (surveyId == null || surveyId <= 0) {
            throw new BadRequestException("surveyId 不能为空");
        }
    }

    protected void requireResponseId(Long responseId) {
        if (responseId == null || responseId <= 0) {
            throw new BadRequestException("responseId 不能为空");
        }
    }

    protected void requireQuotaId(Long quotaId) {
        if (quotaId == null || quotaId <= 0) {
            throw new BadRequestException("quotaId 不能为空");
        }
    }

    protected String getQuotaSyncLockKey(Long surveyId) {
        return String.format(QUOTA_SYNC_LOCK, surveyId.toString());
    }

    protected String getQuotaSyncProgressKey(Long surveyId) {
        return String.format(QUOTA_SYNC_PROGRESS, surveyId.toString());
    }

    protected String getQuotaTokenKey(Long surveyId, Long quotaId) {
        return String.format(QUOTA_TOKEN, surveyId.toString(), quotaId.toString());
    }

    /**
     * 配额统计hash max:surveyId
     *
     * @param quotaId
     * @return
     */
    protected String getQuotaStatMaxHashKey(Long quotaId) {
        return String.format(QUOTA_STAT_MAX_HASH_KEY, quotaId.toString());
    }

    /**
     * 配额统计hash use:surveyId
     *
     * @param quotaId
     * @return
     */
    protected String getQuotaStatUseHashKey(Long quotaId) {
        return String.format(QUOTA_STAT_USE_HASH_KEY, quotaId.toString());
    }

    protected String getQuotaStatKey(Long surveyId) {
        return String.format(QUOTA_STAT, surveyId.toString());
    }


    protected long unbox(Long l) {
        return l != null ? l : 0;
    }

}
