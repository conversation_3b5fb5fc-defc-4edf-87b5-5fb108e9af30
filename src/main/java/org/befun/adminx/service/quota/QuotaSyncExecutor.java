package org.befun.adminx.service.quota;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.property.QuotaProperties;
import org.befun.adminx.service.quota.bean.QuotaStat;
import org.befun.adminx.service.quota.bean.QuotaSyncProgress;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.IntStream;

@Slf4j
@Service
public class QuotaSyncExecutor {

    @Autowired
    private QuotaProperties quotaProperties;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Async
    public void sync(long surveyId,
                     long count,
                     QuotaStat stat,
                     String progressKey,
                     List<Long> syncQuotas,
                     MatchQuotaResponseFunction match,
                     BiConsumer<Long, Map<Long, List<Long>>> consumerMatchResponseIds,
                     BiConsumer<Boolean, QuotaStat> complete) {
        boolean success = false;
        try {
            int size = quotaProperties.getSizePerPage();
            int maxPage = (int) (count % size > 0 ? (count / size + 1) : (count / size));
            int threads = Math.min(maxPage, quotaProperties.getMaxThreads());
            // 清空历史进度数据
            stringRedisTemplate.delete(progressKey);
            stringRedisTemplate.opsForHash().putAll(progressKey,
                    Map.of(
                            QuotaSyncProgress.HASH_KEY_TOTAL, String.valueOf(count),
                            QuotaSyncProgress.HASH_KEY_TOTAL_PAGE, String.valueOf(maxPage),
                            QuotaSyncProgress.HASH_KEY_THREADS, String.valueOf(threads),
                            QuotaSyncProgress.HASH_KEY_SIZE_PER_PAGE, String.valueOf(size)
                    )
            );
            IntStream.range(0, threads).parallel().forEach(i -> {
                int page = i;
                while (page < maxPage) {
                    long start = System.currentTimeMillis();
                    int p = page + 1;
                    try {
                        consumerMatchResponseIds.accept(surveyId, match.match(surveyId, syncQuotas, p, size));
                    } catch (Throwable e) {
                        log.error("问卷：{}，同步历史答卷配额失败，page={}, size={}", surveyId, p, size);
                        throw new BadRequestException(String.format("问卷：%d，同步历史答卷配额失败，page=%d, size=%d", surveyId, p, size));
                    }
                    page += threads;
                    long costMs = System.currentTimeMillis() - start;
                    stringRedisTemplate.opsForHash().increment(progressKey, QuotaSyncProgress.HASH_KEY_PARSED_PAGE, 1);    // 每处理完一页，自增1
                    stringRedisTemplate.opsForHash().increment(progressKey, QuotaSyncProgress.HASH_KEY_TOTAL_MS, costMs);        // 每处理完一页，增加处理时间
                }
            });
            success = true;
        } catch (Throwable e) {
            log.error("问卷：{}，同步历史答卷配额失败", stat.surveyId, e);
            stringRedisTemplate.delete(progressKey);
            throw new BadRequestException(e.getMessage());
        } finally {
            complete.accept(success, stat);
            // 处理进度数据的过期时间，默认1小时
            if (success) {
                stringRedisTemplate.opsForHash().put(progressKey, QuotaSyncProgress.HASH_KEY_COMPLETED, String.valueOf(1));
                stringRedisTemplate.expire(progressKey, quotaProperties.getProgressExpire());
            }
        }
    }
}
