package org.befun.adminx.service.quota;


import org.befun.adminx.service.quota.bean.QuotaInit;
import org.befun.adminx.service.quota.bean.QuotaStat;
import org.befun.adminx.service.quota.bean.QuotaStatItem;
import org.befun.adminx.service.quota.bean.QuotaSyncProgress;

import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

public interface QuotaLimit {

    /**
     * 同步配额
     * 每次启动问卷的时候，如果开启了配额，则会调用此方法，同步配额
     *
     * @param surveyId      问卷id
     * @param allQuotas     最新的所有的配额（配额id和配额的最大值）
     * @param syncQuotas    需要同步的配额，包括新增的和修改过逻辑的配额（配额id）
     * @param countResponse 需要重新计算配额的答卷总数
     * @param match         计算匹配配额的答卷id
     * @param complete      执行完同步会回调此方法
     */
    boolean syncQuotaStat(Long surveyId,
                          List<QuotaInit> allQuotas,
                          List<Long> syncQuotas,
                          Long countResponse,
                          MatchQuotaResponseFunction match,
                          BiConsumer<Boolean, QuotaStat> complete);

    /**
     * 查询同步配额进度
     */
    Optional<QuotaSyncProgress> syncProgress(Long surveyId);

    /**
     * 查询配额进度
     */
    List<QuotaStatItem> getQuotaStat(Long surveyId);

    /**
     * 查询是否有配额
     */
    boolean hasQuota(Long surveyId, List<Long> matchQuoteIds);

    /**
     * 使用配额，如果容量已满，会失败
     */
    boolean useQuota(Long surveyId, Long responseId, List<Long> matchQuoteIds);

    /**
     * 使用配额，忽略容量，满了也可以使用
     */
    boolean useQuotaIgnoreCapacity(Long surveyId, Long responseId, List<Long> matchQuoteIds);

    /**
     * 删除答卷，回滚配额
     */
    boolean deleteResponse(Long surveyId, Long responseId);

}
