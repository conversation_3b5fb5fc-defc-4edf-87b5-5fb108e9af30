package org.befun.adminx.service.quota.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class QuotaToken {

    public String key;
    public long surveyId;
    public long quotaId;
    public long responseId;
    public long success;

    public QuotaToken(Long surveyId, Long quotaId, Long responseId, String key) {
        this.surveyId = surveyId;
        this.quotaId = quotaId;
        this.responseId = responseId;
        this.key = key;
    }

    /**
     * 成功添加到redis
     */
    public boolean success() {
        return success > 0;
    }


    public QuotaRollback mapToRollback() {
        return new QuotaRollback(key, responseId);
    }
}
