package org.befun.adminx.service.quota.bean;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Map;
import java.util.Optional;

@Getter
@Setter
public class QuotaSyncProgress {

    public static final String HASH_KEY_TOTAL = "total";
    public static final String HASH_KEY_TOTAL_PAGE = "total-page";
    public static final String HASH_KEY_PARSED_PAGE = "parsed-page";
    public static final String HASH_KEY_SIZE_PER_PAGE = "size-per-page";
    public static final String HASH_KEY_THREADS = "threads";
    public static final String HASH_KEY_TOTAL_MS = "total-ms";
    public static final String HASH_KEY_COMPLETED = "completed";

    public boolean completed;
    public long total;
    public long parsed;
    public long totalPage;
    public long parsedPage;
    public long sizePerPage;
    public long threads;
    public long totalMs;
    public long needMs;

    public static Optional<QuotaSyncProgress> parse(Map<String, String> map) {
        if (MapUtils.isNotEmpty(map)) {
            QuotaSyncProgress progress = new QuotaSyncProgress();
            progress.completed = parseToLong(map.get(HASH_KEY_COMPLETED)) == 1;
            progress.total = parseToLong(map.get(HASH_KEY_TOTAL));
            progress.sizePerPage = parseToLong(map.get(HASH_KEY_SIZE_PER_PAGE));
            progress.totalPage = parseToLong(map.get(HASH_KEY_TOTAL_PAGE));
            progress.parsedPage = parseToLong(map.get(HASH_KEY_PARSED_PAGE));
            progress.threads = parseToLong(map.get(HASH_KEY_THREADS));
            progress.totalMs = parseToLong(map.get(HASH_KEY_TOTAL_MS));
            if (progress.threads > 0) {
                progress.totalMs /= progress.threads;
            }
            progress.parsed = progress.sizePerPage * progress.parsedPage;
            if (progress.parsed > progress.total) {
                progress.parsed = progress.total;
            }
            long remainPage = progress.totalPage - progress.parsedPage;
            if (remainPage > 0 && progress.parsedPage > 0) {
                progress.needMs = (long) ((progress.totalMs * 1.0 / progress.parsedPage) * remainPage);
            }
            return Optional.of(progress);
        }
        return Optional.empty();
    }

    protected static long parseToLong(String s) {
        if (NumberUtils.isDigits(s)) {
            return Long.parseLong(s);
        } else {
            return 0;
        }
    }
}
