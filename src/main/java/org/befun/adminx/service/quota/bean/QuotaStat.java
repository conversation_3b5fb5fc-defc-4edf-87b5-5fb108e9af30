package org.befun.adminx.service.quota.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QuotaStat {
    public long surveyId;
    public List<QuotaStatItem> items;


    private static final String HASH_KEY_IDS = "ids";                       // stat hash hashKey ids
    private static final String HASH_KEY_USE = "use:%s";                    // stat hash hashKey use      %s=quotaId
    private static final String HASH_KEY_MAX = "max:%s";                    // stat hash hashKey max      %s=quotaId


    public QuotaStat(long surveyId) {
        this.surveyId = surveyId;
    }

    /**
     * 判断命中的配额是否有已收集完成的，只要有一个配额收满都提前结束
     */
    public boolean hasQuota(List<Long> matchQuoteIds) {
        return matchQuoteIds.stream().anyMatch(quota -> items.stream().anyMatch(i -> i.use >= i.max && i.quotaId == quota.longValue()));
    }

    public Map<String, String> toCache() {
        Map<String, String> cache = new HashMap<>();
        List<String> ids = new ArrayList<>();
        items.forEach(i -> {
            ids.add(String.valueOf(i.quotaId));
            cache.put(getQuotaStatMaxKey(i.quotaId), String.valueOf(i.max));
            cache.put(getQuotaStatUseKey(i.quotaId), String.valueOf(i.use));
        });
        cache.put(HASH_KEY_IDS, String.join(",", ids));
        return cache;
    }

    public static String getQuotaStatMaxKey(Long quotaId) {
        return String.format(HASH_KEY_MAX, quotaId.toString());
    }

    public static String getQuotaStatUseKey(Long quotaId) {
        return String.format(HASH_KEY_USE, quotaId.toString());
    }

    public static QuotaStat init(Long surveyId, List<QuotaInit> allQuotas) {
        QuotaStat r = new QuotaStat(surveyId);
        r.items = allQuotas.stream().map(i -> new QuotaStatItem(i.quotaId, i.max, 0)).collect(Collectors.toList());
        return r;
    }

    public static Optional<QuotaStat> parse(Long surveyId, Map<String, String> stat) {
        if (MapUtils.isNotEmpty(stat)) {
            String ids = stat.get(HASH_KEY_IDS);
            if (StringUtils.isNotEmpty(ids)) {
                QuotaStat r = new QuotaStat(surveyId);
                r.items = Arrays.stream(ids.split(","))
                        .map(QuotaStat::parseToLong)
                        .filter(i -> i > 0)
                        .map(i -> {
                            long max = QuotaStat.parseToLong(stat.get(getQuotaStatMaxKey(i)));
                            long use = QuotaStat.parseToLong(stat.get(getQuotaStatUseKey(i)));
                            return new QuotaStatItem(i, max, use);
                        })
                        .collect(Collectors.toList());
                return Optional.of(r);
            }
        }
        return Optional.empty();
    }

    protected static long parseToLong(String s) {
        if (NumberUtils.isDigits(s)) {
            return Long.parseLong(s);
        } else {
            return 0;
        }
    }
}