package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.SurveyFingerprint;
import org.befun.adminx.entity.SurveyFingerprintDto;
import org.befun.adminx.repository.SurveyFingerprintRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimilarityService extends BaseService<SurveyFingerprint, SurveyFingerprintDto, SurveyFingerprintRepository> {
    /**
     * ctor
     */
    public void SimilarityService() {

    }

    /**
     *
     * @return
     */
    private String convertResponseToString() {
        return "";
    }

    /**
     * 索引新的填答
     */
    public void index() {
        String text = convertResponseToString();
//        Simhash hash = new Simhash(text, 64);

    }

    /**
     * 查询相似填答
     */
    public void query() {

    }
}
