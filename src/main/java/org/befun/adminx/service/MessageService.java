package org.befun.adminx.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.survey.SurveyResponseRollBackDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Map;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageService {
    private final String TOPIC_SURVEY_RESPONSE_ROLLBACK = "survey-response-rollback";

    @Autowired
    private ObjectMapper objectMapper;

//    @Autowired
//    private KafkaTemplate<String, String> kafkaTemplate;

    @Lazy
    @Autowired
    private MessageService messageService;

    /**
     * send survey response rollback to kafka
     *
     * @param dto
     */
    public void notifyRollBackResponse( SurveyResponseRollBackDto dto) {
        log.info("notify to survey_response_rollback kafka for surveyId:{} responseId:{}", dto.getSurveyId(), dto.getResponseId());
//        messageService.send(TOPIC_SURVEY_RESPONSE_ROLLBACK, dto);
    }

    @Async
    protected void send(String topic, BaseDTO dto) {
        try {
            String message = objectMapper.writeValueAsString(dto);
//            kafkaTemplate.send(topic, message);
        } catch (JsonProcessingException ex) {
            log.error("Failed to send survey_response_rollback kafka {}", ex.getMessage());
            ex.printStackTrace();
        }
    }

    @SneakyThrows
    public String buildMessage(TemplateInfoDto template, Map<String, Object> parameters, String url) {
        if(StringUtils.isNotEmpty(url)) {
            URL u = new URL(url);
            // 获取路径
            String path = u.getPath();
            // 如果路径以斜杠结尾，则删除它
            if (path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            // 获取路径最后一个分隔符后面的部分
            String code = path.substring(path.lastIndexOf("/") + 1);
            parameters.put("code",code);
            //获取url后面的code
            return TemplateEngine.renderTextTemplate(template.getExample(), parameters);
        } else {
            throw new BadRequestException("问卷地址不能为空");
        }
    }

}
