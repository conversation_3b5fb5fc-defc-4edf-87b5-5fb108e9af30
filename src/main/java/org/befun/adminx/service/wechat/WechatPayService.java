package org.befun.adminx.service.wechat;


import com.github.binarywang.wxpay.bean.entpay.EntPayRequest;
import com.github.binarywang.wxpay.bean.request.WxPaySendRedpackRequest;
import com.github.binarywang.wxpay.bean.result.WxPaySendRedpackResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import java.util.Arrays;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.BillStates;
import org.befun.adminx.constant.GenerateCodeType;
import org.befun.adminx.constant.WechatDrawCashType;
import org.befun.adminx.entity.WechatDrawCashRecord;
import org.befun.adminx.exception.WechatCallBackException;
import org.befun.adminx.property.SendRedPackProperties;
import org.befun.adminx.property.WechatTransferBillProperties;
import org.befun.adminx.utils.CreateRandomStr;
import org.befun.core.utils.RestUtils;
import org.befun.extension.dto.transfer.TransferBillsCancelResult;
import org.befun.extension.dto.transfer.TransferBillsNotifyResult;
import org.befun.extension.dto.transfer.TransferBillsRequest;
import org.befun.extension.dto.transfer.TransferBillsRequest.TransferSceneReportInfo;
import org.befun.extension.dto.transfer.TransferBillsResult;
import org.befun.extension.service.WeChatPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/3/7 16:12
 */
@Service
@Slf4j
public class WechatPayService {

    @Autowired
    private SendRedPackProperties redPackProperties;

    @Autowired
    private WechatTransferBillProperties wechatTransferBillProperties;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private WechatDrawCashRecordService wechatDrawCashRecordService;

    /**
     * 红包提现*
     *
     * @param request
     * @param openId
     * @param amount
     * @return
     */
    public WxPaySendRedpackResult pay(HttpServletRequest request, String openId, int amount) throws WxPayException {
        WxPaySendRedpackRequest wxPaySendRedpackRequest = new WxPaySendRedpackRequest();
        WxPaySendRedpackResult wxPaySendRedpackResult = null;
        buildWxPaySendRedPackRequest(wxPaySendRedpackRequest, request, openId, amount);
        //发红包
        wxPaySendRedpackResult = weChatPayService.getRedpackService().sendRedpack(wxPaySendRedpackRequest);
        //转账到零钱
//      weChatPayService.getPartnerTransferService().batchTransfer(partnerTransferRequest);

        //记录微信付款记录
        wechatDrawCashRecordService.addRecord(openId, WechatDrawCashType.RED_PACK, amount,
                wxPaySendRedpackResult.getMchBillNo(), wxPaySendRedpackResult.getReturnCode(), wxPaySendRedpackResult.getReturnMsg(), null, null, null);

        return wxPaySendRedpackResult;
    }

    public void entPay(HttpServletRequest request,String openid, int amount){
        try {
            EntPayRequest build = EntPayRequest.newBuilder()
                    .partnerTradeNo(CreateRandomStr.generatorPayNo())
                    .checkName("test")
                    .openid(openid)
                    .amount(amount)
                    .spbillCreateIp(RestUtils.getClientIpAddress(request))
                    .build();
            weChatPayService.getEntPayService().entPay(build);
        } catch (WxPayException e) {
            e.printStackTrace();
        }
    }

    public void buildWxPaySendRedPackRequest(WxPaySendRedpackRequest wxPaySendRedpackRequest, HttpServletRequest request, String openId, int amount) {
        wxPaySendRedpackRequest.setClientIp(RestUtils.getClientIpAddress(request));
        wxPaySendRedpackRequest.setMchBillNo(CreateRandomStr.generatorVerifyCode(28, GenerateCodeType.NUMBER));
        wxPaySendRedpackRequest.setReOpenid(openId);
        wxPaySendRedpackRequest.setNonceStr(CreateRandomStr.generatorPayNo());
        wxPaySendRedpackRequest.setNonceStr(UUID.randomUUID().toString().replace("-", ""));
        wxPaySendRedpackRequest.setTotalAmount(amount);
        wxPaySendRedpackRequest.setTotalNum(redPackProperties.getTotalNum());
        wxPaySendRedpackRequest.setSendName(redPackProperties.getSendName());
        wxPaySendRedpackRequest.setWishing(redPackProperties.getWishing());
        wxPaySendRedpackRequest.setActName(redPackProperties.getActName());
        wxPaySendRedpackRequest.setRemark(redPackProperties.getRemark());
        wxPaySendRedpackRequest.setSceneId(redPackProperties.getSceneId());
        wxPaySendRedpackRequest.setRiskInfo(redPackProperties.getRiskInfo());
    }

    public TransferBillsResult transferBill(String openId, int amount, String billNo) throws WxPayException {
        TransferBillsRequest request = buildTransferBillRequest(openId, amount, billNo);
        return weChatPayService.transferBills(request);
    }

    private TransferBillsRequest buildTransferBillRequest(String openId, int amount, String billNo) {
        TransferBillsRequest request = new TransferBillsRequest();
        request.setAppid(wechatTransferBillProperties.getAppId());
        request.setOpenid(openId);
        request.setTransferSceneId(wechatTransferBillProperties.getTransferSceneId());
        request.setTransferAmount(amount);
        request.setNotifyUrl(wechatTransferBillProperties.getCallBackUrl());
        request.setOutBillNo(billNo);
        request.setTransferRemark(wechatTransferBillProperties.getTransferRemark());

        TransferSceneReportInfo nameInfo = new TransferSceneReportInfo();
        nameInfo.setInfoType("活动名称");
        nameInfo.setInfoContent(wechatTransferBillProperties.getActivityName());
        TransferSceneReportInfo contentInfo = new TransferSceneReportInfo();
        contentInfo.setInfoType("奖励说明");
        contentInfo.setInfoContent(wechatTransferBillProperties.getActivityContent());
        request.setTransferSceneReportInfos(Arrays.asList(nameInfo, contentInfo));
        return request;
    }

    public String generateBillNo() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 20);
    }

    /**
     * 撤销转账并修改记录状态
     * @param record
     * @throws WxPayException
     */
    public void cancelTransferBill(WechatDrawCashRecord record) throws WxPayException {
        TransferBillsCancelResult cancelResult = weChatPayService
                .transformBillsCancel(record.getBillNo());
        record.setStates(BillStates.valueOf(cancelResult.getState()));
        wechatDrawCashRecordService.save(record);
    }

    public TransferBillsNotifyResult transferBillCallBack(HttpServletRequest request) {
        try {
            return weChatPayService
                    .parseTransferBillsNotifyResult(request);
        } catch (Exception ex) {
            throw new WechatCallBackException("FAIL", ex.getMessage());
        }
    }
}
