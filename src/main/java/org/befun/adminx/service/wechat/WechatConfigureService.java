package org.befun.adminx.service.wechat;


import cn.hutool.core.util.XmlUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.WxMpMassOpenIdsMessage;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftInfo;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftList;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.result.WxMpMassSendResult;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.wechat.WechatNotifyRequestDto;
import org.befun.adminx.dto.wechat.WechatResponseDto;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.ReplyMessageService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.WeChatMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description
 */
@Service
@Slf4j
@Getter
@Setter
public class WechatConfigureService {

    @Autowired
    private WeChatMpService weChatMpService;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private ReplyMessageService replyMessageService;

    /**
     * 生成微信授权跳转地址 公众号
     *
     * @param redirectUri
     * @param scope
     * @param state
     * @return
     */
    public String buildQrConnectUrl(String redirectUri, String scope, String state) {
        if (redirectUri.isEmpty() || scope.isEmpty())
            throw new BadRequestException("参数错误");
        return weChatMpService.getOAuth2Service().buildAuthorizationUrl(redirectUri, scope, state);
    }


    /**
     * 通过code获取用户信息 公众号授权
     *
     * @param code
     * @return
     */
    public WxOAuth2AccessToken getAccessToken(String code) {
        Assert.notNull(code,"code is null");
        try {
            WxOAuth2AccessToken accessToken = weChatMpService.getOAuth2Service().getAccessToken(code);
            return accessToken;
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }
    /**
     * 通过code获取用户信息 公众号授权
     * @return
     */
    public String getAccessToken() {
        try {
            String accessToken = weChatMpService.getAccessToken();
            return accessToken;
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 刷新access_token
     *
     * @param refreshToken
     * @return
     */
    public WxOAuth2AccessToken refreshAccessToken(String refreshToken) {
        Assert.notNull(refreshToken,"refreshToken is null");
        try {
           WxOAuth2AccessToken accessToken = weChatMpService.getOAuth2Service().refreshAccessToken(refreshToken);
           return accessToken;
        } catch (Exception e) {
           throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 拉取用户信息
     *
     * @param accessToken
     * @return
     */
    public WxOAuth2UserInfo getWxUserInfo(WxOAuth2AccessToken accessToken) {
        Assert.notNull(accessToken,"accessToken is null");
        try {
            WxOAuth2UserInfo userInfo = weChatMpService.getOAuth2Service().getUserInfo(accessToken, "zh_CN");
            return userInfo;
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 获取微信jsapi参数
     *
     * @param url
     * @return
     */
    public WxJsapiSignature getWxSignature(String url) {
        if(StringUtils.isEmpty(url)) throw new BadRequestException("url不能为空");
        try {
            WxJsapiSignature jsapiSignature = weChatMpService.createJsapiSignature(url);
            return jsapiSignature;
        } catch (WxErrorException e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 微信消息订阅
     * @param xml
     */
    public String notifyMessage(String xml) {
        log.info("后台接收到公众号发来的数据是:\n{}", xml);
        // 把微信发送给后端的xml消息转成WechatNotifyRequestDto对象
        Map<String, Object> map = XmlUtil.xmlToMap(xml);
        WechatNotifyRequestDto wechatNotifyRequestDto = JsonHelper.toObject(map, WechatNotifyRequestDto.class);
        if(wechatNotifyRequestDto == null) return "empty message";
        WechatResponseDto responseDto = new WechatResponseDto();

        switch(wechatNotifyRequestDto.getMsgType()) {
            case "event"://关注、取关事件消息
                responseDto = communityUserService.subscribeEvent(wechatNotifyRequestDto);
                break;
            case "text"://文本消息
                responseDto.setContent(replyMessageService.getReplyMessage(wechatNotifyRequestDto.getContent()));
                responseDto.setCreateTime(new Date().getTime());
                responseDto.setFromUserName(wechatNotifyRequestDto.getToUserName());
                responseDto.setToUserName(wechatNotifyRequestDto.getFromUserName());
                break;
            case "LOCATION"://地理位置消息
                communityUserService.updateUserLocation(wechatNotifyRequestDto);
                responseDto = null;
                break;
            case "image":
            case "voice":
            case "video":
            case "music":
            case "news":
            default:
                responseDto = null;
        }
        if(responseDto == null) {
            responseDto = new WechatResponseDto();
            responseDto.setContent(replyMessageService.getReplyMessage("收到消息"));
            responseDto.setCreateTime(new Date().getTime());
            responseDto.setFromUserName(wechatNotifyRequestDto.getToUserName());
            responseDto.setToUserName(wechatNotifyRequestDto.getFromUserName());
        }
        return XmlUtil.mapToXmlStr(JsonHelper.toMap(responseDto));
    }

    /**
     * 发送模板消息
     * @param message
     * @return
     */
    public String sendTemplateMsg(WxMpTemplateMessage message) {
        if(message == null) return null;
        try {
            String result = weChatMpService.getTemplateMsgService().sendTemplateMsg(message);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * *发送微信图文消息
     * @param draftMediaId
     * @param openidList
     * @return
     */
    public WxMpMassSendResult sendWechatMsg(String draftMediaId, List<String> openidList) {
        try{
            WxMpMassOpenIdsMessage message = new WxMpMassOpenIdsMessage();
            message.setMsgType(WxConsts.MassMsgType.MPNEWS); // 设置消息类型为图文消息
            message.setMediaId(draftMediaId);
            message.setToUsers(openidList);
            WxMpMassSendResult result = weChatMpService.getMassMessageService().massOpenIdsMessageSend(message);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发送客服消息
     * @param message
     * @return
     */
    public Boolean sendKeFuMsg(WxMpKefuMessage message) {
        if(message == null) return null;
        try {
            return weChatMpService.getKefuService().sendKefuMessage(message);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * *获取草稿列表
     * @return
     */
    public WxMpDraftList getDraftList() {
        try {
            return weChatMpService.getDraftService().listDraft(0, 10, 1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * *获取草稿详情
     * @param mediaId
     * @return
     */
    public WxMpDraftInfo getDraftInfo(String mediaId) {
        try {
            return weChatMpService.getDraftService().getDraft(mediaId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取所有关注列表openid
     * @return
     */
    public List<String> getAllOpenid() {
        List<String> result = new ArrayList<>();
        try {
            String nextOpenid = null;
            boolean hasNext = true;
            int maxOpenidNum = 10000;//每次最多拉10000
            while (hasNext){
                WxMpUserList wxMpUserList = weChatMpService.getUserService().userList(nextOpenid);
                result.addAll(wxMpUserList.getOpenids());
                //拉去数量小于10000直接返回,大于继续获取
                if(wxMpUserList.getCount() < maxOpenidNum){
                    hasNext = false;
                }else {
                    nextOpenid = wxMpUserList.getNextOpenid();
                }
            }
            return result;
        } catch (WxErrorException e) {
            log.error("获取关注列表失败：{}",e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
}
