package org.befun.adminx.service.wechat;


import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.external.contact.ExternalContact;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactBatchInfo;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.adminx.entity.UnionId;
import org.befun.adminx.repository.UnionIdRepository;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.property.WeChatCpProperty;
import org.befun.extension.service.WeChatCpService;
import org.befun.extension.service.WeChatMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.ExternalContact.UNIONID_TO_EXTERNAL_USERID;


@Service
@Slf4j
@ConditionalOnBean({WeChatCpProperty.class,WeChatCpService.class})
public class WechatCpConfigService {
    @Autowired
    private WeChatCpService weChatCpService;
    @Autowired
    private WeChatMpService weChatMpService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UnionIdRepository unionIdRepository;
    @Autowired
    private WeChatCpProperty weChatCpProperty;
    @Autowired
    private WechatCpHandler wechatCpHandler;


    @Scheduled(cron = "${befun.extension.wechat-cp.union-id-cron}")
    public void unionIdCron() {
        log.info("unionIdCron start");
        try{
            wechatCpHandler.setup();
            syncCommunityUserUnionId(null);
            syncWechatCpUserUnionId();
        }catch (Exception e){
            log.error("unionIdCron error", e);
        }
    }


    public void syncCommunityUserUnionId(String whereCondition) {
        log.info("start to get new community user union id with where condition: {}", whereCondition);
        try {
            int pageSize = 1000; // 每页大小
            int pageNumber = 1; // 当前页码
            boolean hasMoreData = true; // 是否还有更多数据
            String where = StringUtils.isNotBlank(whereCondition) ?  whereCondition : "";

            while (hasMoreData) {
                // 构建分页查询语句
                String sql = String.format("SELECT u.openid FROM `community_users` u LEFT JOIN `union_id` ui ON u.openid = ui.openid where ui.openid is null %s LIMIT ?, ?",
                        where);
                List<String> openids = jdbcTemplate.queryForList(sql, String.class, (pageNumber - 1) * pageSize, pageSize);
                log.info("unionIdCron openids: {}", openids.size());
                if (openids.isEmpty()) {
                    hasMoreData = false; // 如果没有数据，退出循环
                } else {
                    // 把openids按照100个一组进行批量查询
                    for (int i = 0; i < openids.size(); i += 100) {
                        try {
                            List<String> subList = openids.subList(i, Math.min(i + 100, openids.size()));
                            // parallelStream线程安全
                            ConcurrentLinkedDeque<UnionId> addList = new ConcurrentLinkedDeque<>();
                            weChatMpService.getUserService().userInfoList(subList).parallelStream().forEach(user -> {
                                log.info("syncCommunityUserUnionId openid: {} unionid: {}", user.getOpenId(), user.getUnionId());
                                Optional.ofNullable(user.getUnionId()).ifPresent(uid -> {
                                    Pair<String, String> externalUserId = Pair.of(null, null);
                                    addList.add(new UnionId(user.getUnionId(), user.getOpenId(), externalUserId.getLeft(), externalUserId.getRight()));
                                });
                            });

                            if (!addList.isEmpty()) {
                                unionIdRepository.saveAll(addList);
                            }
                        }catch (Exception e){
                            log.info("syncCommunityUserUnionId user list error", e);
                        }
                    }
                    pageNumber++; // 增加页码
                }
            }
        } catch (Exception e) {
            log.error("addUnionId error", e);
        }
        log.info("end to get new community user union id");
    }




    public void syncWechatCpUserUnionId() {
        log.info("start to get new wechat-cp user union id");
        try {
            wechatCpHandler.setup();
            weChatCpService.getExternalContactService().listFollowers().forEach(follower -> {
                log.info("syncWechatCpUserUnionId follower: {}", follower);
                try {
                    getUnionIdAndExternalUserId(follower).forEach((k,v)->{
                        String unionId = k;
                        String externalUserId = v;
                        Optional.ofNullable(unionIdRepository.findTopOneByUnionId(unionId)).ifPresent(u->{
                            wechatCpHandler.tagCpUser(follower, externalUserId, u.getOpenId());
                            u.setExternalUserId(externalUserId);
                            u.setUserId(follower);
                            unionIdRepository.save(u);
                        });
                    });
                } catch (Exception e) {
                    log.error("wechat cp user:{} listExternalContacts error", follower, e);
                }
            });


        }catch (Exception e){
            log.error("syncWechatCpUserUnionId2 error", e);
        }
        log.info("end to get new wechat-cp user union id");
    }

    // 通过unionid获取eternal_userid/pending_id
    public Pair<String, String> getEternalUserId(String unionId, String openId) {
        Pair<String, String> id = Pair.of(null, null);
        try {
            String body = JsonHelper.toJson(Map.of("unionid", unionId, "openid", openId));
            String url = weChatCpService.getWxCpConfigStorage().getApiUrl(UNIONID_TO_EXTERNAL_USERID);
            String responseContent = weChatCpService.post(url, body);
            log.info("unionid_to_external_userid url: {}, body: {} , response: {}", url, body, responseContent);
            Map<String, Object> tmpJson = JsonHelper.toMap(responseContent);
            String externalUserid = Objects.toString(tmpJson.get("external_userid"), null);
            String pendingId = Objects.toString(tmpJson.get("pending_id"), null);
            log.info("unionId: {}, openId: {}, eternalUserId: {}, pendingId: {}", unionId, openId, externalUserid, pendingId);
            id = Pair.of(externalUserid, pendingId);
        }catch (Exception e){
            log.error("wechat-cp 获取external_userid/pending_id失败", e);
        }
        return id;
    }

    public String getUnionIdByExternalUserId(String externalUserId){
        String unionId = null;
        try {
            ExternalContact user = weChatCpService.getExternalContactService().getContactDetail(externalUserId, null).getExternalContact();
            unionId = user.getUnionId();
        }catch (Exception e){
            log.error("wechat-cp 获取unionId失败", e);
        }
        return unionId;
    }

    public ConcurrentHashMap<String, String> getUnionIdAndExternalUserId(String follower) {
        // 线程安全
        ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
        try {
            String cursor = null;
            Integer size = 100;
            WxCpExternalContactBatchInfo externalUserInfo;

            do {
                // 获取批量联系人详情
                externalUserInfo = weChatCpService.getExternalContactService()
                        .getContactDetailBatch(new String[]{follower}, cursor, size);
                List<WxCpExternalContactBatchInfo.ExternalContactInfo> externalUser = externalUserInfo.getExternalContactList();

                if (externalUser != null && !externalUser.isEmpty()) {
                    externalUser.parallelStream().forEach(user -> {
                        ExternalContact externalContact = user.getExternalContact();
                        String externalUserId = externalContact.getExternalUserId();
                        String unionId = externalContact.getUnionId();
                        if (StringUtils.isNotEmpty(unionId)) {
                            log.info("follower: {}, unionId: {}, externalUserId: {}", follower, unionId, externalUserId);
                            map.put(unionId, externalUserId);
                        }
                    });
                }

                cursor = externalUserInfo.getNextCursor();
            } while (StringUtils.isNotEmpty(cursor));
        } catch (Exception e) {
            log.error("wechat-cp 获取external_user_info失败", e);
        }

        log.info("follower: {}, externalUser size: {}", follower, map.size());
        return map;
    }
}
