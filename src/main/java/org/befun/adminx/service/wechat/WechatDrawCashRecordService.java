package org.befun.adminx.service.wechat;

import java.util.Arrays;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.BillStates;
import org.befun.adminx.constant.WechatDrawCashType;
import org.befun.adminx.entity.WechatDrawCashRecord;
import org.befun.adminx.entity.WechatDrawCashRecordDto;
import org.befun.adminx.repository.WechatDrawCashRecordRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/3/10 14:59
 */
@Service
@Slf4j
public class WechatDrawCashRecordService extends BaseService<WechatDrawCashRecord, WechatDrawCashRecordDto, WechatDrawCashRecordRepository> {

    /**
     * * 新增微信打款记录
     * @param openId
     * @param type
     * @param amount
     * @param mchBillNo
     * @param returnCode
     * @param returnMsg
     */
    public WechatDrawCashRecord addRecord(String openId, WechatDrawCashType type, int amount, String mchBillNo,
            String returnCode, String returnMsg, String billNo, String packageInfo, BillStates states) {
        if(type == null) return null;
        WechatDrawCashRecord record = new WechatDrawCashRecord(openId, type, amount, mchBillNo, returnCode, returnMsg, billNo, packageInfo, states);
        repository.save(record);
        return record;
    }

    /**
     * 获取用户处理中的提现转账记录
     * @param openId
     * @return
     */
    public Optional<WechatDrawCashRecord> findProcessingTransferBillRecord(String openId) {
        return repository.findFirstByOpenIdAndTypeAndStatesNotIn(openId, WechatDrawCashType.TRANSFER_BILL,
                Arrays.asList(BillStates.SUCCESS, BillStates.FAIL, BillStates.CANCELLED));
    }

    /**
     * 获取账单号对应的提现转账记录
     */
    public Optional<WechatDrawCashRecord> findTransferBillRecordByBillNo(String billNo, String openId) {
        return repository
                .findFirstByOpenIdAndBillNo(openId, billNo);
    }
}
