package org.befun.adminx.service.wechat;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.entity.WechatSubscribeRecord;
import org.befun.adminx.entity.WechatSubscribeRecordDto;
import org.befun.adminx.repository.WechatSubscribeRecordRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/16 14:40
 */
@Service
@Slf4j
public class WechatSubscribeService extends BaseService<WechatSubscribeRecord, WechatSubscribeRecordDto, WechatSubscribeRecordRepository> {

    /**
     * *新增关注取关记录
     * @param cuid
     * @param openid
     */
    public void addRecord(Long cuid, String openid, Boolean type) {
        if(type == null) return;
        WechatSubscribeRecord record = new WechatSubscribeRecord(cuid, openid, type);
        repository.save(record);
    }

    /**
     * 根据openid获取关注记录*
     * @param openId
     * @return
     */
    public WechatSubscribeRecordDto getLastSubscribeByOpenId(String openId) {
        if(StringUtils.isEmpty(openId)) return null;
        Optional<WechatSubscribeRecord> ops = repository.findFirstByOpenIdOrderByIdDesc(openId);
        return ops.map(this::mapToDto).orElse(null);
    }
}
