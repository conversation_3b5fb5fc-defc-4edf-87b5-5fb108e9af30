package org.befun.adminx.service.wechat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalTagGroupList;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.befun.adminx.entity.CommunityUserAdditional;
import org.befun.adminx.property.WechatCpCommunityProperties;
import org.befun.adminx.repository.CommunityUserAdditionalRepository;
import org.befun.adminx.repository.CommunityUserRepository;
import org.befun.adminx.repository.UnionIdRepository;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.UnionIdService;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.WeChatCpService;
import org.befun.extension.service.WeChatMpService;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@ConditionalOnBean(value = WeChatCpService.class)
public class WechatCpHandler {

    @Autowired
    private WeChatCpService weChatCpService;
    @Autowired
    private UnionIdRepository unionIdRepository;
    @Autowired
    private WeChatMpService weChatMpService;
    @Autowired
    private WechatCpConfigService wechatCpConfigService;
    @Autowired
    private WechatCpCommunityProperties wechatCpCommunityProperties;
    @Autowired
    private CommunityUserRepository communityUserRepository;
    @Autowired
    private CommunityUserAdditionalRepository communityUserAdditionalRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    private final String syncCommunityUserUnionIdLock = "syncCommunityUserUnionIdLock";

    private final HashMap<String, List<WxCpUserExternalTagGroupList.TagGroup.Tag>> tagMap = new HashMap<>();

    @PostConstruct
    public void setup() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> communityTag = objectMapper.convertValue(wechatCpCommunityProperties.getCommunity(), Map.class);
            weChatCpService.getExternalContactService().getCorpTagList(new String[]{}).getTagGroupList()
                    .stream().filter(group-> communityTag.containsValue(group.getGroupName()))
                    .forEach(
                    group -> {
                        tagMap.put(group.getGroupName(), group.getTag());
                        log.info("init community tagMap {}", group.getGroupName());
                    });
        } catch (Exception e) {
            log.error("init community tagMap error", e);
        }
    }

    /**
     * {
     *   "allFieldsMap" : {
     *     "ChangeType" : "add_external_contact",
     *     "UserID" : "JiangJiaWei",
     *     "CreateTime" : "1735787910",
     *     "WelcomeCode" : "Cp6IEJcb8VkmFQSVkfTzNrC2QaMW3SJyYoOn7GsW-os",
     *     "Event" : "change_external_contact",
     *     "ExternalUserID" : "wmdizQCwAAaOrz5DYOd6rzpVW2prC_Iw",
     *     "ToUserName" : "ww765695cd4aa437d5",
     *     "FromUserName" : "sys",
     *     "MsgType" : "event"
     *   },
     *   "agentId" : "1000206",
     *   "toUserName" : "ww765695cd4aa437d5",
     *   "fromUserName" : "sys",
     *   "createTime" : 1735787910,
     *   "msgType" : "event",
     *   "event" : "change_external_contact",
     *   "changeType" : "add_external_contact",
     *   "userId" : "JiangJiaWei",
     *   "externalUserId" : "wmdizQCwAAaOrz5DYOd6rzpVW2prC_Iw",
     *   "welcomeCode" : "Cp6IEJcb8VkmFQSVkfTzNrC2QaMW3SJyYoOn7GsW-os",
     *   "extAttrs" : {
     *
     *
     *
     *
     *
     *
     *     "items" : [ ]
     *   },
     *   "scanCodeInfo" : { },
     *   "sendPicsInfo" : {
     *     "picList" : [ ]
     *   },
     *   "sendLocationInfo" : { },
     *   "approvalInfo" : { }
     * }
     */
    // 添加微信客户事件
    public void addWeChatUserHandler(WxCpXmlMessage message) {
        log.info("addWeChatUserHandler " +
                        "agentId: {}, " +
                        "toUserName: {}, " +
                        "fromUserName: {}, " +
                        "createTime: {}, " +
                        "msgType: {}, " +
                        "event: {}, " +
                        "changeType: {}, " +
                        "userId: {}, " +
                        "externalUserId: {}",
                message.getAgentId(),
                message.getToUserName(),
                message.getFromUserName(),
                message.getCreateTime(),
                message.getMsgType(),
                message.getEvent(),
                message.getChangeType(),
                message.getUserId(),
                message.getExternalUserId()
        );

        try {
            String externalUserId = message.getExternalUserId();
            String unionId = wechatCpConfigService.getUnionIdByExternalUserId(externalUserId);
            log.info("add user externalUserId: {}, unionId: {}", externalUserId, unionId);

            if(Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(syncCommunityUserUnionIdLock, "true", Duration.ofDays(1)))){
                log.info("addWeChatUserHandler syncCommunityUserUnionId without lock");
                wechatCpConfigService.syncCommunityUserUnionId(String.format("and u.create_time>'%s'", (LocalDate.now()).minusDays(1)));
            }
            Optional.ofNullable(unionIdRepository.findTopOneByUnionId(unionId)).ifPresent(u -> {
                Optional.ofNullable(u.getOpenId()).ifPresent(openId -> {
                    tagCpUser(message.getUserId(), externalUserId, openId);
                });
                u.setExternalUserId(externalUserId);
                u.setUserId(message.getUserId());
                unionIdRepository.save(u);

            });

        } catch (Exception e) {
            log.error("addWeChatUserHandler error", e);
        }

    }

    public void tagCpUser(String userId, String externalUserId, String openId){
        try {
            final ArrayList<String> addIds = new ArrayList<>();
            final ArrayList<String> removeIds = new ArrayList<>();
            log.info("tag cp user:{} , externalUserId: {}, openId: {}", userId, externalUserId, openId);
            communityUserRepository.findByOpenId(openId)
                    .flatMap(communityUser -> communityUserAdditionalRepository.findById(communityUser.getId()))
                    .ifPresent(communityUserAdditional -> {
                        WechatCpCommunityProperties.Community community = wechatCpCommunityProperties.getCommunity();
                        tagSex(communityUserAdditional, community, addIds::add, removeIds::add);
                        tagRegion(communityUserAdditional, community, addIds::add, removeIds::add);
                        tagEducation(communityUserAdditional, community, addIds::add, removeIds::add);
                        tagBirthYear(communityUserAdditional, community, addIds::add, removeIds::add);
                    });
            if (!addIds.isEmpty()){
                log.info("addWeChatUserHandler openId: {}, addIds: {} , removeTags length:{}", openId, JsonHelper.toJson(addIds), removeIds.size());
                tagUser(userId, externalUserId, addIds.toArray(new String[0]), removeIds.toArray(new String[0]));
            }

        } catch (Exception e) {
            log.error("get user by openid error", e);
        }
    }

    public void tagUser(String userId, String externalUserId, String[] addTag, String[] removeTag) {
        try {
            log.info("tagUser userId: {}, externalUserId: {}, addTag: {}, removeTag length:{}", userId, externalUserId, Arrays.toString(addTag), removeTag.length);
            weChatCpService.getExternalContactService().markTag(userId, externalUserId, addTag, removeTag);
        } catch (Exception e) {
            log.error("tagUser error", e);
        }
    }

    private void tagSex(CommunityUserAdditional communityUserAdditional, WechatCpCommunityProperties.Community community, Consumer<String> addId, Consumer<String> removeId) {
        // 性别处理
        List<WxCpUserExternalTagGroupList.TagGroup.Tag> sexTags = tagMap.get(community.getSexTagName());

        Optional.ofNullable(communityUserAdditional.getGender()).ifPresent(genderName ->{
            Optional.ofNullable(sexTags).ifPresent(tags->{
                tags.forEach(t->{
                    if(genderName.equals(t.getName())){
                        addId.accept(t.getId());
                    }else {
                        removeId.accept(t.getId());
                    }
                });
            });
        });
    }

    private void tagRegion(CommunityUserAdditional communityUserAdditional, WechatCpCommunityProperties.Community community,Consumer<String> addId, Consumer<String> removeId) {
        // 地区（省、市）处理

        List<WxCpUserExternalTagGroupList.TagGroup.Tag> provinceCityTagList = tagMap.get(community.getRegionTagName());

        Optional.ofNullable(tagMap.get(community.getRegionTagName())).ifPresent(regionTagList -> {
            regionTagList.forEach(t->{
                if(Stream.of(communityUserAdditional.getProvince(), communityUserAdditional.getCity())
                        .filter(Objects::nonNull)
                        .anyMatch(location -> t.getName().substring(0,2).contains(location.substring(0,2)))){
                    addId.accept(t.getId());
                }else {
                    removeId.accept(t.getId());
                }
            });
        });
    }

    private void tagEducation(CommunityUserAdditional communityUserAdditional, WechatCpCommunityProperties.Community community,Consumer<String> addId, Consumer<String> removeId){
        // 学历处理
        Optional.ofNullable(tagMap.get(community.getEducationTagName())).ifPresent(educationTagList -> {
            educationTagList.forEach(t->{
                if(Objects.toString(communityUserAdditional.getEducation(), "").equals(t.getName())){
                    addId.accept(t.getId());
                }else {
                    removeId.accept(t.getId());
                }
            });
        });
    }

    private void tagBirthYear(CommunityUserAdditional communityUserAdditional, WechatCpCommunityProperties.Community community,Consumer<String> addId, Consumer<String> removeId) {
        // 出生年
        Optional.ofNullable(tagMap.get(community.getBirthYearTagName())).ifPresent(birthYearTagList -> {
            Optional.ofNullable(communityUserAdditional.getBirthday())
                    .map(DateFormatter::convertToYear)
                    .ifPresent(year -> {
                        String yearName = year.toString();

                        if (year <= community.getBirthYearTagBefore()) {
                            yearName = year + "及以前";
                        }

                        if (year >= community.getBirthYearTagAfter()) {
                            yearName = year + "及以后";
                        }
                        String finalYearName = yearName;
                        birthYearTagList.forEach(t -> {
                            if (t.getName().equals(finalYearName)) {
                                addId.accept(t.getId());
                            } else {
                                removeId.accept(t.getId());
                            }
                        });
                    });
        });
    }


}
