package org.befun.adminx.service;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.text.StringSubstitutor;
import org.befun.adminx.constant.backup.BackupLogType;
import org.befun.adminx.dto.backup.BackupDiffResultDto;
import org.befun.adminx.dto.backup.BackupRecordDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.repository.BackupAccountRepository;
import org.befun.adminx.repository.BackupLogRepository;
import org.befun.adminx.repository.FeatureRepository;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.task.annotation.TaskLock;
import org.hibernate.Session;
import org.hibernate.jdbc.ReturningWork;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 10:00
 */
@Service
@Slf4j
public class FeatureService extends BaseService<Feature, FeatureDto, FeatureRepository> {

	@Override
	public <S extends BaseEntityDTO<Feature>> FeatureDto create(S data) {
		return super.create(data);
	}

	@Override
	public <S extends BaseEntityDTO<Feature>> FeatureDto updateOne(long id, S change) {
		FeatureDto dto = (FeatureDto) change;
		Double awardPercent = dto.getAwardPercent();
		if (dto.getAwardPercent() != null && (awardPercent > 1 || awardPercent < 0)) {
			throw new BadRequestException("奖励系数只能是0-100");
		}
		return super.updateOne(id, change);
	}
}
