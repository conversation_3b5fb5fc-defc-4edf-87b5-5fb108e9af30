package org.befun.adminx.service;

import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.*;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.adminx.dto.community.CheckTaskDto;
import org.befun.adminx.dto.event.EventType;
import org.befun.adminx.dto.event.SurveyResponseViewDto;
import org.befun.adminx.dto.ext.*;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.adminx.dto.survey.SurveyPlusResponseMessageDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.dto.survey.SurveyResponseRollBackDto;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.dto.task.TaskFenceDto;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderStartEnd;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.exception.SurveyErrorCode;
import org.befun.adminx.exception.SurveyErrorException;
import org.befun.adminx.repository.*;
import org.befun.adminx.service.audit.AuditContent;
import org.befun.adminx.service.quota.QuotaAllMatchLimit;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.adminx.task.TemplateSendExecutor;
import org.befun.adminx.trigger.AdminxEventTrigger;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;



/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeliveryService extends BaseService<DeliveryTask, DeliveryTaskDto, DeliveryTaskRepository> {


    @Autowired
    private AuditRecordRepository auditRecordRepository;

    @Autowired
    private SurveyCompletedRecordRepository completedRecordRepository;

    @Autowired
    private DeliveryTaskRepository deliveryTaskRepository;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private CommunityUserScoresService communityUserScoresService;

    @Autowired
    private SurveyCompletedRecordService surveyCompletedRecordService;

    @Autowired
    private AuditRepository auditRepository;

    @Autowired
    private AuditService auditService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyQuotaRepository surveyQuotaRepository;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private QuotaService quotaService;

    @Autowired
    private QuotaAllMatchLimit quotaAllMatchLimit;
    private CommunityGridRepository communityGridRepository;

    @Lazy
    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private HybridSurveyService hybridSurveyService;

    @Autowired
    private SendRecordService sendRecordService;

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;

    @Autowired
    private SurveySendRecordService surveySendRecordService;

    @Autowired
    private SurveyLinkService linkService;

    @Autowired
    private AdminxQuotaRepository adminxQuotaRepository;

    @Autowired
    private AdminxEventTrigger adminxEventTrigger;

    @Autowired
    private WechatConfigureService wechatConfigureService;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private TemplateSendExecutor TemplateSendExecutor;

    @Value("${befun.admin.xm-plus-url}")
    private String xmPlusUrl;

    @Autowired
    private FeatureRepository featureRepository;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Value("${community.uri.response-quota-rollback}")
    private String responseQuotaRollback;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public <S extends BaseEntityDTO<DeliveryTask>> DeliveryTaskDto create(S data) {
        String surveyId = ((DeliveryTaskDto) data).getSid();
        List<DeliveryTask> taskList = repository.findOneBySidAndTaskTypeAndStatusIn(surveyId,((DeliveryTaskDto) data).getTaskType(), Arrays.asList(DeliveryTaskStatus.WAIT_SEND,DeliveryTaskStatus.PAUSE,DeliveryTaskStatus.COLLECTING));
        if(taskList.size() > 0 ){
            throw new BadRequestException("待发送或者进行中的问卷，请勿重复创建任务");
        }
        return super.create(data);
    }

    @Override
    public Page<DeliveryTaskDto> findAll(ResourceEntityQueryDto<DeliveryTaskDto> queryDto) {
        queryDto.setSorts(Sort.by("id").descending());
        if(queryDto.getQueryCriteriaList().size() > 0) {
            queryDto.getQueryCriteriaList().stream().forEach(query ->{
                if(query.getKey().equals("title"))
                    query.setOperator(QueryOperator.LIKE);
            });
        }
        Page<DeliveryTaskDto> deliveryTaskDtos = super.findAll(queryDto);

        deliveryTaskDtos.getContent().forEach(task->{
            if(StringUtils.isNumeric(task.getSid())){
                Optional<Survey> survey = surveyRepository.findById(Long.parseLong(task.getSid()));
                if(survey.isPresent()){
                    task.setEnableAdminxQuota(survey.get().getEnableAdminxQuota());
                }
            }
        });

        return deliveryTaskDtos;
    }

    @Override
    protected void afterMapToDto(List<DeliveryTask> entity, List<DeliveryTaskDto> dto) {
        super.afterMapToDto(entity, dto);
        dto.forEach(task ->{
            task.setSurveyStatus(hybridSurveyService.getSurveyStatus(task.getType(), task.getSid()));
            if(StringUtils.isEmpty(task.getTitle())) task.setTitle(task.getName());
            SendRecord record = sendRecordService.getSendRecordByTaskId(task.getId());
            if(record != null) {
                task.setEnablePush(record.getEnablePush());
                task.setSendType(record.getSendType());
                task.setBlackList(StringUtils.isEmpty(record.getBlackList()) ? null : record.getBlackList().replace(",","\n"));
                task.setWhiteList(StringUtils.isEmpty(record.getWhiteList()) ? null : record.getWhiteList().replace(",", "\n"));
                task.setSamples(JsonHelper.toList(record.getRules(), SamplesDto.class));
            }
        });
    }

    @Override
    public Boolean deleteOne(long id) {
        DeliveryTask task = require(id);
        //如果渠道id不为空 设置渠道状态为回收中
        if(task.getChannelId() != 0L && task.getChannelId() != null)
            channelService.updateChannelStatus(task.getChannelId(), ChannelStatus.COMPLETE);
        return super.deleteOne(id);
    }

//    /**
//     * 根据上传CSV审核
//     */
//    public AuditResponseDto auditPassByCsv(DeliveryTask task, List<AuditRequestItemDto> auditRequestItems) {
//
//        try {
//            // 保存审核通过记录
//            auditRequestItems.stream().forEach(items -> deliveryService.auditPass(task, items.getResponseId(), items.getOpenId(), new AuditResultJsonDto()));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//        return AuditResponseDto.builder()
//                .message("need to be implemented")
//                .build();
//    }

    public DeliveryTask getDeliveryTask(String surveyId, String trackId) {
        if (StringUtils.isNotEmpty(trackId)){
            return require(Long.parseLong(trackId));
        } else {
            return deliveryTaskRepository.findFirstBySid(surveyId, Sort.by("id").descending()).get();
        }
    }

    /**
     * 根据上传Text content审核
     */
    public AuditResponseDto auditPassByContent(DeliveryTaskDto taskDto, String[] ids) {

        try {
            // 保存审核通过记录
            Arrays.stream(ids).filter(id -> !id.equals("")).forEach(id ->
                    deliveryService.auditPass(taskDto, Long.parseLong(id), null, new AuditResultJsonDto()));
        }catch (Exception e){
            e.printStackTrace();
        }
        return AuditResponseDto.builder()
                .message("need to be implemented")
                .build();
    }

//    /**
//     * 根据上传CSV审核
//     */
//    public AuditResponseDto auditFailByCsv(DeliveryTask task, List<AuditRequestItemDto> auditRequestItems) {
//        try {
//            // 保存未审核通过记录
//            auditRequestItems.stream().forEach(items -> deliveryService.auditFail(task, items.getResponseId(), items.getOpenId(), new AuditResultJsonDto()));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//
//        return AuditResponseDto.builder()
//                .message("need to be implemented")
//                .build();
//    }

    /**
     * 根据上传Text content审核失败
     */
    public AuditResponseDto auditFailByContent(DeliveryTaskDto taskDto, String[] ids) {

        try {
            // 保存审核通过记录
            Arrays.stream(ids).filter(id -> !id.equals("")).forEach(id ->
                    deliveryService.auditFail(taskDto, Long.parseLong(id), null, new AuditResultJsonDto()));
        }catch (Exception e){
            e.printStackTrace();
        }
        return AuditResponseDto.builder()
                .message("need to be implemented")
                .build();
    }

    /**
     * 审核通过业务逻辑
     * @param taskDto
     * @param responseId
     * @param openId
     */
    @Transactional
    public AuditRecord auditPass(DeliveryTaskDto taskDto, Long responseId, String openId, AuditResultJsonDto auditResultJsonDto) {
        if(taskDto == null || responseId == null) {
            deliveryService.auditError(taskDto.getSid(), responseId,"responseId is null");
            return null;
        }

        Optional<AuditRecord> optionalAuditRecord = auditRecordRepository.findAllByTaskIdAndResponseId(taskDto.getId(), responseId);
        //如果已经审核过了 不再重复审核
        if(optionalAuditRecord.isPresent()) return null;
        Optional<SurveyCompletedRecord> completedRecord = completedRecordRepository.findFirstBySidAndResponseIdOrderByIdDesc(taskDto.getSid(), responseId);

        if (openId == null) {
            if(!completedRecord.isPresent()) return null;
            openId = completedRecord.get().getOpenId();
        }

        Long shareUserId = null;
        if (completedRecord.isPresent()) shareUserId = completedRecord.get().getInviteId();

        //增加用户可用积分和总积分 减去冻结积分
        CommunityUser communityUser = communityUserService.addUserScore(openId, ScoreType.F_P, taskDto, shareUserId);
        if(communityUser == null) {
            deliveryService.auditError(taskDto.getSid(), responseId,"communityUser is not exists");
            return null;
        }

        //给邀请人加转介绍费用
        communityUserService.addInviteAward(communityUser, taskDto);

        //添加审核记录
        AuditRecord auditRecord = new AuditRecord(responseId, openId, communityUser.getId(), taskDto);
        auditRecordRepository.save(auditRecord);

        //增加已审核数量
        taskDto.getEntity().setAuditCount(taskDto.getEntity().getAuditCount() + 1);
        //减少待审核数量
        int auditWaitCount = Math.max(taskDto.getEntity().getAuditWaitCount() - 1, 0);
        taskDto.getEntity().setAuditWaitCount(auditWaitCount);
        deliveryTaskRepository.save(taskDto.getEntity());

        //更新答卷审核状态
        auditResultJsonDto.setPoints(taskDto.getScore());
        responseService.updateAuditStatus(responseId, ResponseStatus.FINAL_SUBMIT, auditResultJsonDto);
        log.info("audit success,surveyId:{},responseId:{},openId:{}", taskDto.getSid(), responseId, openId);

        this.updateResponseNum(Long.parseLong(taskDto.getSid()),1);

        //异步更新问卷统计数据
        statisticsService.updateAuditData(responseId, communityUser.getId(), ResponseStatus.FINAL_SUBMIT);

        //追访任务 发通知告知用户
        sendRecordService.sendAuditPassNotice(taskDto,communityUser);
        return auditRecord;
    }

    public void updateResponseNum(long surveyId, int add) {
        String sql = String.format("update cem_platform.survey set response_finish_num = IF( response_finish_num IS NULL, 0, response_finish_num ) + (%s) where id = %s", add, surveyId);
        jdbcTemplate.update(sql);
    }

    /**
     * 审核不通过业务逻辑
     * @param taskDto
     * @param responseId
     * @param openId
     */
    @Transactional
    public AuditRecord auditFail(DeliveryTaskDto taskDto, Long responseId, String openId, AuditResultJsonDto auditResultJsonDto) {
        if(taskDto == null || responseId == null) {
            deliveryService.auditError(taskDto.getSid(), responseId,"responseId is null");
            return null;
        }
        Optional<AuditRecord> optionalAuditRecord = auditRecordRepository.findAllByTaskIdAndResponseId(taskDto.getId(), responseId);
        //如果已经审核过了 不再重复审核
        if(optionalAuditRecord.isPresent()) return null;

        if(openId == null) {
            Optional<SurveyCompletedRecord> completedRecord = completedRecordRepository.findFirstBySidAndResponseIdOrderByIdDesc(taskDto.getSid(), responseId);
            if(!completedRecord.isPresent()) return null;
            openId = completedRecord.get().getOpenId();
        }

        //减去冻结积分
        CommunityUser communityUser = communityUserService.addUserScore(openId, ScoreType.F_N, taskDto, null);
        if(communityUser == null) {
            deliveryService.auditError(taskDto.getSid(), responseId,"communityUser is not exists");
            return null;
        }
        //this.addAuditFailNotice(communityUser, taskDto, ScoreType.G_F);
        //审核记录
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setIsPassed(false);
        auditRecord.setResponseId(responseId);
        auditRecord.setOpenId(openId);
        auditRecord.setTaskId(taskDto.getId());
        auditRecord.setCuid(communityUser.getId());
        auditRecord.setTitle(taskDto.getTitle());
        auditRecord.setSid(Long.parseLong(taskDto.getSid()));
        auditRecord.setTaskType(taskDto.getTaskType());
        auditRecordRepository.save(auditRecord);

        //增加审核不通过数量
        taskDto.getEntity().setAuditFailCount(taskDto.getEntity().getAuditFailCount() + 1);
        //减少待审核数量
        int auditWaitCount = Math.max(taskDto.getEntity().getAuditWaitCount() - 1, 0);
        taskDto.getEntity().setAuditWaitCount(auditWaitCount);
        deliveryTaskRepository.save(taskDto.getEntity());

        //更新答卷审核状态
        responseService.updateAuditStatus(responseId, ResponseStatus.AUDIT_FAIL , auditResultJsonDto);
        log.info("audit fail,surveyId:{},responseId:{},openId:{}", taskDto.getSid(), responseId, openId);
        //把审核失败的答卷 写入kafka
        SurveyResponseRollBackDto dto = new SurveyResponseRollBackDto(Long.parseLong(taskDto.getSid()), responseId, ResponseStatus.AUDIT_FAIL);
//        messageService.notifyRollBackResponse(dto);
        responseQuotaRollback(dto);

        //异步更新问卷统计数据
        statisticsService.updateAuditData(responseId, communityUser.getId(), ResponseStatus.AUDIT_FAIL);

        return auditRecord;
    }

    /**
     * 审核通过业务逻辑 new
     * @param auditContent
     */
    public void auditPassNew(AuditContent auditContent) {

        SurveyCompletedRecord completedRecord = auditContent.getSurveyCompletedRecord();
        Long shareUserShareId = null;
        if (completedRecord != null) {
            shareUserShareId = completedRecord.getInviteId();
        }

        //增加用户可用积分和总积分 减去冻结积分
        communityUserService.addUserScoreNew(auditContent, ScoreType.F_P, shareUserShareId);

        //给邀请人加转介绍费用
        communityUserService.addInviteAwardNew(auditContent);

        //添加审核记录
        AuditRecord auditRecord = new AuditRecord(auditContent.getResponseId(), auditContent.getOpenId(), auditContent.getUserInfo().getId(), auditContent.getDeliveryTask());
        auditRecord.setIsPassed(true);
        auditContent.setAuditRecord(auditRecord);

        //增加已审核数量
        auditContent.getDeliveryTask().setAuditCount(auditContent.getDeliveryTask().getAuditCount() + 1);
        //减少待审核数量
        Integer auditWaitCount = Math.max(auditContent.getDeliveryTask().getAuditWaitCount() - 1, 0);
        auditContent.getDeliveryTask().setAuditWaitCount(auditWaitCount);

        //更新答卷审核状态
        auditContent.getAuditResultJsonDto().setPoints(auditContent.getDeliveryTask().getScore());
        auditContent.getResponse().setAuditResult(JsonHelper.toJson(auditContent.getAuditResultJsonDto()));
        auditContent.getResponse().setStatus(ResponseStatus.FINAL_SUBMIT);

        log.info("audit success,surveyId:{},responseId:{},openId:{}", auditContent.getSurveyId(), auditContent.getResponseId(), auditContent.getOpenId());

        //异步更新问卷统计数据
        statisticsService.updateAuditData(auditContent.getResponseId(), auditContent.getUserInfo().getId(), ResponseStatus.FINAL_SUBMIT);

        //追访任务 发通知告知用户
        sendRecordService.sendAuditPassNotice(mapToDto(auditContent.getDeliveryTask()), auditContent.getUserInfo());
    }

    /**
     * 审核不通过业务逻辑
     * @param auditContent
     */
    public void auditFailNew(AuditContent auditContent) {

        Integer score = auditContent.getDeliveryTask().getScore();

        //减去冻结积分
        auditContent.getUserInfo().setFreezeScore(Math.max(auditContent.getUserInfo().getFreezeScore() - score, 0));

        //填答问卷只需要写入积分记录
        auditContent.getUserScoresList().add(communityUserScoresService.addScoreRecordNew(auditContent.getUserInfo(), score, ScoreType.F_N, new SurveySimpleDto(auditContent.getSurveyId().toString(), auditContent.getDeliveryTask().getName(), auditContent.getUserInfo().getNickName()), null));

        //添加审核记录
        AuditRecord auditRecord = new AuditRecord(auditContent.getResponseId(), auditContent.getOpenId(), auditContent.getUserInfo().getId(), auditContent.getDeliveryTask());
        auditRecord.setIsPassed(false);
        auditContent.setAuditRecord(auditRecord);

        //增加审核不通过数量
        auditContent.getDeliveryTask().setAuditFailCount(auditContent.getDeliveryTask().getAuditFailCount() + 1);

        //减少待审核数量
        int auditWaitCount = Math.max(auditContent.getDeliveryTask().getAuditWaitCount() - 1, 0);
        auditContent.getDeliveryTask().setAuditWaitCount(auditWaitCount);

        //更新答卷审核状态
        auditContent.getResponse().setAuditResult(JsonHelper.toJson(auditContent.getAuditResultJsonDto()));
        auditContent.getResponse().setStatus(ResponseStatus.AUDIT_FAIL);
        log.info("audit fail,surveyId:{},responseId:{},openId:{}", auditContent.getSurveyId(), auditContent.getResponseId(), auditContent.getOpenId());

        //把审核失败的答卷
        SurveyResponseRollBackDto dto = new SurveyResponseRollBackDto(auditContent.getSurveyId(), auditContent.getResponseId(), ResponseStatus.AUDIT_FAIL);
        responseQuotaRollback(dto);

        //异步更新问卷统计数据
        statisticsService.updateAuditData(auditContent.getResponseId(), auditContent.getUserInfo().getId(), ResponseStatus.AUDIT_FAIL);
    }

    /**
     * 网格邀请填答审核失败，给网格员增加审核不通过积分日志
     */
    private void addAuditFailNotice(CommunityUser communityUser, DeliveryTaskDto taskDto, ScoreType type) {
        SurveyCompletedRecord surveyCompletedRecord = completedRecordRepository.findFirstBySidAndCuid(taskDto.getSid(), communityUser.getId());
        //存在grid，表示通过网格员催答填答
        if (surveyCompletedRecord == null || surveyCompletedRecord.getGridId() == null) {
            log.warn("未通过网格员催答");
            return;
        }
        Feature feature = featureRepository.findFirstByName("gridAward");
        if (feature == null || feature.getAwardPercent() == null) {
            log.warn("gridAward 不存在");
            return;
        }
        CommunityGrid grid = communityGridRepository.findFirstByGridId(surveyCompletedRecord.getGridId());
        if(grid == null){
            log.warn("网格不存在");
            return;
        }
        //网格员
        CommunityUser gridUser = communityUserService.requireUser(grid.getCuid());
        if(gridUser == null){
            log.warn("网格员不存在");
            return;
        }
        int score = (int) Math.floor(taskDto.getScore() * feature.getAwardPercent());
        communityUserScoresService.addScoreRecord(gridUser, score, type, new SurveySimpleDto(taskDto.getSid(), taskDto.getName(), communityUser.getNickName()), null);
    }

    /**
     * 审核异常处理
     *
     * @param responseId
     * @param errorMessage
     * @throws BadRequestException
     */
    public void auditError(String surveyId, Long responseId, String errorMessage) throws BadRequestException {
        AuditResultJsonDto dto = new AuditResultJsonDto();
        dto.setFailRuleName(errorMessage);
        responseService.updateAuditStatus(responseId, ResponseStatus.AUDIT_ERROR, dto);
        //回退配额
        responseQuotaRollback(new SurveyResponseRollBackDto(Long.parseLong(surveyId), responseId, ResponseStatus.AUDIT_ERROR));
    }

    /**
     * *发送任务预估人数
     * @param task
     * @param taskChange
     * @return
     */
    public Integer sendTaskExpected(DeliveryTask task, SendTaskExDto taskChange){

        if(task == null) return 0;

        //过滤中英文逗号 "," "，"
        if(StringUtils.isNotEmpty(taskChange.getBlackList())) {
            String cuidContent = taskChange.getBlackList().replaceAll("\n",",").replaceAll("，",",");
            taskChange.setBlackList(cuidContent);
        }

        if (StringUtils.isNotEmpty(taskChange.getWhiteList())) {
            String whiteList = taskChange.getWhiteList().replaceAll("\n", ",").replaceAll("，", ",");
            taskChange.setWhiteList(whiteList);
        }

        //匹配用户
        List<CommunityUserSearchDto> users = communityUserService.searchSendTaskUser(taskChange);
        if(users != null && users.size() > 0) {
            return users.size();
        }
        return 0;
    }

    public String buildInviteShareLink(Long taskId, Long cuid) {
        DeliveryTask task = requireDeliveryTask(taskId);
        CommunityUser user = communityUserService.requireUser(cuid);
        return linkService.toShortUrl(
                task.getSurveyUrl(),
                true,
                Long.valueOf(task.getSid()),
                Map.of(
                        "openid", user.getOpenId(),
                        "additionData", Map.of("invite", user.getId().toString())
                ));


    }
    
    /**
     * 发送任务
     * @param task
     * @return
     */
    @Transactional()
    public AuditResponseDto sendDeliveryTask(DeliveryTask task, SendTaskExDto taskChange){

        if(taskChange.getInviteScore() == null) {
            throw new BadRequestException("邀请积分奖励不能为空，可以设置为0");
        }
        if(taskChange.getTotal() == null) {
            throw new BadRequestException("调查人数不能为空，可以设置为0");
        }
        if(!Arrays.asList(DeliveryTaskStatus.WAIT_SEND,DeliveryTaskStatus.PAUSE).contains(task.getStatus())) {
            throw new BadRequestException("只有待发送和暂停状态的任务才能发送");
        }
        if (task.getAuditCount() + task.getAuditWaitCount() >= taskChange.getTotal() || taskChange.getTotal() <= 0) {
            throw new BadRequestException("调查人数不能少于已回收的答卷数量");
        }
        if (!taskChange.getScore().equals(task.getScore()) && task.getAuditWaitCount() > 0) {
            throw new BadRequestException("待审核人数不为0，不支持修改积分");
        }

        if(StringUtils.isNotEmpty(task.getSurveyUrl()) && StringUtils.isEmpty(task.getShortUrl())) {
            task.setSurveyUrl(task.getSurveyUrl() + "&trackId=" + task.getId() + "&taskType=" + task.getTaskType().toString());
            task.setShortUrl(linkService.toShortUrl(task.getSurveyUrl(), true, null, null));
        }
        boolean firstSend = task.getStatus()==DeliveryTaskStatus.WAIT_SEND;
        task.setStatus(DeliveryTaskStatus.COLLECTING);
        task.setTotal(taskChange.getTotal());
        task.setEnableInvite(taskChange.getEnableInvite());
        task.setEnableElectronicFence(taskChange.getEnableElectronicFence());
        task.setLimitLocations(taskChange.getLimitLocations());

        if(taskChange.getStartTime() != null) {
            task.setStartTime(taskChange.getStartTime());
        }
        if(taskChange.getOverTime() != null) {
            task.setOverTime(DateFormatter.getEndDate(taskChange.getOverTime()));
        }
        if(taskChange.getInviteScore() > 0) {
            task.setInviteScore(taskChange.getInviteScore());
        }
        if(taskChange.getScore() > 0) {
            task.setScore(taskChange.getScore());
        }

        if(!task.getLimitInvitePeople().equals(taskChange.getLimitInvitePeople())){
            task.setLimitInvitePeople(taskChange.getLimitInvitePeople());
        }
        if(!task.getLimitInvitePeopleNum().equals(taskChange.getLimitInvitePeopleNum())){
            task.setLimitInvitePeopleNum(taskChange.getLimitInvitePeopleNum());
        }
        deliveryTaskRepository.save(task);

        //如果渠道id不为空 设置渠道状态为回收中
        if(task.getChannelId() != null && task.getChannelId() > 0L) {
            SurveyChannel channel = channelService.updateChannelStatus(task.getChannelId(), ChannelStatus.RECOVERY);
            if (firstSend && channel != null){
                Survey survey = surveyService.get(channel.getSid());
                if (survey != null) {
                    adminxEventTrigger.addEventAndTrigger(
                            survey.getOrgId(),
                            "adminxTask:" + task.getId(),
                            EventType.ADMINX_CHANNEL_ORDER_START_END,
                            new EventAdminxChannelOrderStartEnd(survey.getId(), channel.getId(), true,false));
                }
            }
        }

        //写入发送记录
        sendRecordService.sendTask(taskChange);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }
    /**
     * 模板推送
     * @param id
     * @return
     */
    public Boolean templateSend(Long id, TemplateSendDto taskChange){
        DeliveryTask task = requireDeliveryTask(id);
        String surveyUrl = String.format(xmPlusUrl, task.getSid(), task.getChannelId());
        if(!surveyUrl.contains("&trackId=")){
            surveyUrl = surveyUrl +"&trackId=" + id + "&taskType=" + task.getTaskType().toString();
        }
        TemplateSendTaskDto dto = new TemplateSendTaskDto();
        BeanUtils.copyProperties(taskChange,dto);
        dto.setSurveyUrl(surveyUrl);
        dto.setTaskId(id);
        SendRecord sendRecord = this.saveSendRecord(task, taskChange);
        dto.setSendRecordId(sendRecord.getId());
        TemplateSendExecutor.performAsync(dto);
        return true;
    }

    private SendRecord saveSendRecord(DeliveryTask task,TemplateSendDto dto){
        SendRecord sendRecord = SendRecord.builder()
                .taskId(task.getId())
                .sid(task.getSid())
                .templateId(dto.getThirdPartTemplateId())
                .sendType(dto.getSendType())
                .enablePush(false)
                .latest(false)
                .rules(JsonHelper.toJson(dto.getSamples()))
                .sendStatus(SendStatus.WAIT_SEND)
                .operator(TenantContext.getCurrentUserId())
                .taskSendType(TaskSendType.TEMPLATE_SEND).build();
        SendRecord result = sendRecordService.save(sendRecord);
        return result;
    }



    /**
     * 重新发送任务
     * @param task
     * @return
     */
    @Transactional()
    public AuditResponseDto resendDeliveryTask(DeliveryTask task, ResendDto resendDto){
        sendRecordService.resend(task,resendDto);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 发送追访任务
     *
     */
    @Transactional()
    public AuditResponseDto sendFollowTask(DeliveryTask task, SendFollowTaskDto sendFollowTaskDto){

        if(!Arrays.asList(DeliveryTaskStatus.WAIT_SEND,DeliveryTaskStatus.PAUSE).contains(task.getStatus())) {
            throw new BadRequestException("只有待发送和暂停状态的任务才能发送");
        }

        task.setStatus(DeliveryTaskStatus.COLLECTING);

        //如果渠道id不为空 设置渠道状态为回收中
        if(task.getChannelId() != 0L && task.getChannelId() != null)
            channelService.updateChannelStatusAndWechatConfig(task.getChannelId(), ChannelStatus.RECOVERY);

        //写入发送记录
        Integer total = sendRecordService.sendFollowTask(mapToDto(task),sendFollowTaskDto);
        if(StringUtils.isNotEmpty(task.getSurveyUrl()) && StringUtils.isEmpty(task.getShortUrl())) {
            task.setSurveyUrl(task.getSurveyUrl() + "&trackId=" + task.getId() + "&taskType=" + task.getTaskType().toString());
            task.setShortUrl(linkService.toShortUrl(task.getSurveyUrl(), true, null, null));
        }

        //更新追访总人数
        task.setTotal(total);
        deliveryTaskRepository.save(task);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }


    /**
     * 暂停任务
     * @param task
     * @return
     */
    public AuditResponseDto pauseDeliveryTask(DeliveryTask task){
        if(!List.of(DeliveryTaskStatus.COLLECTING,DeliveryTaskStatus.PAUSE).contains(task.getStatus())) {
            throw new BadRequestException("只有进行中的任务才能暂停");
        }

        task.setStatus(DeliveryTaskStatus.PAUSE);
        deliveryTaskRepository.save(task);

        //如果渠道id不为空 设置渠道状态为回收中
        if(task.getChannelId() != 0L && task.getChannelId() != null)
            channelService.updateChannelStatus(task.getChannelId(), ChannelStatus.PAUSE);

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }
    /**
     * 检查任务的调查期限 自动暂停任务
     * @param
     * @return
     */
    public void autoPauseDeliveryTask(){
        List<DeliveryTask> taskList = repository.findByTaskTypeAndStatus(TaskType.DELIVERY, DeliveryTaskStatus.COLLECTING);

        Optional.ofNullable(taskList).ifPresent(list ->{
            list.stream().forEach(deliveryTask -> {
                if(deliveryTask.getOverTime() != null && DateFormatter.getLastEndDate(new Date()).after(deliveryTask.getOverTime())) {
                    log.info("taskId : {} is pause", deliveryTask.getId());
                    pauseDeliveryTask(deliveryTask);
                }
            });
        });
    }

    /**
     * 重启任务
     * @param task
     * @return
     */
    public AuditResponseDto restartDeliveryTask(DeliveryTask task){
        if(task.getStatus() != DeliveryTaskStatus.PAUSE) {
            throw new BadRequestException("只有暂停中的任务才能重启");
        }

        task.setStatus(DeliveryTaskStatus.COLLECTING);
        deliveryTaskRepository.save(task);

        //如果渠道id不为空 设置渠道状态为回收中
        if(task.getChannelId() != 0L && task.getChannelId() != null)
            channelService.updateChannelStatus(task.getChannelId(), ChannelStatus.RECOVERY);

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 结束任务，
     * @param task
     * @param fromCem
     * @return
     */
    @Transactional
    public AuditResponseDto stopDeliveryTask(DeliveryTask task,boolean fromCem){
        if(task.getStatus() == DeliveryTaskStatus.COMPLETE) {
            throw new BadRequestException("已完成的任务不能结束");
        }
        task.setStatus(DeliveryTaskStatus.COMPLETE);
        task.setEndTime(new Date());
        deliveryTaskRepository.save(task);

        //如果渠道id不为空 设置渠道状态为已结束
        if (task.getChannelId() != null && task.getChannelId() != 0L) {
            SurveyChannel channel = channelService.updateChannelCompleteAndRecycle(task.getChannelId(), task.getAuditCount());
            // 如果是 cem 关闭的，不发送事件
            if (!fromCem && channel != null) {
                Survey survey = surveyService.get(channel.getSid());
                if (survey != null) {
                    adminxEventTrigger.addEventAndTrigger(
                            survey.getOrgId(),
                            "adminxTask:" + task.getId(),
                            EventType.ADMINX_CHANNEL_ORDER_START_END,
                            new EventAdminxChannelOrderStartEnd(survey.getId(), channel.getId(), false, true));
                }
            }
        }

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 结束任务
     * @param task
     * @return
     */
    @Transactional
    public AuditResponseDto stopDeliveryTask(DeliveryTask task){
        return stopDeliveryTask(task,false);
    }


    /**
     * 消费体验家答卷
     * @param responseMessageDto
     */
    public void surveyResponseConsumer(SurveyResponseMessageDto responseMessageDto){

        String surveyId = responseMessageDto.getSurveyId().toString();
        String openId   = responseMessageDto.getOpenid();

        if(responseMessageDto.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS) return;
        //如果没有openid不作处理
        if(StringUtils.isEmpty(responseMessageDto.getOpenid()) && StringUtils.isEmpty(responseMessageDto.getExternalUserId()) && StringUtils.isEmpty(responseMessageDto.getCustomerName())) {
            deliveryService.auditError(surveyId, responseMessageDto.getResponseId(),"openId or cuid is null");
            return;
        }
        CommunityUser communityUser;
        //TODO 兼容之前externalUserId = cuid的情况，追访任务没有openid externalUserId = cuid  || customerName = cuid
        if(StringUtils.isEmpty(openId) && StringUtils.isNotEmpty(responseMessageDto.getCustomerName())) {
            Long cuid = Long.parseLong(responseMessageDto.getCustomerName());
            communityUser = communityUserService.requireUser(cuid);
            if(communityUser == null) {
                deliveryService.auditError(surveyId, responseMessageDto.getResponseId(),"openId or cuid is null");
                return;
            }
            openId = communityUser.getOpenId();
            responseMessageDto.setOpenid(openId);
            //更新答卷详情的openid
            responseService.updateOpenId(responseMessageDto.getResponseId(),openId);
        } else {
            communityUser = communityUserService.requireUser(openId);
        }
        if (communityUser == null) {
            log.error("找不到对应社区用户 surveyId:{}, responseId:{}, openId:{}", surveyId, responseMessageDto.getResponseId(), openId);
            throw new RuntimeException("找不到对应社区用户");
        }

        //增加用户问卷填答数量
        communityUser.setSurveyCount(communityUser.getSurveyCount() == null ? 1 : communityUser.getSurveyCount() + 1);
        communityUserService.save(communityUser);

        //如果已有填答记录 不做处理
        if(surveyCompletedRecordService.verifyIsCompleted(surveyId,openId)) {
            log.info("surveyId:{} responseId:{} consumer fail,survey is complete", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
            deliveryService.auditError(surveyId, responseMessageDto.getResponseId(),"survey is complete");
            return;
        }

        //写入填答记录
        surveyCompletedRecordService.insertCompletedRecord(surveyId, openId ,responseMessageDto.getResponseId(),responseMessageDto.getGridId());

        DeliveryTask task = getDeliveryTask(surveyId, responseMessageDto.getTrackId());

        if(task == null) {
            log.info("surveyId:{} responseId:{} consumer fail,task is not exists", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
            deliveryService.auditError(surveyId, responseMessageDto.getResponseId(),"task is not exists");
            return;
        }

        //追访任务 更新追访记录
        if(task.getTaskType() == TaskType.FOLLOW) {
            surveySendRecordService.updateSurveySendRecordStatus(task.getId(), SubmitStatus.SUBMIT, communityUser.getId(), responseMessageDto.getStatus(),true);
        }

        //提前结束的问卷 不纳入计算
        if(responseMessageDto.getStatus() != ResponseStatus.WAIT_AUDIT) {
            log.info("surveyId:{} responseId:{} consumer fail,survey status is {}", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId(), responseMessageDto.getStatus());
            return;
        }

        //如果问卷回收数量达到总回收数量 暂停该投放任务 追访任务不暂停
        if(task.getTaskType() == TaskType.DELIVERY && (task.getTotal() - task.getAuditWaitCount() - task.getAuditCount() <= 1)) {
            pauseDeliveryTask(task);
        }

        //增加待审核数量 和填答人数
        task.setAuditWaitCount(task.getAuditWaitCount() + 1);
        task.setReplyCount(task.getReplyCount() + 1);
        deliveryTaskRepository.save(task);

        //增加冻结积分
        communityUserService.addUserScore(openId, ScoreType.F, mapToDto(task), null);
        log.info("surveyId:{} responseId:{} consumer success", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());

        //答卷自动审核
        autoAudit(mapToDto(task), responseMessageDto.getResponseId(), openId, AuditType.AUTO);
    }

    /**
     * 用户打开问卷*
     * @param dto
     */
    public void surveyResponseViewConsumer(SurveyResponseViewDto dto) {
        if(dto == null || dto.getResponseId() == null || dto.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS) return;

        DeliveryTask task = getDeliveryTask(dto.getSurveyId().toString(), dto.getTrackId());

        if(task == null) {
            log.info("surveyId:{} responseId:{} response view consumer fail,task is not exists", dto.getSurveyId(), dto.getResponseId());
            return;
        }

        //追访任务 更新追访记录
        if(task.getTaskType() == TaskType.FOLLOW) {
            if(task.getId() == null || task.getId() <= 0 || StringUtils.isEmpty( dto.getClientId())){
                throw new BadRequestException("追访记录不存在");
            }
            SurveyResponse response = responseService.get(dto.getResponseId());
            if (response != null && StringUtils.isNotEmpty(response.getOpenid())) {
                CommunityUser user = communityUserService.requireUser(response.getOpenid());
                Optional.ofNullable(user).ifPresent(u ->{
                    surveySendRecordRepository.updateSubmitStatusByTaskIdAndCuid(SubmitStatus.NOT_SUBMIT, dto.getStatus(), task.getId(), u.getId());
                });
            }
        }
    }

    /**
     * 消费调研家答卷详情
     * @param responseMessageDto
     */
    public void surveyPlusResponseConsumer(SurveyPlusResponseMessageDto responseMessageDto) {
        String surveyId = responseMessageDto.getSurveyId();
        String openId   = responseMessageDto.getOpenid();

        //如果没有openid不作处理
        if(responseMessageDto.getOpenid() == null) {
            log.info("surveyId:{} responseId:{} consumer fail,openId is null", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
            return;
        }

        //写入填答记录
        if(surveyCompletedRecordService.verifyIsCompleted(surveyId,openId)) {
            log.info("surveyId:{} responseId:{} consumer fail,survey is complete", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
            return;
        }

        surveyCompletedRecordService.insertCompletedRecord(surveyId, openId ,responseMessageDto.getResponseId(),null);

        //提前结束的问卷 不纳入计算
        if(!responseMessageDto.getIsEarlyEnd().equals("N")) {
            log.info("surveyId:{} responseId:{} consumer fail,survey status is {}", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId() ,responseMessageDto.getIsEarlyEnd());
            return;
        }

        Optional<DeliveryTask> optionalDeliveryTask = deliveryTaskRepository.findFirstBySid(surveyId, Sort.by("id").descending());
        if(!optionalDeliveryTask.isPresent()) {
            log.info("surveyId:{} responseId:{} consumer fail,task is not exists", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
            return;
        }
        DeliveryTask task = optionalDeliveryTask.get();
        //如果问卷回收数量达到总回收数量 暂停该任务
        if(task.getReplyCount() + 1 >= task.getTotal()) {
            task.setStatus(DeliveryTaskStatus.PAUSE);
        }
        //增加待审核数量 和填答人数
        task.setAuditWaitCount(task.getAuditWaitCount() + 1);
        task.setReplyCount(task.getReplyCount() + 1);
        deliveryTaskRepository.save(task);
        log.info("surveyId:{} responseId:{} consumer success", responseMessageDto.getSurveyId(), responseMessageDto.getResponseId());
    }

    /**
     * 根据taskId获取详情
     * @param taskId
     * @return
     */
    private DeliveryTask requireDeliveryTask(Long taskId) {
        Optional<DeliveryTask> optionalDeliveryTask = deliveryTaskRepository.findById(taskId);
        if(!optionalDeliveryTask.isPresent()) {
            throw new BadRequestException("任务id不存在");
        }
        return optionalDeliveryTask.get();
    }

    /**
     * 更新审核数量
     * @param deliveryTask
     * @param auditCountType
     * @param num
     * @return
     */
    private void updateAuditCount(DeliveryTask deliveryTask, AuditCountType auditCountType, int num){
        switch (auditCountType) {
            case AUDIT_COUNT:
                deliveryTask.setAuditCount(deliveryTask.getAuditCount() + num );break;
            case AUDIT_FAIL_COUNT:
                deliveryTask.setAuditFailCount(deliveryTask.getAuditFailCount() + num );break;
            case AUDIT_WAIT_COUNT:
                deliveryTask.setAuditWaitCount(deliveryTask.getAuditWaitCount() + num );break;
            case TOTAL:
                deliveryTask.setTotal(deliveryTask.getTotal() + num );break;
            default:
        }
        repository.save(deliveryTask);
    }

    /**
     * 社区获取问卷列表
     * @param queryDto
     * @return
     */
    public List<DeliveryTaskDto> getSurveyList(ResourceEntityQueryDto<DeliveryTaskDto> queryDto, Map<String,Object> map) {

        if(!map.containsKey("cuid") || map.get("cuid") == null || map.get("cuid") == "") {
            throw new BadRequestException("微信授权失败");
        }
        //获取用户信息
        Long cuid = Long.parseLong(map.get("cuid").toString());
        CommunityUser communityUser = communityUserService.requireUser(cuid);
        if(communityUser == null) {
            throw new BadRequestException("用户不存在");
        }

        String openId = communityUser.getOpenId();

        String s = JsonHelper.toJson(queryDto);
        ResourceEntityQueryDto<DeliveryTaskDto> followQueryDto = JsonHelper.toObject(s, ResourceEntityQueryDto.class);
        List<DeliveryTaskDto> followTask = this.getFollowTaskSurvey(followQueryDto, cuid);

        //加上过期时间
        queryDto.addCriteria(new ResourceQueryCriteria("startTime", new Date(), QueryOperator.LESS_THAN_EQUAL));
        queryDto.addCriteria(new ResourceQueryCriteria("overTime", new Date(), QueryOperator.GREATER_THAN_EQUAL ));

        //获取任务列表
        Page<DeliveryTaskDto> deliveryList = super.findAll(queryDto);
        List<DeliveryTaskDto> deliveryTaskDtos = new ArrayList<>();
        deliveryTaskDtos.addAll(deliveryList.getContent());
        //根据发送规则匹配 问卷
        List<DeliveryTaskDto> matchTasks = sendRecordService.matchSendTaskRule(deliveryTaskDtos, communityUserService.getUserAdditional(communityUser.getId()));
        matchTasks.addAll(followTask);
        if(matchTasks == null || matchTasks.size() == 0) return matchTasks;

        List<String> surveyIds = new ArrayList<>();
        matchTasks.stream().forEach(i ->surveyIds.add(i.getSid()));
        //获取用户已完成的问卷
        List<SurveyCompletedRecord> completedRecords = surveyCompletedRecordService.getUserCompletedSurveys(openId, surveyIds);
        if( completedRecords == null || completedRecords.size()==0 ) {
            return matchTasks;
        }
        List<String> completedSurveyIds = new ArrayList<>();
        completedRecords.stream().forEach(i ->completedSurveyIds.add(i.getSid()));

        return matchTasks.stream().filter(i -> !completedSurveyIds.contains(i.getSid())).collect(Collectors.toList());
    }

    private List<DeliveryTaskDto> getFollowTaskSurvey(ResourceEntityQueryDto<DeliveryTaskDto> queryDto,Long cuid){
        queryDto.getQueryCriteriaList().add(new ResourceQueryCriteria("taskType",TaskType.FOLLOW));
        queryDto.getQueryCriteriaList().add(new ResourceQueryCriteria("status",DeliveryTaskStatus.COLLECTING));

        //查询追访任务id
        List<DeliveryTaskDto> matchTasks = super.findAll(queryDto).getContent();
        if(matchTasks.isEmpty()){
            return List.of();
        }
        List<Long> taskIds = matchTasks.stream().map(DeliveryTaskDto::getId).collect(Collectors.toList());

        //查询发送给我的所有问卷
        List<String> SurveySendRecords = surveySendRecordRepository.findByCuidAndTaskIdIn(cuid,taskIds)
                .stream().map(SurveySendRecord::getSurveyId).collect(Collectors.toList());
        if(SurveySendRecords.isEmpty()){
            return List.of();
        }
        //找出发送给我的任务
        List<DeliveryTaskDto> filterTask = matchTasks.stream().filter(x -> SurveySendRecords.contains(x.getSid())).collect(Collectors.toList());

        return filterTask;
    }

    /**
     * 开启审核
     * @param task
     * @return
     */
    public void toggleAudit(DeliveryTask task){
        if(task.getType() != SurveyType.XM_PLUS) {
            throw new BadRequestException("只有体验家问卷可以开启审核");
        }
        Optional<Audit> audit = auditRepository.findFirstBySid(task.getSid());
        if (task.getEnableAudit() == false && audit.isEmpty()) {
            throw new BadRequestException("没有配置审核");
        }
        task.setEnableAudit(!task.getEnableAudit());
        deliveryTaskRepository.save(task);
    }

    /**
     * 开启回收提醒
     *
     * @param task
     * @return
     */
    public void toggleNotify(DeliveryTask task) {
        if (task.getType() != SurveyType.XM_PLUS) {
            throw new BadRequestException("只有体验家问卷可以开启回收提醒");
        }
        task.setEnableNotify(!task.getEnableNotify());
        deliveryTaskRepository.save(task);
    }

    /**
     * 开启关闭配额
     * 如果该问卷下没有答题数据,就不用跑配额
     * 如果有答题数据，就需要异步跑配额
     *
     * @param taskId
     */
    public void onOffQuota(Long taskId) {
        DeliveryTask task = deliveryService.require(taskId);
        Long sid = Long.parseLong(task.getSid());
        List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySidAndChannelType(sid, QuotaChannelType.SURVEY_PLUS);
        AdminxQuota adminxQuota = adminxQuotaRepository.findFirstBySid(sid);
        if(Objects.isNull(adminxQuota)){
            throw new BadRequestException("请先配置社区配额");
        }
        Survey survey = surveyRepository.findById(sid).orElseThrow(()-> new BadRequestException("问卷不存在"));
        boolean adminxQuotaStatus = !survey.getEnableAdminxQuota();
        survey.setEnableAdminxQuota(adminxQuotaStatus);
        //如果配额不为空 启用问卷的时候 需要重新计算配额
        if(adminxQuotaStatus && surveyQuotas.size() > 0){
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        }
        surveyRepository.save(survey);
    }


//    /**
//     * 关闭配额
//     * @param taskId
//     */
//    public void closeQuota(Long taskId) {
//        DeliveryTask task = deliveryService.require(taskId);
//        Long sid = Long.parseLong(task.getSid());
//        Survey survey = surveyRepository.findById(sid).orElseThrow(()-> new BadRequestException("问卷不存在"));
//        survey.setEnableAdminxQuota(false);
//        surveyRepository.save(survey);
//    }



    /**
     * 自动审核
     * @param taskDto
     * @param responseId
     */
    public void autoAudit(DeliveryTaskDto taskDto, Long responseId, String openId, AuditType auditType) {
        log.info("start auto audit,surveyId:{},responseId:{},openId:{}", taskDto.getSid(), responseId, openId);
        AuditRecord auditRecord;
        AuditResultDto auditResult = auditService.auditByTask(taskDto, responseId);
        //未开启审核
        if(auditResult == null){
            log.warn("auto audit fail,surveyId:{},responseId:{},openId:{},auditResult is null", taskDto.getSid(), responseId, openId);
            return;
        }
        AuditResultJsonDto dto = new AuditResultJsonDto();
        dto.setScore(auditResult.getScore());
        dto.setType(auditType);
        dto.setFailRuleName(auditResult.getFailRuleName());
        // todo 下载数据需要的字段
        if(auditResult.getPass()) {
            auditRecord = deliveryService.auditPass(taskDto, responseId, openId, dto);
        }else {
            auditRecord = deliveryService.auditFail(taskDto, responseId, openId, dto);
        }
        if(auditRecord != null) {
            auditRecord.setScore(auditResult.getScore());
            auditRecord.setFailRuleName(auditResult.getFailRuleName());
            auditRecord.setLog(auditResult.getLog());
            auditRecord.setType(auditType);
            auditRecordRepository.save(auditRecord);
        }
    }

    /**
     * *手动审核时 使用自动审核规则
     * @param taskDto
     * @param ids
     * @return
     */
    public AuditResponseDto auditPassByAutoAudit(DeliveryTaskDto taskDto, String[] ids) {
        try {
            // 保存审核通过记录
            Arrays.stream(ids).filter(id -> !id.equals("")).forEach(id -> {
                        taskDto.setEnableAudit(true);
                        autoAudit(taskDto, Long.parseLong(id), null, AuditType.SEMI_AUTOMATIC);
                    });
        }catch (Exception e){
            e.printStackTrace();
        }
        return AuditResponseDto.builder()
                .message("need to be implemented")
                .build();
    }

    /**
     * *配额回退
     * @param messageDto
     */
//    public void responseQuotaRollback(SurveyResponseRollBackDto dto) {
//        //设置请求头参数
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.set("Accept", "application/json");
//
//        RestTemplate restTemplate = new RestTemplate();
//        HttpEntity httpEntity = new HttpEntity(JsonHelper.toJson(dto), headers);
//
//        try{
//            //回退问卷配额
//            ResponseEntity<String> result = restTemplate.exchange(responseQuotaRollback, HttpMethod.POST, httpEntity, String.class);
//            System.out.println("survey response quota rollback："+ result.getBody());
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    public void responseQuotaRollback(SurveyResponseRollBackDto messageDto) {
        if (messageDto.getSurveyId() == null || messageDto.getResponseId() == null){
            return;
        }
        Survey survey = surveyService.requireSurvey(messageDto.getSurveyId());
        //回退配额进度
        List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySid(survey.getId())
                .stream().filter(x->x.getChannelType()==QuotaChannelType.SURVEY_PLUS)
                .collect(Collectors.toList());
        if (survey.getEnableAdminxQuota() && !surveyQuotas.isEmpty()){
            quotaAllMatchLimit.deleteResponse(survey.getId(), messageDto.getResponseId());
        }

    }

    /**
     * 网格任务列表
     * @return
     */
    public List<DeliveryTaskDto> getSupervisorTask(String gridId) {
        if(StringUtils.isEmpty(gridId)){
            throw new BadRequestException("gridId is empty");
        }
        List<DeliveryTask> deliveryTasks = repository.findByOpenSupervisorIsTrue();
        Feature feature = featureRepository.findFirstByName("gridAward");
        deliveryTasks.forEach(x -> {
            if (feature != null) {
                x.setScore((int) Math.floor(x.getScore() * feature.getAwardPercent()));
            }
            int replyCount = completedRecordRepository.countBySidAndGridId(x.getSid(), gridId);
            x.setReplyCount(replyCount);
        });
        return mapToDto(deliveryTasks);
    }

    /**
     * 开启督导
     * @param taskId
     * @return
     */
    public boolean openSupervisor(Long taskId) {
        DeliveryTask deliveryTask = repository.findById(taskId).orElseThrow();
        if(deliveryTask.getOpenSupervisor()){
            throw new BadRequestException("已开启");
        }
        deliveryTask.setOpenSupervisor(true);
        repository.save(deliveryTask);
        return true;
    }

    public void checkTaskStatus(DeliveryTask task) {
        if (task == null) throw new SurveyErrorException(SurveyErrorCode.TASK_IS_NOT_EXIST);
        switch (task.getStatus()) {
            case WAIT_SEND:
                throw new SurveyErrorException(SurveyErrorCode.TASK_WAIT_SEND);
            case PAUSE:
                throw new SurveyErrorException(SurveyErrorCode.TASK_IS_PAUSE);
            case COMPLETE:
                throw new SurveyErrorException(SurveyErrorCode.TASK_IS_COMPLETE);
            default:
        }
        Date currentTime = new Date();
        if (task.getStartTime() != null && currentTime.before(task.getStartTime()))
            throw new SurveyErrorException(SurveyErrorCode.TASK_WAIT_SEND);
        if (task.getOverTime() != null && currentTime.after(task.getOverTime()))
            throw new SurveyErrorException(SurveyErrorCode.TASK_IS_COMPLETE);
    }

    /**
     * *判断用户是否符合条件
     * @param dto
     * @return
     */
    public Boolean checkConditions(HttpServletRequest request, CheckTaskDto dto) {
        if (dto == null || dto.getTrackId() == null || StringUtils.isEmpty(dto.getOpenid()))
            throw new BadRequestException("任务不存在");
        CommunityUser communityUser = communityUserService.requireUser(dto.getOpenid());
        //校验用户状态
        communityUserService.checkUserStatus(communityUser);
        //校验任务状态
        DeliveryTask task = requireDeliveryTask(dto.getTrackId());
        checkTaskStatus(task);
        //校验用户是否填答
        if (surveyCompletedRecordService.verifyIsCompleted(task.getSid(), communityUser.getOpenId())) {
            throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
        }
        // 校验是否限制邀请人数
        if (task.getLimitInvitePeople()) {
            int countInviteTaskNum = surveyCompletedRecordService.countInviteTaskNum(task.getId(), dto.getInviteId());
            if (countInviteTaskNum >= task.getLimitInvitePeopleNum()) {
                throw new SurveyErrorException(SurveyErrorCode.TASK_INVITE_LIMIT);
            }
        }

        // 分享时添加到cuid白名单
        if (dto.getInviteId() != null) {
            Optional.ofNullable(sendRecordService.getSendRecordByTaskId(task.getId()))
                    .ifPresent(sendRecord -> {
                        if (sendRecord.getWhiteList() != null) {
                            List<String> list = new ArrayList<>(Arrays.asList(sendRecord.getWhiteList().split(",")));
                            String userId = String.valueOf(communityUser.getId());
                            if (!list.contains(userId)) {
                                list.add(userId);
                                sendRecord.setWhiteList(String.join(",", list));
                                sendRecordService.save(sendRecord);
                            }
                        }
                    });
        }

        // 校验电子围栏
        if (task.getEnableElectronicFence()) {
            if (CollectionUtils.isNotEmpty(task.getLimitLocations())) {
                checkLocations(request, dto, task);
            }
        }

        //校验是否符合条件
        Boolean match = sendRecordService.checkConditionsByRule(task,communityUser);
        if (!match) throw new SurveyErrorException(SurveyErrorCode.USER_NOT_MATCH_CONDITION);
        return true;
    }

    void checkCuid(Long taskId, Long cuid){
        Optional<SurveySendRecord> surveySendRecord = surveySendRecordRepository.findFirstByTaskIdAndCuid(taskId, cuid);
        if(surveySendRecord.isEmpty()){
            throw new SurveyErrorException(SurveyErrorCode.USER_NOT_MATCH_CONDITION);
        }
    }

    void checkLocations(HttpServletRequest request, CheckTaskDto dto, DeliveryTask task) {
        // 优先使用经纬度
        SurveyTrackingDataDto trackingDataDto = new SurveyTrackingDataDto();
        if (dto.getLatitude() != null && dto.getLongitude() != null) {
            trackingDataDto = trackingService.parseLocation(dto.getLatitude(), dto.getLongitude());
        } else {
            String ip= RestUtils.getClientIpAddress(request);
            trackingDataDto = trackingService.parseRequest(ip);
        }
        String province = trackingDataDto.getProvince();
        String city = trackingDataDto.getCity();
        Boolean inArea = false;
        for (List<String> location : task.getLimitLocations()) {
            if(location.size() == 1) {
                if(StringUtils.isNotEmpty(province) && province.equals(location.get(0))){
                    inArea = true;
                    break;
                }
            } else if(location.size() == 2) {
                if(StringUtils.isNotEmpty(city) && city.equals(location.get(1))) {
                    inArea = true;
                    break;
                }
            }
        }
        if (!inArea) {
            throw new SurveyErrorException(SurveyErrorCode.TASK_AREA_LIMIT);
        }
    }

    /**
     * 获取投放任务电子围栏配置
     * @return
     */
    public TaskFenceDto getElectronicFence(Long taskId) {
        DeliveryTask task = requireDeliveryTask(taskId);
        TaskFenceDto fenceDto = new TaskFenceDto();
        if (task.getEnableElectronicFence() && task.getLimitLocations() != null && !task.getLimitLocations().isEmpty()) {
            fenceDto.setEnableLocationFence(true);
        }
        return fenceDto;
    }
}




















