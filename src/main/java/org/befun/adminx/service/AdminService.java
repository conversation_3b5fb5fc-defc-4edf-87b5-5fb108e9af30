package org.befun.adminx.service;

import cn.hutool.core.util.HexUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.befun.adminx.auditor.SurveyAuditor;
import org.befun.adminx.constant.AutoAuditType;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.audit.AuditContext;
import org.befun.adminx.dto.audit.AuditEvaluateRequestDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.AdminRepository;
import org.befun.adminx.repository.AuditRepository;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.service.BaseService;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 自动审核服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminService extends BaseService<Admin, AdminDto, AdminRepository> {

    @SneakyThrows
    @Override
    public <S extends BaseEntityDTO<Admin>> AdminDto create(S data) {
        Assert.notNull(((AdminDto) data).getUserName(), "用户名不存在");
        Assert.notNull(((AdminDto) data).getPassword(), "密码不存在");
        ((AdminDto) data).setId(getMaxCuid());
        MessageDigest md = MessageDigest.getInstance("MD5");
        ((AdminDto) data).setPassword(HexUtil.encodeHexStr(md.digest(((AdminDto) data).getPassword().getBytes())));
        return super.create(data);
    }

    public Long getMaxCuid(){
        return repository.findFirstByOrderByIdDesc().get().getId() + 1;
    }

}
