package org.befun.adminx.service.expression;

import cn.hanyi.expression.expression.ExpressionEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.message.SurveyResponseCellMessageDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.service.SurveyBaseEntityService;
import org.befun.adminx.utils.QuestionsUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExpressionService {

    @Autowired
    private ExpressionCache expressionCache;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    List<QuestionType> SKIP_QUESTIONS_TYPE = List.of(
            QuestionType.MARK,
            QuestionType.EMPTY,
            QuestionType.SEPARATOR,
            QuestionType.MEDIA,
            QuestionType.GROUP
    );

    /**
     * 构建表达器
     *
     * @param expression
     * @param buildVariables
     * @return
     */
    public ExpressionEvaluator buildEvaluator(String expression, Boolean buildVariables) {
        return new ExpressionEvaluator(expression, buildVariables);
    }

    private List<SurveyQuestion> getQuestions(Long surveyId) {
        return surveyBaseEntityService.get(Survey.class,surveyId).getQuestions();
    }

    /**
     * 获取表达式需要的context
     * 如果缓存中有就直接就只获取
     * 没有就构建后存入缓存
     * @param responseId
     * @return
     */

    /**
     * 构建用于表达式的context
     */
    // 这里使用事务是为了保持本次操作的懒加载的数据
    @Transactional(noRollbackFor = Exception.class)
    public Map<Object, Object> buildContext(Long responseId, Map<String, Object> extraContext) {

        SurveyResponse entity = surveyBaseEntityService.require(SurveyResponse.class, responseId);
        // deep copy
        var response = JsonHelper.toObject(JsonHelper.toJson(entity), SurveyResponse.class);

        var context = new HashMap<>();
        var data = new ArrayList<SurveyResponseCellMessageDto>();
        var mergeCellList = new ArrayList<SurveyResponseCell>();

        List<SurveyQuestion> questions = getQuestions(response.getSurveyId());

        QuestionsUtils.questionsFilterGroup(questions).stream()
                .filter(surveyQuestion -> !SKIP_QUESTIONS_TYPE.contains(surveyQuestion.getType()))
                .forEach(surveyQuestion -> {
                    Optional<SurveyResponseCell> questionsCell = response.getCells().stream().filter(c -> c.getQuestionId().equals(surveyQuestion.getId())).findFirst();
                    SurveyResponseCell cell = questionsCell.isEmpty() ? new SurveyResponseCell(response.getSurveyId(), surveyQuestion, response) : questionsCell.get();
                    mergeCellList.add(cell);
                });

        Map<Long, SurveyQuestion> questionIdMap = questions.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        mergeCellList.forEach(cell -> {
            data.add(new SurveyResponseCellMessageDto(questionIdMap.get(cell.getQuestionId()), cell));
        });

        // 将系统自定参数放入表达式需要的content
        context.put("externalUserId", buildMapValue(response.getExternalUserId()));
        context.put("customerId", buildMapValue(response.getCustomerId()));
        context.put("departmentId", buildMapValue(response.getDepartmentId()));
        context.put("departmentName", buildMapValue(response.getDepartmentName()));
        context.put("customerName", buildMapValue(response.getCustomerName()));
        context.put("customerGender", buildMapValue(response.getCustomerGender()));
        context.put("departmentCode", buildMapValue(response.getDepartmentCode()));
        context.put("externalCompanyId", buildMapValue(response.getExternalCompanyId()));

        // 将自定义参数放入表达式需要的content
        response.getParameters().forEach((key, value) -> context.put(key, buildMapValue(value)));

        for (SurveyResponseCellMessageDto questionsItems : data) {

            if (questionsItems.getType() == QuestionType.GROUP) {
                continue;
            }

            if (questionsItems.getValue() == null) {
                // 跳过构造空值
                continue;
            }

            // 打分题不适用的值是-1，需要在保存时换成不适用标签的名称
//            if (questionsItems.getInapplicableLabel() != null) {
//                QuestionType type = questionsItems.getType();
//                SurveyResponseCellMessageDto inapplicableLabelDto = data.stream().filter(q -> Objects.equals(q.getQuestionId(), questionsItems.getQuestionId())).findFirst().get();
//                Object value = questionsItems.getValue();
//                if (value == null) {
//                    continue;
//                }
//                QuestionsItemsDto editValue = inapplicableLabelDto.getQuestionsItems().entrySet().stream().iterator().next().getValue();
//
//                if (QuestionType.SCORE == type) {
//                    if (value.equals(-1)) {
//                        editValue.setValue(questionsItems.getInapplicableLabel());
//                        inapplicableLabelDto.setValue(questionsItems.getInapplicableLabel());
//                    }
//                }
//
//                if (List.of(QuestionType.MATRIX_SCORE, QuestionType.MATRIX_SLIDER).contains(type)) {
//                    if (((Map) value).values().contains(-1)) {
//                        ((Map<?, ?>) editValue.getValue()).forEach((k, v) -> {
//                            if (v.equals(-1)) {
//                                ((Map) editValue.getValue()).put(k, questionsItems.getInapplicableLabel());
//                            }
//                        });
//                    }
//                }
//            }

            Map<String, Object> questionMap = JsonHelper.toMap(questionsItems.getQuestionsItems().values().iterator().next());
            questionMap.put("tags", questionsItems.getTags());
            questionMap.put("comment", questionsItems.getComment());
            context.put(
                    questionsItems.getQuestionsItems().keySet().iterator().next(),
                    questionMap
            );
        }
        if (MapUtils.isNotEmpty(extraContext)) {
            context.putAll(extraContext);
        }
        return context;
    }


    /**
     * 是否命中表达式
     *
     * @param responseId
     * @param expression
     * @return Boolean
     */
    public Boolean triggerExpression(Long responseId, String expression, Map<String, Object> extraContext, Boolean forceBuild) {
        try {
            var context = expressionCache.getContext(responseId, extraContext, forceBuild);
            log.debug("responseId: {}, exp: {} ,context: {}", responseId, expression, context);
            var expressionEvaluator = expressionCache.getExpressionEvaluator(
                    expression.replace("system_parameters.", "").replace("parameters.", "")
            );
            Object triggerResult = expressionEvaluator.evaluate(context);
            if (triggerResult instanceof Boolean && BooleanUtils.isTrue((Boolean) triggerResult)) {
                return true;
            }
        } catch (Exception e) {
            log.error("表达式命中错误: {}", e);
            return false;
        }

        return false;
    }

    /**
     * 是否命中表达式
     */
    public Boolean triggerExpression(String expression, Map<String, Object> context) {
        try {
            var expressionEvaluator = expressionCache.getExpressionEvaluator(
                    expression.replace("system_parameters.", "").replace("parameters.", "")
            );
            Object triggerResult = expressionEvaluator.evaluate(context);
            if (triggerResult instanceof Boolean && BooleanUtils.isTrue((Boolean) triggerResult)) {
                return true;
            }
        } catch (Exception e) {
            log.error("表达式命中错误", e);
            return false;
        }
        return false;
    }

    /**
     * 构造只有QuestionsItemsDto value的map
     *
     * @return
     */
    private Map buildMapValue(Object value) {
        return value == null ? null :
                new HashMap() {
                    {
                        put("value", value);
                        put("items", null);
                        put("columns", null);
                    }
                };
    }

    /**
     * 获取content
     */
    public Map getContent(Long responseId, Map<String, Object> extraContext, Boolean forceBuild) {
        return expressionCache.getContext(responseId, extraContext, forceBuild);
    }
}
