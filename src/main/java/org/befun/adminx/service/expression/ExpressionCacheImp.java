package org.befun.adminx.service.expression;

import cn.hanyi.expression.expression.ExpressionEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 使用LinkedHashMap缓存,在put时加锁
 */
@Slf4j
@Component
class ExpressionCacheImp implements ExpressionCache {

    @Value("${survey.expression.context-max-num:100}")
    private int CONTEXT_MAX_NUM;
    @Value("${survey.expression.expression-max-num:100}")
    private int EXPRESSION_MAX_NUM;

    private LinkedHashMap<Long, Map> contextMap;

    private LinkedHashMap<Integer, ExpressionEvaluator> expressionMap;


    @PostConstruct
    private void inti() {
        contextMap = new LinkedHashMap<>() {
            protected boolean removeEldestEntry(Map.Entry<Long, Map> eldest) {
                return size() > CONTEXT_MAX_NUM;
            }
        };
        expressionMap = new LinkedHashMap<>() {
            protected boolean removeEldestEntry(Map.Entry<Integer, ExpressionEvaluator> eldest) {
                return size() > EXPRESSION_MAX_NUM;
            }
        };
    }


    @Autowired
    private ExpressionService expressionService;

    @Override
    public Map getContext(Long responseId, Map<String, Object> extraContext, Boolean forceBuild) {
        Map<Object, Object> context = null;

        context = expressionService.buildContext(responseId, extraContext);

        if (MapUtils.isNotEmpty(extraContext)) {
            context.putAll(extraContext);
        }

        return context;
    }

    @Override
    public ExpressionEvaluator getExpressionEvaluator(String expression) {
        ExpressionEvaluator expressionEvaluator = expressionService.buildEvaluator(expression, false);
        return expressionEvaluator;
    }

    @Override
    public Integer putExpressionEvaluator(String expression, ExpressionEvaluator expressionEvaluator) {
        int hash = hashExpression(expression);
        expressionMap.put(hash, expressionEvaluator);
        return hash;
    }
}
