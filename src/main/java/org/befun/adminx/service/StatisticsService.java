package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.dto.user.UserStatisticsDto;
import org.befun.adminx.dto.survey.SurveyResponseStatisticsDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.SurveyStatistics;
import org.befun.adminx.entity.SurveyStatisticsDto;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.SurveyStatisticsRepository;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/4 17:18
 */
@Slf4j
@Service
public class StatisticsService extends BaseService<SurveyStatistics, SurveyStatisticsDto, SurveyStatisticsRepository> {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 根据日期查询用户的统计数据
     * @param date
     * @param cuid
     * @return
     */
    public SurveyStatistics getOneByDateAndCuid(String date, Long cuid) {
        Optional<SurveyStatistics> optional = repository.findByDateAndCuid(date,cuid);
        if(optional.isPresent()) return optional.get();
        else return new SurveyStatistics();
    }

    /**
     * 获取用户项目统计数据
     * @param cuid
     * @return
     */
    public UserStatisticsDto getUserStatisticsData(Long cuid) {
        String sql = String.format("select " +
                "ifnull(sum(total),0) AS total," +
                "ifnull(sum(completed),0) AS completed," +
                "ifnull(sum(quota_full),0) AS quotaFull," +
                "ifnull(sum(audit_pass),0) as auditPass," +
                "ifnull(sum(early_completed),0) AS earlyCompleted," +
                "ifnull( sum( completed ) / sum( total ) *100,0) AS completedRate," +
                "ifnull( sum( audit_pass ) / sum( completed ) *100,0) as passRate " +
                "from survey_statistics where cuid=%d;", cuid);
        try {
            return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(UserStatisticsDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 根据条件筛选用户
     * @param params
     * @return
     */
    public List<UserStatisticsDto> searchUser(Map<String,Object> params) {
        String sql = " select cuid," +
                "ifnull(sum(total),0) AS total," +
                "ifnull(sum(completed),0) AS completed," +
                "ifnull(sum(quota_full),0) AS quotaFull," +
                "ifnull(sum(audit_pass),0) as auditPass," +
                "ifnull(sum(early_completed),0) AS earlyCompleted," +
                "ifnull( sum( completed ) / sum( total ) *100,0) AS completedRate," +
                "ifnull( sum( audit_pass ) / sum( completed ) *100,0) AS passRate " +
                "from survey_statistics ";

        if(params.containsKey("dateGte") && params.containsKey("dateLte")) {
            sql += String.format(" where date >= '%s' and date <= '%s' ", params.get("dateGte"), params.get("dateLte"));
        }
        params.remove("dateGte");
        params.remove("dateLte");
        sql += " group by cuid ";
        List<String> havingList = new ArrayList<>();
        for (Map.Entry<String,Object> entry : params.entrySet()) {
            if(entry.getValue() != null) havingList.add(searchOperators(entry.getKey(),entry.getValue().toString()));
        }
        String having = "";
        if(havingList.size() >0) {
            having = "having (" + StringUtils.join(havingList," and ") + ")" ;
        }

        if(StringUtils.isEmpty(having)) return null;
        else sql += having;
        log.info("searchUser sql:{}",sql);

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(UserStatisticsDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 表达式解析
     * @param key
     * @param value
     * @return
     */
    private String searchOperators(String key, String value) {
        String having = "";
        if(value == null) return having;
        String op = value.split(",")[0];
        String val = value.split(",")[1];
        switch (op){
            case "eq":
                op = " = ";break;
            case "neq":
                op = " <> ";break;
            case "gte":
                op = " >= ";break;
            case "gt":
                op = " > ";break;
            case "lte":
                op = " <= ";break;
            case "lt":
                op = " < ";break;
            default:
        }
        return key + op + val;
    }

    /**
     * 同步昨天的数据
     */
    public String syncData() {
        String startTime = DateFormatter.getLastDay(0,0,0);
        String endTime = DateFormatter.getLastDay(23,59,59);
        addSurveyStatisticsData(startTime.substring(0,10), responseService.getSurveyStatistics(startTime,endTime));
        return startTime.substring(0,10);
    }

    private Long incrAmount() {
        return stringRedisTemplate.opsForValue().increment("SyncDataAmount", 1);
    }

    private Long getAmount() {
        String amount = stringRedisTemplate.opsForValue().get("SyncDataAmount");
        if(StringUtils.isEmpty(amount)) return 0l;
        else return Long.parseLong(stringRedisTemplate.opsForValue().get("SyncDataAmount"));
    }

    /**
     * 同步2022年历史数据
     */
    public String syncAllData() {
        String time = "2022-01-01";
        Long amount = getAmount();
        time = DateFormatter.getPlusDay(time, Math.toIntExact(amount));
        String startTime = time + " 00:00:00";
        String endTime = time + " 23:59:59";
        //如果统计审核日期和创建日期是同一天 不需要更新 因为隔天会统计
        if(time.equals(DateFormatter.getNowStringDate())) return time;
        addSurveyStatisticsData(startTime.substring(0,10), responseService.getSurveyStatistics(startTime,endTime));
        incrAmount();
        return time;
    }

    /**
     * 把统计数据写入到 问卷统计数据表
     * @param date
     * @param statisticsData
     */
    @Transactional
    public void addSurveyStatisticsData(String date, List<SurveyResponseStatisticsDto> statisticsData) {
        if(StringUtils.isEmpty(date)) {
            log.info("syncData date is empty: {}", LocalTime.now());
            return;
        }
        if(statisticsData == null || statisticsData.size() == 0) {
            log.info("syncData statisticsData is empty: {}", date);
            return;
        }
        statisticsData.stream().forEach(data ->{
            CommunityUser user = communityUserService.requireUser(data.getOpenid());
            if(user != null) {
                SurveyStatistics statistics = getOneByDateAndCuid(date, user.getId());
                if(statistics.getCuid() == null && statistics.getDate() == null) {
                    statistics.setCuid(user.getId());
                    statistics.setDate(date);
                    //完成数量 = 审核通过的+失败的+异常的+作废的+待审核的
                    Integer completed = data.getAuditPass() + data.getAuditFail() + data.getAuditError() + data.getInvalid() + data.getWaitAudit();
                    statistics.attributeSummary(data.getTotal(), completed, data.getQuotaFull(), data.getEarlyCompleted(), data.getAuditPass(), data.getAuditFail());
                    repository.save(statistics);
                }
            }
        });
        log.info("syncData success: {}", date);
    }

    /**
     * 审核之后 增加问卷通过数量
     * @param responseId
     * @param cuid
     * @param status
     */
    @Async
    public void updateAuditData(Long responseId, Long cuid, ResponseStatus status) {
        log.info("start to updateAuditData: {}", LocalTime.now());
        SurveyResponse response = responseService.get(responseId);
        if(response == null) {
            log.info("updateAuditData error response is null: {}", LocalTime.now());
            return;
        }
        String date = DateFormatter.getStringDate(response.getCreateTime());
        //如果统计审核日期和创建日期是同一天 不需要更新 因为隔天会统计
        if(date.equals(DateFormatter.getNowStringDate())) return;
        SurveyStatistics statistics = getOneByDateAndCuid(date, cuid);
        switch (status) {
            case FINAL_SUBMIT:
                statistics.setAuditPass(statistics.getAuditPass() + 1);break;
            case AUDIT_FAIL:
                statistics.setAuditFail(statistics.getAuditFail() + 1);break;
            case AUDIT_ERROR:
                break;
            default:
                return;
        }
        statistics.setDate(date);
        statistics.setCuid(cuid);
        statistics.attributeSummary();
        repository.save(statistics);
        log.info("updateAuditData success: {}", date);
    }
}
