package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.auditor.SurveyAuditor;
import org.befun.adminx.constant.AutoAuditType;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.audit.AuditContext;
import org.befun.adminx.dto.audit.AuditEvaluateRequestDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.dto.survey.SurveyResponseSimilarityDto;
import org.befun.adminx.entity.Audit;
import org.befun.adminx.entity.AuditDto;
import org.befun.adminx.entity.DeliveryTaskDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.AuditRepository;
import org.befun.adminx.repository.SurveyRepository;
import org.befun.adminx.service.audit.AuditContent;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 自动审核服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuditService extends BaseService<Audit, AuditDto, AuditRepository> {

    @Autowired
    private MapperService mapperService;

    @Autowired
    private AuditRepository auditRepository;

    @Autowired
    private Map<String, SurveyAuditor> auditorMap = new HashMap<>();

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyRepository surveyRepository;


    @Override
    public <S extends BaseEntityDTO<Audit>> AuditDto create(S data) {
        AuditDto dto = (AuditDto) data;
        String sid = dto.getSid();
        if(sid != null && repository.findFirstBySid(sid).isPresent()){
            throw new BadRequestException("问卷已存在审核设置");
        }
        return super.create(data);
    }

    @Override
    public Page<AuditDto> findAll(ResourceEntityQueryDto<AuditDto> queryDto) {
        queryDto.setSorts(Sort.by("id").descending());
        if(queryDto.getQueryCriteriaList().size() > 0) {
            queryDto.getQueryCriteriaList().stream().forEach(query ->{
                if(query.getKey().equals("name"))
                    query.setOperator(QueryOperator.LIKE);
            });
        }
        return super.findAll(queryDto);
    }

    /**
     * 匹配审核规则
     */
    public AuditContext fakeResponseData(Long surveyId, Long responseId) {
        SurveyResponse response = responseService.require(responseId);
        if(response == null) return null;
        Map<String, Object> context = responseService.getResponseContext(response);
        return AuditContext.builder()
                .surveyId(surveyId)
                .responseId(responseId)
                .context(context)
                .build();
    }

    /**
     * 匹配审核规则 new
     * @param auditContent
     * @return
     */
    public AuditContext fakeResponseDataNew(AuditContent auditContent) {
        Map<String, Object> context = responseService.getResponseContextNew(auditContent);
        return AuditContext.builder()
                .surveyId(auditContent.getSurveyId())
                .responseId(auditContent.getResponseId())
                .context(context)
                .build();
    }

    /**
     * 任务自动审核
     * @param taskDto
     */
    public AuditResultDto auditByTask(DeliveryTaskDto taskDto, Long responseId) {
        Optional<Audit> audit = auditRepository.findFirstBySid(taskDto.getSid());
        if ( taskDto.getType() != SurveyType.XM_PLUS || audit.isEmpty() || !taskDto.getEnableAudit()) {
            log.info("audit null,taskType:{},enableAudit:{}", taskDto.getType(), taskDto.getEnableAudit());
            return null;
        }
        AuditDto auditDto = mapperService.map(audit.get(), AuditDto.class);
        Long sid = Long.valueOf(taskDto.getSid());
        AutoAuditType auditType = audit.get().getAuditType();
        AuditContext context = this.fakeResponseData(sid,responseId);
        log.info("auditByTask,surveyId:{},responseId:{},context:{}", sid, responseId, context == null ? null : JSONObject.toJSONString(context.getContext()));
        SurveyAuditor auditor = auditorMap.get(auditType.name());
        Assert.notNull(auditor, "invalid auditor");

        return auditor.run(auditDto, context);
    }

    /**
     * 评估
     * @param requestDto
     */
    public AuditResultDto evaluate(AuditEvaluateRequestDto requestDto) {
        AuditContext context = new AuditContext();
        context.setContext(requestDto.getContext());

        SurveyAuditor auditor = auditorMap.get(requestDto.getAuditType().name());
        AuditDto auditDto = new AuditDto();
        auditDto.setCode(requestDto.getCode());
        auditDto.setAuditType(requestDto.getAuditType());
        auditDto.setLanguage(requestDto.getLanguage());
        auditDto.setRules(requestDto.getRules());
        auditDto.setThreshold(requestDto.getThreshold());
        return auditor.run(auditDto, context);
    }

    /**
     * 任务自动审核
     * @param auditContent
     */
    @Transactional
    public void auditByTaskNew(AuditContent auditContent) {
        auditContent.setAudit(auditRepository.findFirstBySid(auditContent.getSurveyId().toString()).orElseGet(() -> null));
        if (auditContent.getDeliveryTask().getType() != SurveyType.XM_PLUS) {
            auditContent.setReturnFlag(false);
            throw new BadRequestException("非体验家问卷不能自动审核");
        }
        if (auditContent.getAudit() == null) {
            log.info("auditByTask surveyId:{},responseId:{},未配置自动审核规则",auditContent.getSurveyId(), auditContent.getResponseId());
            auditContent.setReturnFlag(false);
            return;
        }
        //开启相似度匹配
        if (auditContent.getAudit().getEnableSimilarityCheck()) {
            similarityCheck(auditContent);
        }

        if (!auditContent.getDeliveryTask().getEnableAudit()) {
            log.info("auditByTask surveyId:{},responseId:{},未开启自动审核",auditContent.getSurveyId(), auditContent.getResponseId());
            auditContent.setReturnFlag(false);
            return;
        }

        AuditContext context = fakeResponseDataNew(auditContent);
        log.info("auditByTask surveyId:{},responseId:{},context:{}", auditContent.getSurveyId(), auditContent.getResponseId(), context == null ? null : JSONObject.toJSONString(context.getContext()));
        SurveyAuditor auditor = auditorMap.get(auditContent.getAudit().getAuditType().name());
        Assert.notNull(auditor, "invalid auditor");
        auditContent.setAuditResultDto(auditor.run(mapToDto(auditContent.getAudit()), context));
    }

    /**
     * 相似度检查
     * @param auditContent
     */
    public void similarityCheck(AuditContent auditContent) {
        //未开启相似度检查 或者相似度为null时 不做相似度匹配
        if (auditContent.getAudit() == null || !auditContent.getAudit().getEnableSimilarityCheck() || auditContent.getAudit().getSimilarityPercent() == null) {
            log.info("similarityCheck surveyId:{},responseId:{},未开启相似度检查", auditContent.getSurveyId(), auditContent.getResponseId());
            return;
        }

        //组装去匹配相似度的content
        SurveyResponseSimilarityDto similarityDto = responseService.getSimilarityMatchContent(auditContent.getSurveyId(), auditContent.getResponseId());
        if(similarityDto == null || StringUtils.isEmpty(similarityDto.getContent())) {
            log.info("similarityCheck surveyId:{},responseId:{},组装去匹配相似度的content为null", auditContent.getSurveyId(), auditContent.getResponseId());
            return;
        }

        log.info("similarityCheck surveyId:{},responseId:{},相似度content:{}", auditContent.getSurveyId(), auditContent.getResponseId(), similarityDto.getContent());

        //匹配相似度 最近一个月
        float similarityRatio = (float) auditContent.getAudit().getSimilarityPercent() / 100;
        List<SurveyResponseSimilarityDto> similarityDtoList =  responseService.getSimilarityResponses(auditContent.getSurveyId(), similarityDto.getContent(), DateFormatter.getStringTime(DateFormatter.getLastEndDate(new Date())), similarityRatio);
        if (similarityDtoList == null || similarityDtoList.isEmpty()) {
            log.info("similarityCheck surveyId:{},responseId:{},相似条数为:0", auditContent.getSurveyId(), auditContent.getResponseId());
            return;
        }
        //匹配出来的数据 默认有一条
        if (similarityDtoList.size() >= auditContent.getAudit().getSimilarityCount() + 1) {
            log.info("similarityCheck surveyId:{},responseId:{},相似条数为:{}", auditContent.getSurveyId(), auditContent.getResponseId(), similarityDtoList.size());
            auditContent.setReturnFlag(false);
            auditContent.setSimilarityFlag(true);
            auditContent.setSimilarityDtoList(similarityDtoList);
            throw new BadRequestException("答卷相似条数超过限制");
        }
    }
}
