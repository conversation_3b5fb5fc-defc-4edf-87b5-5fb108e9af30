package org.befun.adminx.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.survey.AbstractSurvey;
import org.befun.adminx.survey.Provider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
public class HybridSurveyService {

    @Autowired
    private Map<String, Provider> surveyProviders;

    /**
     *
     * @param type
     * @param sid
     * @return
     */
    public AbstractSurvey getSurvey(SurveyType type, String sid) {
        Provider provider = surveyProviders.get(type.name().toUpperCase());
        Assert.notNull(sid, "missing sid");
        Assert.notNull(provider, "missing provider");
        AbstractSurvey survey = provider.getSurvey(sid);
        return survey;
    }

    /**
     * 获取问卷状态
     * @param type
     * @param sid
     * @return
     */
    public Boolean getSurveyStatus(SurveyType type, String sid) {
        if(type == null || StringUtils.isEmpty(sid)) return false;
        Provider provider = surveyProviders.get(type.name().toUpperCase());
        return provider.getSurveyStatus(sid);
    }
}
