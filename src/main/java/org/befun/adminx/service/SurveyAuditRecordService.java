package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.adminx.annotation.ResourceCustomQueryHelper;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyAuditSource;
import org.befun.adminx.constant.survey.SurveyAuditStatus;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.adminx.dto.event.EventSurveyManualCheckDto;
import org.befun.adminx.dto.event.EventSurveyReportDto;
import org.befun.adminx.dto.event.EventType;
import org.befun.adminx.dto.surveyaudit.SurveyAuditDto;
import org.befun.adminx.dto.surveyaudit.SurveyAuditRecordQueryDto;
import org.befun.adminx.dto.surveyaudit.SurveyContentAuditDto;
import org.befun.adminx.dto.surveyaudit.SurveyReportDto;
import org.befun.adminx.entity.CemEvent;
import org.befun.adminx.entity.Organization;
import org.befun.adminx.entity.SurveyAuditRecord;
import org.befun.adminx.entity.SurveyAuditRecordDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.repository.CemEventRepository;
import org.befun.adminx.repository.SurveyAuditRecordRepository;
import org.befun.adminx.repository.SurveyResponseRepository;
import org.befun.adminx.trigger.AdminxEventTrigger;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SurveyAuditRecordService extends BaseService<SurveyAuditRecord, SurveyAuditRecordDto, SurveyAuditRecordRepository> {

    @Value("${adminx.survey-audit.report.token:f3e36704-fa24-478e-9e49-cf2a99240f59}")
    private String reportToken;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private SurveyService surveyService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private CemEventRepository cemEventRepository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private AdminxEventTrigger adminxEventTrigger;

    @Override
    public <S extends ResourceCustomQueryDto> Page<SurveyAuditRecordDto> findAll(S params) {
        SurveyAuditRecordQueryDto dto = (SurveyAuditRecordQueryDto) params;
        Specification<SurveyAuditRecord> specification = ResourceCustomQueryHelper.specification(dto, dto.subQueryRootProeprtyMap());
        PageRequest pageRequest = ResourceCustomQueryHelper.pageRequest(dto);
        return findAll(specification, pageRequest);
    }

    @Override
    protected void afterMapToDto(List<SurveyAuditRecord> entity, List<SurveyAuditRecordDto> dto) {
        Set<Long> orgIds = new HashSet<>();
        Set<Long> surveyIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(entity) && CollectionUtils.isNotEmpty(dto)) {
            entity.forEach(i -> {
                Optional.ofNullable(i.getOrgId()).ifPresent(orgIds::add);
                Optional.ofNullable(i.getSurveyId()).ifPresent(surveyIds::add);
            });
        }
        Map<Long, Organization> orgMap = organizationService.getGroupMapByIds(orgIds, Organization::getId, Function.identity());
        Map<Long, Survey> surveyMap = surveyService.getGroupMapByIds(surveyIds, Survey::getId, Function.identity());
        dto.forEach(i -> {
            Optional.ofNullable(orgMap.get(i.getOrgId())).ifPresent(org -> {
                i.setOrgCode(org.getCode());
                i.setOrgName(org.getName());
                i.setOrgStatus(org.getIsBlock());
                if (org.getXmpUser() != null) {
                    i.setMobile(org.getXmpUser().getMobile());
                }
            });
            Optional.ofNullable(surveyMap.get(i.getSurveyId())).ifPresent(survey -> {
                i.setSurveyTitle(survey.getTitle());
                i.setSurveyStatus(survey.getStatus());
                i.setCountResponse(surveyResponseRepository.countBySurveyIdAndStatus(i.getSurveyId(), ResponseStatus.FINAL_SUBMIT));
            });
            replaceContentAuditContent(i);
        });
    }

    protected void replaceContentAuditContent(SurveyAuditRecordDto dto) {
        // 重新查询审核不通过的描述
        if (dto.getSource() == SurveyAuditSource.contentAudit && dto.getSourceId() > 0 && !"审核失败".equals(dto.getReason())) {
            Object response = entityManager.createNativeQuery("SELECT response FROM cem_platform.content_audit_record where id = " + dto.getSourceId()).getSingleResult();
            if (response != null) {
                Map<String, Object> map = JsonHelper.toMap(response.toString());
                if (map != null) {
                    Set<Long> ids = new HashSet<>();
                    map.forEach((k, v) -> {
                        if (NumberUtils.isDigits(k) && v != null && !"pass".equals(v.toString())) {
                            ids.add(Long.parseLong(k));
                        }
                    });
                    if (!ids.isEmpty()) {
                        String idsString = ids.stream().map(Objects::toString).collect(Collectors.joining(",", "(", ")"));
                        List<?> list = entityManager.createNativeQuery("SELECT type, content, words FROM cem_platform.content_audit_record where id in " + idsString).getResultList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> message = new ArrayList<>();
                            list.forEach(i -> {
                                Object[] j = (Object[]) i;
                                if (j.length == 3) {
                                    Object type = j[0];
                                    Object content = j[1];
                                    Object words = j[2];
                                    if (type != null && words != null) {
                                        if ("image".equals(type.toString())) {
                                            if (content != null) {
                                                List<String> ss = JsonHelper.toList(words.toString(), String.class);
                                                if (ss != null && !ss.isEmpty()) {
                                                    message.addAll(ss);
                                                    message.add(content.toString());
                                                }
                                            }
                                        } else if ("text".equals(type.toString())) {
                                            List<String> ss = JsonHelper.toList(words.toString(), String.class);
                                            if (ss != null && !ss.isEmpty()) {
                                                message.addAll(ss);
                                            }
                                        }
                                    }
                                }
                            });
                            dto.setContent(JsonHelper.toJson(message));
                        }
                    }
                }
            }
        }
    }

    @Override
    public void delete(SurveyAuditRecord entity) {
        Survey survey = surveyService.get(entity.getSurveyId());
        if (survey == null) {
            super.delete(entity);
        } else {
            throw new BadRequestException("只能删除问卷已删除的记录");
        }
    }

    @Transactional
    public boolean audit(long id, SurveyAuditDto dto) {
        SurveyAuditRecord record = require(id);
        if (record.getStatus() != SurveyAuditStatus.init) {
            throw new BadRequestException("不能重复审核");
        }
        long orgId = record.getOrgId();
        long surveyId = record.getSurveyId();
        record.setStatus(dto.getStatus());
        record.setRemark(dto.getRemark());
        record.setAuditTime(new Date());
        Survey survey = surveyService.require(record.getSurveyId());
        EventSurveyManualCheckDto triggerDto = new EventSurveyManualCheckDto(orgId, surveyId, record.getSource().name(), dto.getStatus().name());
        if (dto.getStatus() == SurveyAuditStatus.pass) {
            // pass
            if (record.getSource() == SurveyAuditSource.contentAudit) {
                // survey status -> CONTENT_APPROVAL
                survey.setStatus(SurveyStatus.CONTENT_APPROVAL.ordinal());
                surveyService.save(survey);
                // content audit status pass
                updateContentAuditStatus(record.getSourceId(), true);
            } else {
                // ignore
                // 不用改问卷状态
            }
        } else if (dto.getStatus() == SurveyAuditStatus.noPass) {
            // no pass
            if (record.getSource() == SurveyAuditSource.contentAudit) {
                // survey status -> CONTENT_REJECTED
                survey.setStatus(SurveyStatus.CONTENT_REJECTED.ordinal());
                surveyService.save(survey);
                // content audit status no pass
                updateContentAuditStatus(record.getSourceId(), false);
            } else {
                // survey status -> STOPPED
                survey.setStatus(SurveyStatus.STOPPED.ordinal());
                surveyService.save(survey);
            }
        } else if (dto.getStatus() == SurveyAuditStatus.disabled) {
            // disabled
            if (record.getSource() == SurveyAuditSource.contentAudit) {
                // content audit status no pass
                updateContentAuditStatus(record.getSourceId(), false);
            }
            // survey status -> DISABLED
            survey.setStatus(SurveyStatus.DISABLED.ordinal());
        } else {
            throw new BadRequestException("无效的审核状态");
        }
        repository.save(record);
        // add to worker
        addEventAndTrigger(orgId, surveyId, EventType.SURVEY_MANUAL_CHECK, triggerDto);
        return true;
    }

    private void updateContentAuditStatus(Long contentAuditId, boolean pass) {
        if (contentAuditId != null) {
            if (pass) {
                Set<String> ids = new HashSet<>();
                ids.add(contentAuditId.toString());
                Object response = entityManager.createNativeQuery("SELECT response FROM cem_platform.content_audit_record where id = " + contentAuditId).getSingleResult();
                if (response != null) {
                    Map<String, Object> map = JsonHelper.toMap(response.toString());
                    if (map != null) {
                        map.forEach((k, v) -> {
                            if (NumberUtils.isDigits(k) && v != null && !"pass".equals(v.toString())) {
                                ids.add(k);
                            }
                        });
                    }
                }
                String idsString = String.join(",", ids);
                String updateSql = "update `cem_platform`.`content_audit_record` set status='pass' where id in (" + idsString + ")";
                entityManager.createNativeQuery(updateSql).executeUpdate();
            } else {
                String updateSql = "update `cem_platform`.`content_audit_record` set status='noPass' where id=" + contentAuditId;
                entityManager.createNativeQuery(updateSql).executeUpdate();
            }
        }
    }

    @Transactional
    public void addBySurveyContentAudit(SurveyContentAuditDto dto) {
        SurveyAuditRecord record = new SurveyAuditRecord();
        record.setOrgId(dto.getOrgId());
        record.setSurveyId(dto.getSurveyId());
        record.setSource(SurveyAuditSource.contentAudit);
        record.setSourceId(dto.getAuditId());
        record.setReason(dto.getReason());
        record.setContent(JsonHelper.toJson(dto.getContent()));
        record.setRequestTime(DateHelper.toDate(DateHelper.parseDateTime(dto.getRequestTime())));
        repository.save(record);
    }

    @Transactional
    public boolean addBySurveyReport(SurveyReportDto dto) {
        dto.checkToken(reportToken);
        Survey survey = surveyService.get(dto.parseSurveyId());
        if (survey == null) {
            throw new BadRequestException("问卷不存在");
        }
        SurveyAuditRecord record = new SurveyAuditRecord();
        record.setOrgId(survey.getOrgId());
        record.setSurveyId(survey.getId());
        record.setSource(SurveyAuditSource.report);
        record.setSourceId(dto.getResponseId());
        record.setReason(dto.parseReason());
        record.setContent(dto.parseContent());
        record.setRequestTime(dto.parseRequestTime());
        repository.save(record);
        // add to worker
        addEventAndTrigger(record.getOrgId(), record.getSurveyId(), EventType.SURVEY_REPORT, new EventSurveyReportDto(record.getOrgId(), record.getSurveyId(), record.getReason(), DateHelper.formatDateTime(record.getRequestTime())));
        return true;
    }


    private void addEventAndTrigger(Long orgId, Long surveyId, EventType eventType, Object data) {
        CemEvent entity = new CemEvent();
        entity.setOrgId(orgId);
        entity.setType(eventType);
        entity.setContent(JsonHelper.toJson(data));
        entity.setSource("survey:" + surveyId);
        cemEventRepository.save(entity);
        adminxEventTrigger.triggerEvent(entity.getId());
    }

}
