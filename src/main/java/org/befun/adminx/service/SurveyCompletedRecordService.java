package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.survey.SurveyCompletedRecord;
import org.befun.adminx.entity.survey.SurveyCompletedRecordDto;
import org.befun.adminx.repository.SurveyCompletedRecordRepository;
import org.befun.adminx.service.audit.AuditContent;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyCompletedRecordService extends BaseService<SurveyCompletedRecord, SurveyCompletedRecordDto, SurveyCompletedRecordRepository> {

    @Autowired
    SurveyCompletedRecordRepository completedRecordRepository;

    @Autowired
    CommunityUserService communityUserService;
    /**
     * 验证是否已经填答了问卷
     * @param surveyId
     * @param openId
     * @return
     */
    public Boolean verifyIsCompleted(String surveyId, String openId) {
        Optional<SurveyCompletedRecord> optionalSurveyCompletedRecord = completedRecordRepository.findFirstBySidAndOpenId(surveyId, openId);
        if(optionalSurveyCompletedRecord.isPresent()) {
            return true;
        }else {
            return false;
        }
    }


    public int countInviteTaskNum(Long taskId, Long inviteId) {
        return completedRecordRepository.countByTaskIdAndInviteId(taskId.toString(), inviteId);
    }

    /**
     * 验证是否已经填答了问卷
     * @param auditContent
     * @return
     */
    public Boolean verifyIsCompletedNew(AuditContent auditContent) {
        Optional<SurveyCompletedRecord> optionalSurveyCompletedRecord = completedRecordRepository.findFirstBySidAndOpenId(auditContent.getSurveyId().toString(), auditContent.getOpenId());
        if (optionalSurveyCompletedRecord.isPresent()) {
            auditContent.setSurveyCompletedRecord(optionalSurveyCompletedRecord.get());
            return true;
        }else {
            auditContent.setSurveyCompletedRecord(null);
            return false;
        }
    }

    /**
     * 写入填答记录
     * @param surveyId
     * @param openId
     * @param responseId
     */
    public void insertCompletedRecord(String surveyId, String openId, Long responseId,String gridId) {
        log.info("写入填答记录,surveyId:{}, openId:{},responseId:{}",surveyId, openId,responseId);
        SurveyCompletedRecord surveyCompletedRecord = new SurveyCompletedRecord();
        surveyCompletedRecord.setSid(surveyId);
        surveyCompletedRecord.setOpenId(openId);
        surveyCompletedRecord.setResponseId(responseId);
        surveyCompletedRecord.setGridId(gridId);

        //获取用户cuid
        CommunityUser communityUser = communityUserService.requireUser(openId);
        if(communityUser != null){
            surveyCompletedRecord.setCuid(communityUser.getId());
        }
        completedRecordRepository.save(surveyCompletedRecord);
    }

    /**
     * 写入填答记录
     * @param auditContent
     */
    public void insertCompletedRecordNew(AuditContent auditContent) {
        SurveyCompletedRecord surveyCompletedRecord = new SurveyCompletedRecord();
        surveyCompletedRecord.setSid(auditContent.getSurveyId().toString());
        surveyCompletedRecord.setOpenId(auditContent.getOpenId());
        surveyCompletedRecord.setResponseId(auditContent.getResponseId());
        surveyCompletedRecord.setGridId(auditContent.getData().getGridId());
        surveyCompletedRecord.setCuid(auditContent.getUserInfo().getId());
        surveyCompletedRecord.setTaskId(auditContent.getTaskId());
        Optional.ofNullable(auditContent.getData()).flatMap(data -> Optional.ofNullable(data.getAdditionData().get("invite"))).ifPresent(invite -> {
            surveyCompletedRecord.setInviteId(Long.valueOf(ObjectUtils.toString(invite)));
        });
        auditContent.setSurveyCompletedRecord(surveyCompletedRecord);
        repository.save(auditContent.getSurveyCompletedRecord());
    }

    /**
     * 匹配用户已作答的问卷
     * @param openId
     * @param sids
     * @return
     */
    public List<SurveyCompletedRecord> getUserCompletedSurveys(String openId, List<String> sids){
        return completedRecordRepository.findAllByOpenIdAndSidIn(openId,sids);
    }

    /**
     * 获取用户完成问卷的总数
     * @param openId
     * @return
     */
    public long countCompleteSurveys(String openId) {
        return completedRecordRepository.countByOpenId(openId);
    }

}
