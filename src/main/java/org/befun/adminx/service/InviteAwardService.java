package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.ScoreType;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.DeliveryTaskDto;
import org.befun.adminx.entity.InviteAwardRecord;
import org.befun.adminx.entity.InviteAwardRecordDto;
import org.befun.adminx.repository.InviteAwardRecordRepository;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/26 16:14
 */
@Service
@Slf4j
public class InviteAwardService extends BaseService<InviteAwardRecord, InviteAwardRecordDto, InviteAwardRecordRepository> {

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private CommunityUserScoresService communityUserScoresService;

    public InviteAwardRecord getRecordByCuid(Long cuid) {
        Optional<InviteAwardRecord> optional = repository.findFirstByCuid(cuid);
        if (optional.isPresent()) return optional.get();
        else return null;
    }

    /**
     * 未领取的邀请积分记录
     * @param cuid
     * @param inviteId
     * @param score
     * @param taskId
     * @param scoreType
     */
    public void insertInviteAward(Long cuid, Long inviteId, Integer score, Long taskId, ScoreType scoreType) {
        if(cuid == null || inviteId == null || score == 0 || taskId == null) return;
        if(getRecordByCuid(cuid) != null) return;
        Date expireTime = DateFormatter.addDays(null, 7);
        repository.save(new InviteAwardRecord(cuid, inviteId, score, scoreType, taskId, expireTime));
    }

    /**
     * 给邀请人 增加邀请奖励
     * @param user
     */
    public void addInviteAward(CommunityUser user) {
        InviteAwardRecord record = getRecordByCuid(user.getId());
        Optional.ofNullable(record).ifPresent(r -> {
            if(r.expireTime.after(new Date()) && !record.getStatus()) {
                CommunityUser inviteUser = communityUserService.requireUser(record.getInviteId());
                DeliveryTaskDto taskDto = deliveryService.findOne(record.getTaskId());
                if(inviteUser != null && taskDto != null && record.getScore() > 0) {
                    //增加积分
                    inviteUser.setUserScore(inviteUser.getUserScore().intValue() + record.getScore());
                    inviteUser.setAccumulativeScore(inviteUser.getAccumulativeScore().intValue() + record.getScore());
                    communityUserService.save(inviteUser);

                    //填答问卷只需要写入积分记录
                    communityUserScoresService.addScoreRecord(inviteUser, record.getScore(), record.getScoreType(), new SurveySimpleDto(taskDto.getSid(), taskDto.getName(), user.getNickName()), null);
                    //修改用户邀请奖励的领取状态
                    user.setInviteAward(true);
                    //更改记录状态
                    record.setStatus(true);
                    repository.save(record);
                }
            }
        });
    }
}
