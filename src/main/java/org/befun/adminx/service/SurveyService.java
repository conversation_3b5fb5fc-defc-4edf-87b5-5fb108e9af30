package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.community.SimpleCommunityUserDto;
import org.befun.adminx.dto.survey.SurveyResponseFetchRequestDto;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyDto;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.repository.SurveyRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyService extends BaseService<Survey, SurveyDto, SurveyRepository> {

    @Autowired
    private ResponseService responseService;

    @Autowired
    private CommunityUserService communityUserService;

    /**
     * 获取答题数据 (TBD), 数据库连表查询构建答题数据
     */
    public Map<String, Object> getResponseContext(SurveyDto surveyDto, SurveyResponseFetchRequestDto fetchRequestDto) {
        //获取第一条或者最后一条记录
        SurveyResponse response = responseService.getOneSurveyResponse(surveyDto, fetchRequestDto);
        if(response == null) {
            return null;
        }
        return responseService.getResponseContext(response);
    }

    /**
     * 获取问卷-不存在报错
     *
     * @param surveyId
     * @return
     */
    public Survey requireSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = repository.findById(surveyId);
        Survey survey = surveyOptional.orElse(null);

        if (survey == null) {
            throw new BadRequestException("问卷不存在");
        }

        return survey;
    }

    /**
     * 获取模板问卷
     * @param orgId
     * @param userId
     * @return
     */
    public List<Survey> getTemplateSurveys(Long orgId, Long userId) {
        Optional<List<Survey>> list = repository.findAllByOrgIdAndAndUserId(orgId, userId);
        return list.get();
    }

    /**
     * *根据问卷获取用户的cuid
     * @param surveyId
     * @return
     */
    public SurveySimpleDto getSimpleSurveyBySurveyId(Long surveyId) {
        //获取survey
        Survey survey = requireSurvey(surveyId);
        //获取openid集合
        List<String> openidList = responseService.getOpenIdsBySurveyId(surveyId);
        //获取cuid集合
        List<SimpleCommunityUserDto> simpleUsers = communityUserService.getSimpleCommunityUserByOpenIds(openidList);
        List<Long> cuidList = new ArrayList<>();
        if(simpleUsers != null || simpleUsers.size() > 0) {
            cuidList = simpleUsers.stream().map(user -> user.getId()).collect(Collectors.toList());
        }
        return new SurveySimpleDto(surveyId.toString(), survey.getTitle(), null, cuidList);
    }
}
