package org.befun.adminx.service;

import com.github.binarywang.wxpay.exception.WxPayException;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.BillStates;
import org.befun.adminx.constant.ScoreType;
import org.befun.adminx.constant.WechatDrawCashType;
import org.befun.adminx.dto.community.DrawCashDto;
import org.befun.adminx.dto.community.DrawCashResult;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.WechatDrawCashRecord;
import org.befun.adminx.exception.WechatCallBackException;
import org.befun.adminx.service.wechat.WechatDrawCashRecordService;
import org.befun.adminx.service.wechat.WechatPayService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.transfer.TransferBillsNotifyResult;
import org.befun.extension.dto.transfer.TransferBillsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class CommunityUserDrawCashService extends CommunityUserService {

    @Autowired
    private WechatPayService wechatPayService;

    @Autowired
    private WechatDrawCashRecordService wechatDrawCashRecordService;

    @Autowired
    private CommunityUserScoresService communityUserScoresService;

    /**
     * *提现--发起商家转账
     *
     * @param request
     * @param cashDto
     * @return
     */
    @Override
    @Transactional
    public DrawCashResult drawCash(HttpServletRequest request, DrawCashDto cashDto) {
        CommunityUser communityUser = requireUser(cashDto.getOpenId());
        checkUserStatus(mapToDto(communityUser));

        if (cashDto.getAmount() == null || cashDto.getAmount() <= 0) {
            return new DrawCashResult(false, "提现失败");
        }

        if (cashDto.getAmount() > communityUser.getUserScore()) {
            throw new BadRequestException("提现金额不能超过可用余额");
        }

        Optional<WechatDrawCashRecord> drawCashRecord = wechatDrawCashRecordService
                .findProcessingTransferBillRecord(cashDto.getOpenId());
        if (drawCashRecord.isPresent()) {
            return new DrawCashResult(false, "提现正在处理中");
        }
        String billNo = wechatPayService.generateBillNo();
        //记录提现记录
        WechatDrawCashRecord record = wechatDrawCashRecordService.addRecord(cashDto.getOpenId(),
                WechatDrawCashType.TRANSFER,cashDto.getAmount(),
                null, null, null, billNo, null, null);

        try {
            TransferBillsResult result = wechatPayService
                    .transferBill(cashDto.getOpenId(), cashDto.getAmount(), billNo);
            log.info("发起提现结果：{}", JsonHelper.toJson(result));
            updateDrawCashRecord(record, result);
            if (BillStates.WAIT_USER_CONFIRM.name().equals(result.getState())) {
                return new DrawCashResult(true, "发起提现成功", result.getState(),
                        result.getPackageInfo());
            } else {
                return new DrawCashResult(false, "发起提现失败", result.getState(), null);
            }
        } catch (WxPayException e) {
            return new DrawCashResult(e.getErrCode());
        } catch (Exception e) {
            e.printStackTrace();
            return new DrawCashResult(false, "提现失败");
        } finally {
            record.setStates(BillStates.IN_ERROR);
            wechatDrawCashRecordService.save(record);
        }
    }

    /**
     * * 撤销提现
     *
     * @param cashDto
     * @return
     */
    @Transactional
    public DrawCashResult drawCashCancel(DrawCashDto cashDto) {
        CommunityUser communityUser = requireUser(cashDto.getOpenId());
        checkUserStatus(mapToDto(communityUser));

        try {
            Optional<WechatDrawCashRecord> drawCashRecord = wechatDrawCashRecordService
                    .findProcessingTransferBillRecord(cashDto.getOpenId());
            if (drawCashRecord.isPresent()) {
                wechatPayService.cancelTransferBill(drawCashRecord.get());
            }
            return new DrawCashResult(true, "撤销提现成功");
        } catch (WxPayException e) {
            return new DrawCashResult(e.getErrCode());
        } catch (Exception e) {
            e.printStackTrace();
            return new DrawCashResult(false, "撤销提现失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleCallbackResult(TransferBillsNotifyResult result) {
        CommunityUser communityUser = requireUser(result.getResult().getOpenid());
        if (communityUser == null || communityUser.getStatus() == 2) {
            log.warn("提现回调, 用户不存在或已被拉黑:{}", result.getResult().getOpenid());
            return;
        }
        try {
            ScoreType type = ScoreType.T_A;
            String message = result.getResult().getFailReason();
            Integer balance = communityUser.getUserScore() - result.getResult().getTransferAmount();
            if (BillStates.SUCCESS.name().equals(result.getResult().getState())) {
                type = ScoreType.T_A;
                communityUser.setUserScore(balance);
                repository.save(communityUser);
            } else if (BillStates.FAIL.name().equals(result.getResult().getState())) {
                type = ScoreType.T_F;
            }
            communityUserScoresService
                    .addScoreRecord(communityUser, result.getResult().getTransferAmount(), type,
                            new SurveySimpleDto(), message, null);
        } catch (Exception ex) {
            log.error("更新用户积分记录失败,openId:{}, error:{}", result.getResult().getOpenid(),
                    ex.getMessage());
            throw new BadRequestException();
        }

        try {
            Optional<WechatDrawCashRecord> drawCashRecordOpt = wechatDrawCashRecordService
                    .findTransferBillRecordByBillNo(result.getResult().getOutBillNo(),
                            result.getResult().getOpenid());
            if (drawCashRecordOpt.isPresent()) {
                WechatDrawCashRecord record = drawCashRecordOpt.get();
                record.setStates(BillStates.valueOf(result.getResult().getState()));
                record.setReturnMsg(JsonHelper.toJson(result.getResult()));
                wechatDrawCashRecordService.save(record);
            }
        } catch (Exception ex) {
            log.error("更新用户提现记录失败：billNo:{}, openId:{}, error: {}",
                    result.getResult().getOutBillNo(), result.getResult().getOpenid(),
                    ex.getMessage());
            throw new BadRequestException();
        }
    }

    private void updateDrawCashRecord(WechatDrawCashRecord record, TransferBillsResult result) {
        record.setMchBillNo(result.getTransferBillNo());
        record.setReturnCode(result.getState());
        record.setReturnMsg(result.getFailReason());
        record.setPackageInfo(result.getPackageInfo());
        record.setStates(BillStates.valueOf(result.getState()));
        wechatDrawCashRecordService.save(record);
    }
}
