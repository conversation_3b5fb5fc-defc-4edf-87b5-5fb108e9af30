package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.dto.channel.ChannelRefundInfo;
import org.befun.adminx.entity.OrganizationOrder;
import org.befun.adminx.entity.SampleOrder;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.adminx.entity.survey.SurveyChannelDto;
import org.befun.adminx.repository.OrganizationOrderRepository;
import org.befun.adminx.repository.SurveyChannelRepository;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChannelService extends CustomEmbeddedService<SurveyChannel, SurveyChannelDto, SurveyChannelRepository> {

    @Autowired
    private SurveyChannelRepository channelRepository;

    @Autowired
    WechatConfigureService wechatConfigureService;

    @Autowired
    private SampleOrderService sampleOrderService;

    @Autowired
    private OrganizationOrderRepository organizationOrderRepository;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    /**
     * 获取渠道信息
     *
     * @param channelId
     * @return
     */
    public SurveyChannel requireChannel(@NotNull Long channelId) {
        Optional<SurveyChannel> surveyChannelOptional = channelRepository.findById(channelId);
        if (surveyChannelOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return surveyChannelOptional.get();
    }

    /**
     * 更新渠道状态
     *
     * @param channelId
     * @param status
     */
    public SurveyChannel updateChannelStatus(Long channelId, ChannelStatus status) {
        return updateChannelStatus(channelId, status, null);
    }

    public SurveyChannel updateChannelStatus(Long channelId, ChannelStatus status, String rejectMessage) {
        SurveyChannel surveyChannel = get(channelId);
        if (surveyChannel == null) return null;
        surveyChannel.setStatus(status);
        surveyChannel.setRejectMessage(rejectMessage);
        channelRepository.save(surveyChannel);
        //更新样本订单渠道状态
        sampleOrderService.updateStatus(status, surveyChannel.getSid(), surveyChannel.getId());
        return surveyChannel;
    }

    /**
     * 更新渠道状态驳回
     */
    public void updateChannelReject(Long channelId, String rejectMessage, SampleOrder sampleOrder) {
        SurveyChannel surveyChannel = get(channelId);
        if (surveyChannel == null) {
            return;
        }
        surveyChannel.setStatus(ChannelStatus.REJECT);
        surveyChannel.setRejectMessage(rejectMessage);
        channelRepository.save(surveyChannel);
        //更新样本订单渠道状态
        sampleOrder.setChannelStatus(ChannelStatus.REJECT);
        sampleOrder.setRefundPrice(ChannelRefundInfo.refundPrice(sampleOrder));
        sampleOrderService.save(sampleOrder);
    }

    /**
     * 更新渠道状态结束和回收数量
     */
    public SurveyChannel updateChannelCompleteAndRecycle(Long channelId, int recycle) {
        SurveyChannel surveyChannel = get(channelId);
        if (surveyChannel == null) return null;
        surveyChannel.setStatus(ChannelStatus.COMPLETE);
        channelRepository.save(surveyChannel);
        //更新样本订单渠道状态
        sampleOrderService.updateSampleComplete(surveyChannel.getSid(),channelId,recycle);
        return surveyChannel;
    }

    /**
     * 更新渠道退款信息
     */
    public ChannelRefundInfo updateChannelRefundInfo(SurveyChannel surveyChannel, SampleOrder sampleOrder, OrganizationOrder order) {
        ChannelRefundInfo refundInfo = ChannelRefundInfo.build(sampleOrder, order);
        surveyChannel.setOrderRefund(JsonHelper.toJson(refundInfo));
        channelRepository.save(surveyChannel);
        return refundInfo;
    }

    /**
     * 更新渠道状态和微信设置
     *
     * @param channelId
     * @param status
     */
    public void updateChannelStatusAndWechatConfig(Long channelId, ChannelStatus status) {
        SurveyChannel surveyChannel = get(channelId);
        if (surveyChannel == null) return;
        surveyChannel.setStatus(status);
        surveyChannel.setEnableWechatLimit(false);
        surveyChannel.setEnableWechatReplyOnly(false);
        channelRepository.save(surveyChannel);
    }

    /**
     * 获取问卷渠道列表
     *
     * @param surveyId
     * @return
     */
    public List<SurveyChannel> getChannelList(Long surveyId) {
        return channelRepository.findAllBySid(surveyId);
    }
}
