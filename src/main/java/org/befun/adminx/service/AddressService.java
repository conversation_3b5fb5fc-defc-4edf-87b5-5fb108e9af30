package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.Address;
import org.befun.adminx.entity.AddressDto;
import org.befun.adminx.repository.AddressRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 10:00
 */
@Service
@Slf4j
public class AddressService extends BaseService<Address, AddressDto, AddressRepository> {

}
