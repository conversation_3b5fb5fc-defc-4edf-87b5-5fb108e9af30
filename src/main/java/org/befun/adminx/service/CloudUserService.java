package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.sync.CloudUserDto;
import org.befun.adminx.dto.sync.SyncCloudUserMessageDto;
import org.befun.adminx.property.AdminProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;


/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/20 18:03
 */
@Service
@Slf4j
public class CloudUserService {

    @Autowired
    private AdminProperty adminProperty;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void setCloudUser(SyncCloudUserMessageDto messageDto) {
        if(messageDto ==null) {
            return;
        }

        String sql = String.format("select uid,users_name,co_code,co_name from %s.users where co_code = '%s';", adminProperty.getSurveyPlusDbName(), messageDto.getMessage().getCode());
        log.info("select sql: {}", sql);

        try {
            CloudUserDto cloudUserDto = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CloudUserDto.class));
            if(cloudUserDto.getUid() != null) {
                String updateSql = String.format("update %s.users set co_name='%s' where co_code = '%s';",
                        adminProperty.getSurveyPlusDbName(), messageDto.getMessage().getName(), messageDto.getMessage().getCode());
                log.info("updateSql sql: {}", updateSql);

                //更新co_name
                jdbcTemplate.update(updateSql);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
