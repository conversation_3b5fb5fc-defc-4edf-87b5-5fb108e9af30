package org.befun.adminx.service.audit;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.AuditType;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.dto.survey.SurveyResponseSimilarityDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.survey.SurveyCompletedRecord;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.core.exception.BadRequestException;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AuditContent {

    private Boolean returnFlag = true;//返回状态 false没有消费 true消费成功
    private Boolean auditFlag = false;//审核状态 false 审核失败 true 审核成功
    private Boolean similarityFlag = false;//高度相似 false 不相似 true 相似度极高

    private SurveyResponseMessageDto data;//答卷信息 redis队列的
    private Long responseId;//答卷id
    private Long surveyId;//问卷id
    private Long channelId;//渠道id
    private String taskId;//任务id
    private Long cuid;//cuid
    private String openId;//微信openId
    private CommunityUser userInfo;//社区用户信息
    private CommunityUser inviteUser;//邀请人社区用户信息
    private SurveyCompletedRecord surveyCompletedRecord;//填答记录
    private DeliveryTask deliveryTask;//投放任务
    private SurveyResponse response;//答卷数据
    private AuditRecord auditRecord;//审核记录
    private Audit audit;//审核规则
    private AuditResultDto auditResultDto;//审核结果
    private AuditResultJsonDto auditResultJsonDto;//保存到survey_response的审核结果
    private AuditType auditType = AuditType.AUTO;//审核类型 自动 半自动 手动
    private List<CommunityUserScores> userScoresList = new ArrayList<>();
    private List<SurveyResponseSimilarityDto> similarityDtoList = new ArrayList<>();//相似度高的答卷数据

    /**
     * 校验参数
     */
    public void checkParams() {
        if ((surveyId == null && StringUtils.isEmpty(data.getTrackId())) || data == null) {
            this.returnFlag = false;
            throw new BadRequestException("投放任务id不存在");
        }
        if (data.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS) {
            this.returnFlag = false;
            throw new BadRequestException("非调研家社区问卷");
        }

        if (data.getResponseId() == null || data.getResponseId() <= 0) {
            this.returnFlag = false;
            throw new BadRequestException("答卷id不存在");
        }
    }

    public AuditContent(SurveyResponseMessageDto data) {
        this.data = data;
        this.surveyId = data.getSurveyId();
        this.responseId = data.getResponseId();
        this.taskId = data.getTrackId();
        this.openId = data.getOpenid();
        this.channelId = data.getChannelId();
    }

    public AuditContent(DeliveryTask deliveryTask) {


    }

}
