package org.befun.adminx.service.audit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.*;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.entity.DeliveryTask;
import org.befun.adminx.repository.CommunityUserRepository;
import org.befun.adminx.repository.DeliveryTaskRepository;
import org.befun.adminx.service.*;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ConsumerService {

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    private SurveyCompletedRecordService surveyCompletedRecordService;

    @Autowired
    private SurveySendRecordService surveySendRecordService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private AuditRecordService auditRecordService;

    @Autowired
    private CommunityUserScoresService communityUserScoresService;

    @Autowired
    private CommunityUserRepository communityUserRepository;

    @Autowired
    private DeliveryTaskRepository deliveryTaskRepository;

    @Transactional
    public void consumer(SurveyResponseMessageDto data) {
        AuditContent auditContent = new AuditContent(data);
        try {
            auditContent.checkParams();
            loadSurveyResponse(auditContent);
            loadCommunityUser(auditContent);
            addSurveyCompleteRecord(auditContent);
            loadDeliveryTask(auditContent);
            loadAuditRecord(auditContent);
            startToAudit(auditContent);
            saveData(auditContent);
        } catch (BadRequestException be) {
            be.printStackTrace();
            log.info("surveyId:{} responseId:{} consumer fail, BadRequestException message :{}", auditContent.getSurveyId().toString(), auditContent.getResponseId(), be.getMessage());
            //审核异常的答卷 标记
            if (auditContent.getReturnFlag() && auditContent.getResponse() != null && auditContent.getResponse().getStatus() != ResponseStatus.QUOTA_FUll)
                deliveryService.auditError(auditContent.getSurveyId().toString(), auditContent.getResponseId(), be.getMessage());
            //相似度高的答卷 标记
            if (auditContent.getSimilarityFlag())
                responseService.updateSimilaritySurveyResponse(auditContent.getSimilarityDtoList(), auditContent.getResponse().getSequence());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("surveyId:{} responseId:{} consumer fail, Exception message :{}", auditContent.getSurveyId().toString(), auditContent.getResponseId(), e.getMessage());
        }
    }

    /**
     * load 社区用户信息
     * @param auditContent
     */
    public void loadCommunityUser(AuditContent auditContent) {
        if(StringUtils.isEmpty(auditContent.getOpenId()) && StringUtils.isEmpty(auditContent.getData().getExternalUserId()) && StringUtils.isEmpty(auditContent.getData().getCustomerName())) {
            throw new BadRequestException("openid is null");
        }

        if(StringUtils.isEmpty(auditContent.getOpenId()) && StringUtils.isNotEmpty(auditContent.getData().getCustomerName())) {
            Long cuid = Long.parseLong(auditContent.getData().getCustomerName());
            auditContent.setUserInfo(communityUserService.requireUser(cuid));
        } else {
            auditContent.setUserInfo(communityUserService.requireUser(auditContent.getOpenId()));
        }
        if (auditContent.getUserInfo() == null)
            throw new BadRequestException("community user is null");

        //增加用户问卷填答数量
        auditContent.getUserInfo().setSurveyCount(auditContent.getUserInfo().getSurveyCount() == null ? 1 : auditContent.getUserInfo().getSurveyCount() + 1);
        auditContent.setCuid(auditContent.getUserInfo().getId());
        auditContent.setOpenId(auditContent.getUserInfo().getOpenId());
        auditContent.getData().setOpenid(auditContent.getUserInfo().getOpenId());
        auditContent.getResponse().setOpenid(auditContent.getOpenId());
    }

    /**
     *  load 答卷记录
     * @param auditContent
     */
    public void loadSurveyResponse(AuditContent auditContent) {
        auditContent.setResponse(responseService.get(auditContent.getResponseId()));
        if (auditContent.getResponse() == null) {
            auditContent.setReturnFlag(false);
            throw new BadRequestException("答卷记录不存在");
        }
    }
    /**
     * 增加问卷填答记录
     * @param auditContent
     */
    @Transactional
    public void addSurveyCompleteRecord(AuditContent auditContent) {
        //如果已有填答记录 不做处理
        if(surveyCompletedRecordService.verifyIsCompletedNew(auditContent)) {
            throw new BadRequestException("survey is complete");
        }
        //写入填答记录
        surveyCompletedRecordService.insertCompletedRecordNew(auditContent);
    }

    /**
     * load投放任务
     * @param auditContent
     */
    @Transactional
    public void loadDeliveryTask(AuditContent auditContent) {
        DeliveryTask deliveryTask = deliveryService.getDeliveryTask(auditContent.getSurveyId().toString(), auditContent.getTaskId());
        if (deliveryTask == null) throw new BadRequestException("task is not exists");

        auditContent.setDeliveryTask(deliveryTask);
        auditContent.setTaskId(deliveryTask.getId().toString());
        //追访任务 更新追访记录
        if (deliveryTask.getTaskType() == TaskType.FOLLOW) {
            surveySendRecordService.updateSurveySendRecordStatus(deliveryTask.getId(), SubmitStatus.SUBMIT, auditContent.getCuid(), auditContent.getData().getStatus(),true);
        }

        //提前结束的问卷 不纳入计算
        ResponseStatus status = auditContent.getData().getStatus();
        if (status != ResponseStatus.WAIT_AUDIT) {
            throw new BadRequestException("survey status is " + auditContent.getData().getStatus());
        }

        //增加待审核数量 和填答人数
        auditContent.getDeliveryTask().setAuditWaitCount(deliveryTask.getAuditWaitCount() + 1);
        auditContent.getDeliveryTask().setReplyCount(deliveryTask.getReplyCount() + 1);
        //如果问卷回收数量达到总回收数量 暂停该投放任务 追访任务不暂停
        if(deliveryTask.getTaskType() == TaskType.DELIVERY && (deliveryTask.getTotal() - deliveryTask.getAuditWaitCount() - deliveryTask.getAuditCount() <= 0) && deliveryTask.getStatus() == DeliveryTaskStatus.COLLECTING) {
            auditContent.getDeliveryTask().setStatus(DeliveryTaskStatus.PAUSE);
            //如果渠道id不为空 设置渠道状态为回收中
            if (auditContent.getChannelId() > 0L && auditContent.getChannelId() != null) {
                channelService.updateChannelStatus(auditContent.getChannelId(), ChannelStatus.PAUSE);
            }
        }
    }

    /**
     * load审核记录
     * @param auditContent
     */
    public void loadAuditRecord(AuditContent auditContent) {
        auditContent.setAuditRecord(auditRecordService.reqireAuditRecord(Long.parseLong(auditContent.getTaskId()), auditContent.getResponseId()));
        if (auditContent.getAuditRecord() != null)
            throw new BadRequestException("该答卷已经审核，请勿重复审核");
    }

    /**
     * 自动审核
     * @param auditContent
     */
    @Transactional
    public void startToAudit(AuditContent auditContent) {
        //增加冻结积分
        communityUserService.addUserScoreNew(auditContent, ScoreType.F, null);

        log.info("start auto audit,surveyId:{},responseId:{},openId:{}", auditContent.getSurveyId(), auditContent.getResponseId(), auditContent.getOpenId());
        auditService.auditByTaskNew(auditContent);
        //如果自动审核语法未配置 或者未开启自动审核 直接返回
        if (auditContent.getReturnFlag()) {
            auditContent.setAuditResultJsonDto(new AuditResultJsonDto(auditContent.getAuditResultDto().getScore(), AuditType.AUTO, auditContent.getAuditResultDto().getFailRuleName()));

            if (auditContent.getAuditResultDto().getPass()) {//审核通过
                auditContent.setAuditFlag(true);
                deliveryService.auditPassNew(auditContent);
            } else {//审核失败
                deliveryService.auditFailNew(auditContent);
            }
        }
    }

    /**
     * 保存填答记录
     * @param auditContent
     */
    public void saveCompleteData(AuditContent auditContent) {
        log.info("start to save complete data,surveyId:{},responseId:{},openId:{}", auditContent.getSurveyId(), auditContent.getResponseId(), auditContent.getOpenId());
        //保存填答记录
        if (auditContent.getSurveyCompletedRecord() != null) surveyCompletedRecordService.save(auditContent.getSurveyCompletedRecord());
    }
    /**
     * 保存数据
     * @param auditContent
     */
    @Transactional
    public void saveData(AuditContent auditContent) {
        log.info("start to save data,surveyId:{},responseId:{},openId:{}", auditContent.getSurveyId(), auditContent.getResponseId(), auditContent.getOpenId());

        //保存投放任务
        if (auditContent.getDeliveryTask() != null) deliveryTaskRepository.save(auditContent.getDeliveryTask());
        //保存社区用户信息
        if (auditContent.getUserInfo() != null) communityUserRepository.save(auditContent.getUserInfo());
        //保存邀请人社区用户信息
        if (auditContent.getInviteUser() != null) communityUserRepository.save(auditContent.getInviteUser());
        //保存答卷数据
        if (auditContent.getResponse() != null){
            responseService.save(auditContent.getResponse());
            if(auditContent.getReturnFlag() && auditContent.getResponse().getStatus() == ResponseStatus.FINAL_SUBMIT){
                deliveryService.updateResponseNum(auditContent.getSurveyId(),1);
            }
        }
        //保存审核记录
        if (auditContent.getAuditRecord() != null) auditRecordService.save(auditContent.getAuditRecord());
        //保存积分记录
        if (auditContent.getUserScoresList() != null && !auditContent.getUserScoresList().isEmpty()) communityUserScoresService.saveAll(auditContent.getUserScoresList());
    }
}
