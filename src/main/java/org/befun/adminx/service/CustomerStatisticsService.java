package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Internal;
import org.befun.adminx.dto.query.CountDto;
import org.befun.adminx.entity.CustomerStatistics;
import org.befun.adminx.entity.CustomerStatisticsDto;
import org.befun.adminx.entity.XmPlusUser;
import org.befun.adminx.repository.CustomerStatisticsRepository;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/9 15:51
 */
@Slf4j
@Service
public class CustomerStatisticsService extends BaseService<CustomerStatistics, CustomerStatisticsDto, CustomerStatisticsRepository> {

    @Autowired
    private UserService userService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 企业客户问卷 预警 看榜 数据统计
     */
    public void syncData() {
        List<XmPlusUser> userList = userService.getAdminUserList();
        if(userList == null || userList.isEmpty()) return;
        userList.forEach(this::updateData);
    }

    public void updateData(XmPlusUser xmPlusUser) {
        Long orgId = xmPlusUser.getOrganization().getId();
        if(orgId == null || orgId == 0) return;
        Integer countSurvey = countSurvey(orgId);
        Integer countSurveyResponse = countSurveyResponse(orgId);
        Integer countJourneyMap = countJourneyMap(orgId);
        Integer countEventResult = countEventResult(orgId);
        Integer countBiDashBoard = countBiDashBoard(orgId);
        Integer countCustomer = countCustomer(orgId);
        Integer countUser = countUser(orgId);
        Integer countResponseLastMonth = countResponseLastMonth(orgId);
        Integer allResponseCount = countSurveyResponseTotal(orgId);
        Integer allResponseCountLastMonth = countResponseLastMonthTotal(orgId);
        CustomerStatistics customerStatistics = repository.findOneById(orgId).orElse(new CustomerStatistics());
        customerStatistics.setId(orgId);
        customerStatistics.setSurveyQuantity(countSurvey);
        customerStatistics.setSurveyResponseQuantity(countSurveyResponse);
        customerStatistics.setJourneyMapQuantity(countJourneyMap);
        customerStatistics.setEventQuantity(countEventResult);
        customerStatistics.setBiDashBoardQuantity(countBiDashBoard);
        customerStatistics.setCustomerQuantity(countCustomer);
        customerStatistics.setAccountQuantity(countUser);
        repository.save(customerStatistics);

        //保存到user表
        xmPlusUser.setSurveyCount(countSurvey);
        xmPlusUser.setResponseCount(countSurveyResponse);
        xmPlusUser.setEventCount(countEventResult);
        xmPlusUser.setResponseCountLastMonth(countResponseLastMonth);
        xmPlusUser.setResponseTotal(allResponseCount);
        xmPlusUser.setResponseTotalLastMonth(allResponseCountLastMonth);
        userService.save(xmPlusUser);

    }

    /**
     * 问卷总数
     * @param orgId
     * @return
     */
    public Integer countSurvey(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.survey where deleted=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 问卷回收总数
     * @param orgId
     * @return
     */
    public Integer countSurveyResponse(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.survey_response where status = 1 and deleted=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 最近一个月问卷回收数
     * @param orgId
     * @return
     */
    public Integer countResponseLastMonth(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.survey_response where status = 1 and deleted=0 and org_id = %d and create_time > '%s'", orgId, DateFormatter.getStringTime(DateFormatter.getFirstDayOfMonth(new Date())));
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 问卷投放总数
     * @param orgId
     * @return
     */
    public Integer countSurveyResponseTotal(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.survey_response where org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 问卷本月投放总数
     * @param orgId
     * @return
     */
    public Integer countResponseLastMonthTotal(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.survey_response where org_id = %d and create_time > '%s'", orgId, DateFormatter.getStringTime(DateFormatter.getFirstDayOfMonth(new Date())));
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 旅程总数
     * @param orgId
     * @return
     */
    public Integer countJourneyMap(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.journey_map where is_delete=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 预警总数
     * @param orgId
     * @return
     */
    public Integer countEventResult(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.event_result where deleted=0 and org_id = %d and warning_level > 0", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * BI看板总数
     * @param orgId
     * @return
     */
    public Integer countBiDashBoard(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.bi_dashboard where is_folder=0 and hide=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 客户总数
     * @param orgId
     * @return
     */
    public Integer countCustomer(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.customer where is_delete=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }

    /**
     * 账号总数
     * @param orgId
     * @return
     */
    public Integer countUser(Long orgId) {
        if(orgId == null || orgId == 0l) return 0;
        String sql = String.format("select count(1) count from cem_platform.user where is_delete=0 and org_id = %d", orgId);
        CountDto count = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CountDto.class));
        return count.getCount();
    }
}
