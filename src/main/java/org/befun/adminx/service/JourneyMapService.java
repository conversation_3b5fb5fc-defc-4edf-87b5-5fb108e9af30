package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.JourneyMap;
import org.befun.adminx.entity.JourneyMapDto;
import org.befun.adminx.repository.JourneyMapRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/11 14:36
 */
@Service
@Slf4j
public class JourneyMapService extends BaseService<JourneyMap, JourneyMapDto, JourneyMapRepository> {

    public List<JourneyMap> getTemplateJourneyMap(Long orgId, Long userId) {
        Optional<List<JourneyMap>> list = repository.findAllByOrgIdAndAndUserId(orgId, userId);
        return list.get();
    }
}
