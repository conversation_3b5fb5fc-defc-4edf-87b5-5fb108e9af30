package org.befun.adminx.service;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.IpResolverService;
import cn.hanyi.common.ip.resolver.RegionInfo;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.survey.SurveyTrackingDataDto;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ua_parser.Parser;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TrackingService {
    private final Parser uaParser = new Parser();

    @Autowired
    private IpResolverService ipResolverService;

    @Autowired
    private IpResolverProperties properties;

    public SurveyTrackingDataDto parseRequest(String ipAddress) {
        SurveyTrackingDataDto result = new SurveyTrackingDataDto();
        result.setIp(ipAddress);

        RegionInfo regionInfo = ipResolverService.resolveIpToRegion(ipAddress);
        if (regionInfo != null) {
            result.setCountry(regionInfo.getCountry());
            result.setCity(regionInfo.getCity());
            result.setProvince(regionInfo.getProvince());
            result.setDistrict(regionInfo.getDistrict());
        }
        System.out.println(JsonHelper.toJson(result));
        return result;
    }

    public SurveyTrackingDataDto parseLocation(Float latitude, Float longitude) {
        SurveyTrackingDataDto result = new SurveyTrackingDataDto();
        RegionInfo regionInfo = ipResolverService.resolveLocationToRegion(latitude, longitude);
        // 日志ipResolver
        log.info("ipResolver: {}", JsonHelper.toJson(properties));
        if (regionInfo != null) {
            result.setCountry(regionInfo.getCountry());
            result.setCity(regionInfo.getCity());
            result.setProvince(regionInfo.getProvince());
            result.setDistrict(regionInfo.getDistrict());
        }
        System.out.println(JsonHelper.toJson(result));
        return result;
    }

}
