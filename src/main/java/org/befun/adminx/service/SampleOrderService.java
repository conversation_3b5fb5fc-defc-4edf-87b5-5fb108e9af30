package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.OrderStatus;
import org.befun.adminx.constant.PayType;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.channel.ChannelRefundInfo;
import org.befun.adminx.dto.event.EventType;
import org.befun.adminx.dto.event.SurveyChannelOperationDto;
import org.befun.adminx.dto.message.SurveyPlusConfigureDto;
import org.befun.adminx.dto.notify.ContentDto;
import org.befun.adminx.dto.order.QuoteRequest;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderQuoted;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderRefund;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderReject;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.adminx.repository.*;
import org.befun.adminx.trigger.AdminxEventTrigger;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/10 11:55
 */
@Service
@Slf4j
public class SampleOrderService extends BaseService<SampleOrder, SampleOrderDto, SampleOrderRepository> {

    @Value("${befun.admin.xm-plus-url}")
    private String xmPlusUrl;

    @Value("${community.uri.sample-order-notice}")
    private String sampleOrderNoticeUri;

    @Value("${community.uri.adminx-login}")
    private String adminxLoginUri;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private HybridSurveyService hybridSurveyService;

    @Autowired
    private org.befun.adminx.repository.UserRepository UserRepository;

    @Autowired
    private CemEventRepository cemEventRepository;

    @Autowired
    private TaskService taskService;

    @Autowired
    private OrganizationOrderRepository organizationOrderRepository;

    @Autowired
    private OrganizationRechargeRepository organizationRechargeRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AdminxEventTrigger adminxEventTrigger;


    @Override
    protected void afterMapToDto(List<SampleOrder> entity, List<SampleOrderDto> dto) {
        super.afterMapToDto(entity, dto);
        dto.forEach(order -> {
            order.setSurveyStatus(hybridSurveyService.getSurveyStatus(SurveyType.XM_PLUS, order.getSurveyId().toString()));
            Optional<XmPlusUser> optional = UserRepository.findFirstByOrganization_IdAndIsAdminOrderByIdDesc(order.getOrgId(), 1);
            optional.ifPresent(xm -> {
                order.setOrgCode(xm.getOrganization() == null ? null : xm.getOrganization().getCode());
                order.setOrgName(xm.getOrganization() == null ? null : xm.getOrganization().getName());
                order.setMobile(xm.getMobile());
                order.setEmail(xm.getEmail());
                order.setRealName(xm.getTrueName());
            });
            Optional<SurveyChannel> surveyChannel = surveyChannelRepository.findById(order.getChannelId());
            if (surveyChannel.isPresent()) {
                order.setChannelStatus(surveyChannel.get().getStatus());
                fillChannelOrderStatus(surveyChannel.get(), order);
            }
        });
    }

    private void fillChannelOrderStatus(SurveyChannel channel, SampleOrderDto dto) {
        if ("standard".equals(channel.getOrderPayType())) {
            fillChannelOrderStatusByOrder(channel, dto);
        } else if ("manual".equals(channel.getOrderPayType())) {
            if (channel.getOrderAmount() == null || channel.getOrderAmount() < 0) {
                dto.setOrderStatus("none");
            } else {
                dto.setOrderStatus("init");
                fillChannelOrderStatusByOrder(channel, dto);
            }
        }
    }

    private void fillChannelOrderStatusByOrder(SurveyChannel i, SampleOrderDto dto) {
        // 如果已有订单，则修改为订单的状态
        OrganizationOrder order = i.getOrderId() != null ? organizationOrderRepository.findById(i.getOrderId()).orElse(null) : null;
        if (order != null) {
            // 渠道订单状态：待报价(none)、待付款(init)、已付款(success)、已退款(refund)、退款失败(refund_failure)、退款中(refund_pending)
            dto.setOrderStatus("none");
            switch (order.getStatus()) {
                case init, success, refund, refund_failure, refund_pending -> {
                    dto.setOrderStatus(order.getStatus().name());
                    updatePayTime(order, dto);
                }
            }
            dto.setNeedRefund(canRefund(dto.getQuantity(), dto.getRecycle(), order, i));
            resetRefundPrice(dto, order, i);
        }
    }

    private void resetRefundPrice(SampleOrderDto sampleOrder, OrganizationOrder order, SurveyChannel surveyChannel) {
        if (sampleOrder.getRefundPrice() == null) {
            int quantity = sampleOrder.getQuantity() == null ? 0 : sampleOrder.getQuantity();
            int recycle = sampleOrder.getRecycle() == null ? 0 : sampleOrder.getRecycle();
            if (
                    (surveyChannel.getStatus() == ChannelStatus.COMPLETE
                            || surveyChannel.getStatus() == ChannelStatus.REJECT
                    ) && (order.getStatus() == OrderStatus.refund
                            || order.getStatus() == OrderStatus.refund_failure
                            || order.getStatus() == OrderStatus.refund_pending
                    ) && (quantity - recycle) > 0) {
                sampleOrder.setRefundPrice(ChannelRefundInfo.refundPrice(sampleOrder));
            } else {
                sampleOrder.setRefundPrice(null);
            }
        }
    }

    private void updatePayTime(OrganizationOrder order, SampleOrderDto dto) {
        if (OrderStatus.success.equals(order.getStatus())) {
            //钱包支付
            if (PayType.wallet.equals(order.getPayType())) {
                dto.setPayTime(order.getCreateTime());
            } else {
                OrganizationRecharge recharge = organizationRechargeRepository.findFirstByOrgIdAndOrderId(order.getOrgId(), order.getId());
                if (recharge != null) {
                    dto.setPayTime(recharge.getPayTime());
                }
            }
        }
    }

    @Override
    public Page<SampleOrderDto> findAll(ResourceEntityQueryDto<SampleOrderDto> queryDto) {
        queryDto.setSorts(Sort.by("id").descending());
        if (queryDto.getQueryCriteriaList().size() > 0) {
            queryDto.getQueryCriteriaList().stream().forEach(query -> {
                if (query.getKey().equals("surveyTitle"))
                    query.setOperator(QueryOperator.LIKE);
            });
        }

        String baseSql = "SELECT * FROM (SELECT DISTINCT " +
                " o.id id, " +
                " o.org_id orgId, " +
                " o.s_id surveyId, " +
                " o.survey_title surveyTitle, " +
                " o.channel_name channelName, " +
                " o.channel_id channelId, " +
                " o.quantity quantity, " +
                " o.recycle recycle, " +
                " o.unit_price unitPrice, " +
                " o.total_price totalPrice, " +
                " o.refund_price refundPrice, " +
                " o.create_time createTime, " +
                " o.contacts contacts, " +
                " o.is_contacted isContacted, " +

                " o.count_question countQuestion, " +
                " o.gender_match genderMatch, " +
                " o.age_match ageMatch, " +
                " o.education_match educationMatch, " +
                " o.location_match locationMatch, " +
                " o.other other, " +
                " o.detail detail, " +
                " o.pay_time payTime, " +
                " o.pay_type payType, " +
                " o.pay_no payNo, " +
                " o.pay_refund_no payRefundNo, " +

                " c.`status` channelStatus,  " +
                " c.order_pay_type orderPayType, " +
                " c.order_amount orderAmount, " +
                " oo.`status` orderStatus " +
                " FROM sample_order o " +
                " INNER JOIN cem_platform.survey_channel c ON o.channel_id = c.id  " +
                " LEFT  JOIN cem_platform.organization_order oo on oo.id=c.order_id  " +
                " where c.order_pay_type = 'manual' OR ( c.order_pay_type = 'standard' AND oo.`status` in ('success','refund','refund_failure','refund_pending')) " +
                " ) t %s ORDER BY id DESC";

        //标准订单未支付不查询
        String sql = String.format(baseSql, this.condition(queryDto));

        List<SampleOrderDto> query = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SampleOrderDto.class));

        // 计算起始索引和结束索引
        int start = (queryDto.getPage() - 1) * queryDto.getLimit();
        int end = Math.min(start + queryDto.getLimit(), query.size());

        // 获取分页结果列表
        List<SampleOrderDto> pageContent = query.subList(start, end);
        this.afterMap(pageContent);
        return new PageImpl<>(pageContent, PageRequest.of(queryDto.getPage() - 1, queryDto.getLimit(), Sort.by("id").descending()), query.size());

    }

    private void afterMap(List<SampleOrderDto> dto) {
        dto.forEach(order -> {
            order.setSurveyStatus(hybridSurveyService.getSurveyStatus(SurveyType.XM_PLUS, order.getSurveyId().toString()));
            Optional<XmPlusUser> optional = UserRepository.findFirstByOrganization_IdAndIsAdminOrderByIdDesc(order.getOrgId(), 1);
            optional.ifPresent(xm -> {
                order.setOrgCode(xm.getOrganization() == null ? null : xm.getOrganization().getCode());
                order.setOrgName(xm.getOrganization() == null ? null : xm.getOrganization().getName());
                order.setMobile(xm.getMobile());
                order.setEmail(xm.getEmail());
                order.setRealName(xm.getTrueName());
            });
            Optional<SurveyChannel> surveyChannel = surveyChannelRepository.findById(order.getChannelId());
            if (surveyChannel.isPresent()) {
                order.setChannelStatus(surveyChannel.get().getStatus());
                fillChannelOrderStatus(surveyChannel.get(), order);
            }
        });
    }

    private String condition(ResourceEntityQueryDto<SampleOrderDto> queryDto) {
        StringBuilder condition = new StringBuilder(" ");
        condition.append(" where 1=1 ");
        final List<ResourceQueryCriteria> queryCriteriaList = queryDto.getQueryCriteriaList();
        queryCriteriaList.forEach(x -> {
            if ("surveyId".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " = " + x.getValue());
            } else if ("surveyTitle".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " like '%" + x.getValue() + "%'");
            } else if ("channelName".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " = '" + x.getValue() + "'");
            } else if ("channelStatus".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " = " + ChannelStatus.valueOf(x.getValue().toString()).ordinal());
            } else if ("payType".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " = '" + x.getValue() + "'");
            } else if ("payNo".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " like '%" + x.getValue() + "%'");
            } else if ("payRefundNo".equals(x.getKey())) {
                condition.append(" and ");
                condition.append(x.getKey() + " like '%" + x.getValue() + "%'");
            } else if ("orderStatus".equals(x.getKey())) {
                condition.append(" and ");
                if ("none".equals(x.getValue())) {
                    //待报价
                    condition.append(x.getKey() + " is null and orderPayType ='manual' and (orderAmount is null or orderAmount <0)");
                } else if ("init".equals(x.getValue())) {
                    //待付款
                    condition.append("orderPayType ='manual' and orderAmount > 0 and (orderStatus is null or orderStatus='init'");
                } else {
                    condition.append(x.getKey() + " = '" + x.getValue() + "'");
                }
            }
        });
        return condition.toString();
    }

    /**
     * 体验家问卷调研家通道状态同步*
     *
     * @param operationDto
     */
    public void channelOperation(SurveyChannelOperationDto operationDto) {
        if (operationDto == null || operationDto.getSurveyId() == null || operationDto.getSurveyId() == 0l) return;
        switch (operationDto.getType()) {
            case CHANNEL_CREATE:
                createSampleOrder(operationDto);
                break;
            case CHANNEL_PAUSE:
                deliveryService.pauseDeliveryTask(deliveryService.getDeliveryTask(operationDto.getSurveyId().toString(), null));
                break;
            case CHANNEL_CLOSE:
                deliveryService.stopDeliveryTask(deliveryService.getDeliveryTask(operationDto.getSurveyId().toString(), null), true);
                break;
            default:
        }
    }

    /**
     * *创建社区订单
     *
     * @param dto
     */
    public void createSampleOrder(SurveyChannelOperationDto dto) {
        if (dto == null || StringUtils.isEmpty(dto.getChannelConfigure())) return;

        SampleOrder sampleOrder = null;
        if (dto.getSurveyId() != null || dto.getChannelId() != null) {
            sampleOrder = repository.findFirstBySurveyIdAndChannelId(dto.getSurveyId(), dto.getChannelId());
        }
        if (sampleOrder == null) {
            sampleOrder = new SampleOrder();
        }
        SurveyPlusConfigureDto configureDto = JsonHelper.toObject(dto.getChannelConfigure(), SurveyPlusConfigureDto.class);
        log.info("channel create consumer, dto:{}", JsonHelper.toJson(dto));
        sampleOrder.setOrgId(dto.getOrgId());
        sampleOrder.setSurveyId(dto.getSurveyId());
        sampleOrder.setSurveyTitle(dto.getSurveyTitle());
        sampleOrder.setSurveyRealTitle(dto.getSurveyRealTitle());
        sampleOrder.setChannelId(dto.getChannelId());
        sampleOrder.setChannelName(dto.getChannelName());
        sampleOrder.setChannelStatus(dto.getChannelStatus());
        sampleOrder.setQuantity(configureDto == null ? 0 : configureDto.getQuantity());
        sampleOrder.setTotalPrice(configureDto == null ? null : configureDto.getTotalPrice());
        sampleOrder.setContacts(configureDto == null ? null : configureDto.getContacts());
        sampleOrder.setUnitPrice2(configureDto == null ? BigDecimal.ZERO : configureDto.getUnitPrice());
        sampleOrder.setCountQuestion(configureDto == null ? 0 : configureDto.getCountQuestion());
        sampleOrder.setGenderMatch(configureDto == null ? null : configureDto.getGenderMatch());
        sampleOrder.setAgeMatch(configureDto == null ? null : configureDto.getAgeMatch());
        sampleOrder.setEducationMatch(configureDto == null ? null : configureDto.getEducationMatch());
        sampleOrder.setLocationMatch(configureDto == null ? null : configureDto.getLocationMatch());
        sampleOrder.setOther(configureDto == null ? null : configureDto.getOther());
        sampleOrder.setDetail(configureDto == null ? null : configureDto.getDetail());
        repository.save(sampleOrder);
        //通知到企微群
        //sendMessageToWechatWorker(order);
    }

    /**
     * *投放任务
     *
     * @param order
     * @return
     */
    public AuditResponseDto sendDeliveryTask(SampleOrder order) {
        DeliveryTask task = new DeliveryTask();
        task.setType(SurveyType.XM_PLUS);
        task.setSid(order.getSurveyId().toString());
        task.setName(order.getSurveyRealTitle());
        task.setTitle(order.getSurveyTitle());
        task.setChannelId(order.getChannelId());
        task.setTaskType(TaskType.DELIVERY);
        task.setSurveyUrl(String.format(xmPlusUrl, task.getSid(), task.getChannelId()));
        deliveryService.create(deliveryService.mapToDto(task));
        order.setChannelStatus(ChannelStatus.UNSET);
        return new AuditResponseDto();
    }

    /**
     * 报价订单
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public AuditResponseDto quoteOrder(Long id, QuoteRequest quoteRequest) {
        SampleOrder order = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        if (order.getChannelId() != null && order.getChannelId() != 0L) {
            SurveyChannel surveyChannel = surveyChannelRepository.findById(order.getChannelId())
                    .orElseThrow(() -> new BadRequestException("用户已删除渠道，无法报价"));
            long sampleAmount = quoteRequest.getAmount().multiply(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(order.getQuantity() == null ? 0 : order.getQuantity())).longValue();
            long serviceAmount = (sampleAmount * 6) / 100;
            int total = (int) (sampleAmount + serviceAmount);
            String total2 = (total * 1.0 / 100) + "";
            Map<String, Object> config = JsonHelper.toMap(surveyChannel.getConfigure());
            if (config != null) {
                config.put("unitPrice", quoteRequest.getAmount());
                config.put("totalPrice", total2);
                surveyChannel.setConfigure(JsonHelper.toJson(config));
            }
            surveyChannel.setOrderAmount(total);
            surveyChannelRepository.save(surveyChannel);
            adminxEventTrigger.addEventAndTrigger(
                    order.getOrgId(),
                    "adminxOrder:" + order.getId(),
                    EventType.ADMINX_CHANNEL_ORDER_QUOTED,
                    new EventAdminxChannelOrderQuoted(surveyChannel.getSid(), surveyChannel.getId()));
            order.setUnitPrice2(quoteRequest.getAmount());
            order.setTotalPrice(total2);
            repository.save(order);
        }
        return new AuditResponseDto();
    }

    /**
     * 驳回订单
     *
     * @param message 驳回信息
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public AuditResponseDto rejectOrder(Long id, String message) {
        SampleOrder order = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        if (order.getChannelId() != null && order.getChannelId() != 0L) {
            channelService.updateChannelReject(order.getChannelId(), message, order);
            adminxEventTrigger.addEventAndTrigger(
                    order.getOrgId(),
                    "adminxOrder:" + order.getId(),
                    EventType.ADMINX_CHANNEL_ORDER_REJECT,
                    new EventAdminxChannelOrderReject(order.getSurveyId(), order.getChannelId(), message));
            order.setChannelStatus(ChannelStatus.REJECT);
            repository.save(order);
        }
        return new AuditResponseDto();
    }

    public void updateSampleComplete(Long surveyId, Long channelId, Integer recycle) {
        SampleOrder sampleOrder = repository.findFirstBySurveyIdAndChannelId(surveyId, channelId);
        if (sampleOrder != null) {
            sampleOrder.setChannelStatus(ChannelStatus.COMPLETE);
            updateRefundInfo(sampleOrder, recycle);
        }
    }

    private void updateRefundInfo(SampleOrder sampleOrder, Integer recycle) {
        if (sampleOrder != null) {
            sampleOrder.setRecycle(recycle);
            sampleOrder.setRefundPrice(ChannelRefundInfo.refundPrice(sampleOrder));
            save(sampleOrder);
        }
    }

    private boolean canRefund(Integer quantity, Integer recycle, OrganizationOrder order, SurveyChannel channel) {
        quantity = quantity == null ? 0 : quantity;
        recycle = recycle == null ? 0 : recycle;
        return (channel.getStatus() == ChannelStatus.COMPLETE || channel.getStatus() == ChannelStatus.REJECT)
                && (order.getStatus() == OrderStatus.success)
                && (quantity - recycle) > 0;
    }

    private boolean canReRefund(Integer quantity, Integer recycle, OrganizationOrder order, SurveyChannel channel) {
        quantity = quantity == null ? 0 : quantity;
        recycle = recycle == null ? 0 : recycle;
        return (channel.getStatus() == ChannelStatus.COMPLETE || channel.getStatus() == ChannelStatus.REJECT)
                && (order.getStatus() == OrderStatus.refund_failure)
                && (quantity - recycle) > 0;
    }

    /**
     * 退款
     * 1 已结束状态
     * 2 回收数量小于样本数量
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public AuditResponseDto refundOrder(Long id) {
        SampleOrder sampleOrder = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        if (sampleOrder.getChannelId() != null && sampleOrder.getChannelId() != 0L) {
            SurveyChannel surveyChannel = channelService.get(sampleOrder.getChannelId());
            if (surveyChannel != null) {
                int recycle = sampleOrder.getRecycle();
                DeliveryTask deliveryTask = deliveryService.getRepository().findFirstBySidAndChannelId(surveyChannel.getSid() + "", surveyChannel.getId());
                if (deliveryTask != null) {
                    if (deliveryTask.getAuditWaitCount() > 0) {
                        throw new BadRequestException("任务待审核数不为0，请先完成审核再退款");
                    } else if (!deliveryTask.getAuditCount().equals(sampleOrder.getRecycle())) {
                        recycle = deliveryTask.getAuditCount();
                    }
                }
                updateRefundInfo(sampleOrder, recycle);
                OrganizationOrder order = surveyChannel.getOrderId() != null ? organizationOrderRepository.findById(surveyChannel.getOrderId()).orElse(null) : null;
                if (order != null && canRefund(sampleOrder.getQuantity(), sampleOrder.getRecycle(), order, surveyChannel)) {
                    ChannelRefundInfo refundInfo = channelService.updateChannelRefundInfo(surveyChannel, sampleOrder, order);
                    adminxEventTrigger.addEventAndTrigger(
                            sampleOrder.getOrgId(),
                            "adminxOrder:" + sampleOrder.getId(),
                            EventType.ADMINX_CHANNEL_ORDER_REFUND,
                            new EventAdminxChannelOrderRefund(sampleOrder.getSurveyId(), sampleOrder.getChannelId(), refundInfo.getRefundAmount()));
                }
            }
        }
        return new AuditResponseDto();
    }

    /**
     * 重新退款
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public AuditResponseDto reRefund(Long id) {
        SampleOrder sampleOrder = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        if (sampleOrder.getChannelId() != null && sampleOrder.getChannelId() != 0L) {
            SurveyChannel surveyChannel = channelService.get(sampleOrder.getChannelId());
            if (surveyChannel != null) {
                OrganizationOrder order = surveyChannel.getOrderId() != null ? organizationOrderRepository.findById(surveyChannel.getOrderId()).orElse(null) : null;
                if (order != null && canReRefund(sampleOrder.getQuantity(), sampleOrder.getRecycle(), order, surveyChannel)) {
                    ChannelRefundInfo refundInfo = channelService.updateChannelRefundInfo(surveyChannel, sampleOrder, order);
                    adminxEventTrigger.addEventAndTrigger(
                            sampleOrder.getOrgId(),
                            "adminxOrder:" + sampleOrder.getId(),
                            EventType.ADMINX_CHANNEL_ORDER_RE_REFUND,
                            new EventAdminxChannelOrderRefund(sampleOrder.getSurveyId(), sampleOrder.getChannelId(), refundInfo.getRefundAmount()));
                }
            }
        }
        return new AuditResponseDto();
    }

    /**
     * *更新渠道状态
     *
     * @param status
     * @param surveyId
     * @param channelId
     */
    public void updateStatus(ChannelStatus status, Long surveyId, Long channelId) {
        repository.updateChannelStatusBySurveyIdAndChannelId(status, surveyId, channelId);
    }

    /**
     * 发送企微通知
     *
     * @param order
     */
    public void sendMessageToWechatWorker(SampleOrder order) {
        //设置请求头参数
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");

        RestTemplate restTemplate = new RestTemplate();
        ContentDto contentDto = new ContentDto(order.getContacts(), order.getSurveyId().toString(), order.getSurveyTitle(), order.getChannelId().toString(),
                order.getTotalPrice(), DateFormatter.getStringTime(order.getCreateTime()));
        HttpEntity httpEntity = new HttpEntity(JsonHelper.toJson(buildBody(contentDto)), headers);
        //发送通知到微信群
        ResponseEntity<String> response = restTemplate.exchange(sampleOrderNoticeUri, HttpMethod.POST, httpEntity, String.class);
        System.out.println("send notice to wechat work response：" + response.getBody());

    }

    public Map buildBody(ContentDto dto) {
        var content = String.format("# ️%s\n" +
                        "> 跟进人：%s\n" +
                        "> 问卷ID：%s\n" +
                        "> 问卷标题：%s\n" +
                        "> 渠道ID：%s\n" +
                        "> 订单总价：%s\n" +
                        "> 提交时间：%s\n" +
                        "> 备注：请在半个小时内处理！[点击查看](%s)",
                dto.getTitle(),
                dto.getContacts(),
                dto.getSurveyId(),
                dto.getSurveyTitle(),
                dto.getChannelId(),
                dto.getTotalPrice() != null && StringUtils.isNumeric(dto.getTotalPrice()) ? dto.getTotalPrice() + "元" : "待报价",
                dto.getCreateTime(),
                adminxLoginUri
        );
        var body = new HashMap<String, Object>();
        body.put("msgtype", "markdown");
        body.put("markdown", Map.of("content", content));
        return body;
    }
}
