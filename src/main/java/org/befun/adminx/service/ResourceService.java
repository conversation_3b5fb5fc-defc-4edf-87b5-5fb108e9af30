package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.Resource;
import org.befun.adminx.entity.ResourceDto;
import org.befun.adminx.repository.ResourceRepository;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResourceService extends BaseService<Resource, ResourceDto, ResourceRepository<BaseEntity, Number>> {
}
