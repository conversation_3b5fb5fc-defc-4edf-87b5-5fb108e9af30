package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.community.CommunityUserSessionDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.property.AdminProperty;
import org.befun.adminx.utils.CreateRandomStr;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.support.AbstractLobCreatingPreparedStatementCallback;
import org.springframework.jdbc.support.lob.DefaultLobHandler;
import org.springframework.jdbc.support.lob.LobCreator;
import org.springframework.jdbc.support.lob.LobHandler;
import org.springframework.stereotype.Service;

import javax.sql.rowset.serial.SerialBlob;

import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.SQLException;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommunityUserSessionService {

    @Autowired
    private AdminProperty adminProperty;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 校验token
     * @param token
     */
    public Boolean verifyToken(String token) {
        if(token.isEmpty() || token ==null) {
            return false;
        }
        String sql = String.format("select id,expire,data from community_user_sessions where id = '%s';" ,token);
        log.info("session sql: {}", sql);

        try {
            CommunityUserSessionDto sessionDto = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(CommunityUserSessionDto.class));
            long currentTime = System.currentTimeMillis();
//            log.info("currentTime : {}", currentTime);
//            log.info("overTime : {}", sessionDto.getExpire());
//            log.info("data : {}", new String(sessionDto.getData().getBytes(1, (int) sessionDto.getData().length()),StandardCharsets.UTF_8));
            if(currentTime > sessionDto.getExpire() * 1000) {
                return false;
            }else{
                return true;
            }
        }catch (Exception e){
            return false;
        }
    }

    /**
     * 获取登录token
     * @param communityUser
     * @return
     */
    public String getUserToken(CommunityUser communityUser) {
        if (communityUser.getId() == null) {
            throw new BadRequestException("用户不存在");
        }
        String token  = CreateRandomStr.createRandomStr(32);
        Long expire = System.currentTimeMillis()/1000 + 7*24*60*60;
        String userName = communityUser.getOpenId() == null ? communityUser.getUserName():communityUser.getOpenId();

        LobHandler lobHandler = new DefaultLobHandler();  // reusable object
        jdbcTemplate.execute(
                "INSERT INTO community_user_sessions " +" (`id`, `expire`, `data`) VALUES (?, ?, ?)",
                new AbstractLobCreatingPreparedStatementCallback(lobHandler) {
                    protected void setValues(PreparedStatement ps, LobCreator lobCreator) throws SQLException {
                        ps.setString(1, token);
                        ps.setLong(2, expire);
                        ps.setBlob(3, new SerialBlob(userName.getBytes(StandardCharsets.UTF_8)));
                    }
                }
        );
        return token;
    }
}
