package org.befun.adminx.service.template;


import org.befun.adminx.dto.survey.SimpleTemplateQuestionDto;
import org.befun.adminx.entity.survey.TemplateGroup;
import org.befun.adminx.entity.survey.TemplateGroupDto;
import org.befun.adminx.repository.template.TemplateGroupRepository;
import org.befun.adminx.repository.template.TemplateSurveyQuestionRepository;
import org.befun.core.service.BaseNoPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TemplateGroupService extends BaseNoPageService<TemplateGroup, TemplateGroupDto, TemplateGroupRepository> {

    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private TemplateSurveyQuestionRepository templateSurveyQuestionRepository;

    public TemplateGroup getEmptyGroup(Long orgId, TemplateGroup sourceGroup) {
        TemplateGroup defaultGroup = new TemplateGroup(0, "未分组",  sourceGroup.getType());
        defaultGroup.setOrgId(orgId);
        defaultGroup.setTitle(sourceGroup.getTitle());
        defaultGroup.setId(0L);
        TemplateGroup group = templateGroupRepository.save(defaultGroup);
        return group;
    }

    @Override
    protected void afterMapToDto(List<TemplateGroup> entity, List<TemplateGroupDto> dto) {
        super.afterMapToDto(entity, dto);
        dto.forEach(g -> {
            g.setQuestions(templateSurveyQuestionRepository.findAllByGroupId(g.getId()));
        });
    }

}
