package org.befun.adminx.service.template;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Service
@Slf4j
public class TemplateMapService {

    public TemplateSurveyQuestion mapToTemplate(TemplateSurveyQuestion source, TemplateSurveyQuestion target, Boolean visibleIf) {
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        copySurveyQuestion(source, target, visibleIf);

        List<TemplateSurveyQuestionItem> items = new ArrayList<>();
        source.getItems().forEach(surveyQuestionItem -> {
            TemplateSurveyQuestionItem i = new TemplateSurveyQuestionItem();
            copySurveyQuestionItem(surveyQuestionItem, i, visibleIf);
            i.setText(RegularExpressionUtils.replaceHtml(i.getText()));
            i.setQuestion(target);
            items.add(i);
        });
        List<TemplateSurveyQuestionColumn> columns = new ArrayList<>();
        source.getColumns().forEach(surveyQuestionColumn -> {
            TemplateSurveyQuestionColumn c = new TemplateSurveyQuestionColumn();
            copySurveyQuestionColumn(surveyQuestionColumn, c, visibleIf);
            c.setText(RegularExpressionUtils.replaceHtml(c.getText()));
            c.setQuestion(target);
            columns.add(c);
        });

        target.setTitle(RegularExpressionUtils.replaceHtml(target.getTitle()));
        target.setItems(items);
        target.setColumns(columns);

        target.setUserId(userId);
        target.setOrgId(orgId);
        return target;
    }

    public SurveyQuestion mapToQuestion(TemplateSurveyQuestion source, Boolean visibleIf) {
        SurveyQuestion target = new SurveyQuestion();
        copySurveyQuestion(source, target, visibleIf);
        Optional.ofNullable(source.getItems()).ifPresent(l -> {
            List<SurveyQuestionItem> ls = new ArrayList<>();
            l.forEach(s -> {
                SurveyQuestionItem t = new SurveyQuestionItem();
                copySurveyQuestionItem(s, t, visibleIf);
                t.setQuestion(target);
                ls.add(t);
            });
            target.setItems(ls);
        });
        Optional.ofNullable(source.getColumns()).ifPresent(l -> {
            List<SurveyQuestionColumn> ls = new ArrayList<>();
            l.forEach(s -> {
                SurveyQuestionColumn t = new SurveyQuestionColumn();
                copySurveyQuestionColumn(s, t, visibleIf);
                t.setQuestion(target);
                ls.add(t);
            });
            target.setColumns(ls);
        });
        return target;
    }

    private String parseRemark(String remark) {
        Map<String, Object> map = null;
        try {
            map = JsonHelper.toMap(remark);
            map.put("type", "text");
            //link不保存模板
            map.put("link", null);
            return JsonHelper.toJson(map);
        } catch (Exception e) {
            throw new BadRequestException("结束语解析错误：" + remark);
        }
    }

    private void copySurveyQuestion(BaseQuestion source, BaseQuestion target, Boolean visibleIf) {
        copyProperty(source::getGroupCode, target::setGroupCode);
        copyProperty(source::getType, target::setType);
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getTitle, target::setTitle);
        copyProperty(source::getName, target::setName);
        copyProperty(source::getCode, target::setCode);
        copyProperty(source::getInputType, target::setInputType);
        copyProperty(source::getAreaType, target::setAreaType);
        copyProperty(source::getMin, target::setMin);
        copyProperty(source::getMax, target::setMax);
        copyProperty(source::getLabels, target::setLabels);
        copyProperty(source::getMinLength, target::setMinLength);
        copyProperty(source::getMaxLength, target::setMaxLength);
        copyProperty(source::getStepLength, target::setStepLength);
        copyProperty(source::getDecimalPlaces, target::setDecimalPlaces);
        copyProperty(source::getIsRequired, target::setIsRequired);
        copyProperty(source::getIsNps, target::setIsNps);
        copyProperty(source::getShowLabel, target::setShowLabel);
        copyProperty(source::getInapplicable, target::setInapplicable);
        copyProperty(source::getInapplicableLabel, target::setInapplicableLabel);
        copyProperty(source::getEnableValidator, target::setEnableValidator);
        copyProperty(source::getItemsOrder, target::setItemsOrder);
        copyProperty(source::getRandomLimit, target::setRandomLimit);
        copyProperty(source::getHasOther, target::setHasOther);
        copyProperty(source::getExcludeOther, target::setExcludeOther);
        copyProperty(source::getExcludeOtherLabel, target::setExcludeOtherLabel);
        copyProperty(source::getOtherLabel, target::setOtherLabel);
        copyProperty(source::getShowType, target::setShowType);
        copyProperty(source::getRemark, target::setRemark);
        copyProperty(source::getStartTime, target::setStartTime);
        copyProperty(source::getEndTime, target::setEndTime);
        copyProperty(source::getAdaptedMobile, target::setAdaptedMobile);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getConfigure, target::setConfigure);
        copyProperty(source::getItemLayout, target::setItemLayout);
        copyProperty(source::getLabelLayout, target::setLabelLayout);
        copyProperty(source::getPlaceHolder, target::setPlaceHolder);
        copyProperty(source::getIsModify, target::setIsModify);
        copyProperty(source::getAlias, target::setAlias);
        copyProperty(source::getIsScore, target::setIsScore);
        copyProperty(source::getIsVerifyMobile, target::setIsVerifyMobile);
        copyProperty(source::getIsLinkCustomer, target::setIsLinkCustomer);
        copyProperty(source::getIsDeduplication, target::setIsDeduplication);
    }

    private void copySurveyQuestionItem(BaseQuestionItem source, BaseQuestionItem target, Boolean visibleIf) {
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getValue, target::setValue);
        copyProperty(source::getText, target::setText);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getExcludeOther, target::setExcludeOther);
        copyProperty(source::getConfigure, target::setConfigure);
        copyProperty(source::getIsFix, target::setIsFix);
        copyProperty(source::getScore, target::setScore);
    }

//    private void copySurveyQuestionItem(TemplateSurveyQuestionItem source, SurveyQuestionItem target) {
//        copyProperty(source::getSequence, target::setSequence);
//        copyProperty(source::getValue, target::setValue);
//        copyProperty(source::getText, target::setText);
//        copyProperty(source::getVisibleIf, target::setVisibleIf);
//        copyProperty(source::getExcludeOther, target::setExcludeOther);
//    }

    private void copySurveyQuestionColumn(BaseQuestionColumn source, BaseQuestionColumn target, Boolean visibleIf) {
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getValue, target::setValue);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getText, target::setText);
    }

//    private void copySurveyQuestionColumn(TemplateSurveyQuestionColumn source, SurveyQuestionColumn target) {
//        copyProperty(source::getSequence, target::setSequence);
//        copyProperty(source::getValue, target::setValue);
//        copyProperty(source::getText, target::setText);
//        copyProperty(source::getVisibleIf, target::setVisibleIf);
//    }

//    private void copySurveyLogic(TemplateSurveyLogic source, SurveyLogic target) {
//        copyProperty(source::getQuestionNames, target::setQuestionNames);
//        copyProperty(source::getExpression, target::setExpression);
//        copyProperty(source::getTarget, target::setTarget);
//        copyProperty(source::getType, target::setType);
//    }

    private <T> void copyProperty(Supplier<T> get, Consumer<T> set) {
        set.accept(get.get());
    }
}
