package org.befun.adminx.service.template;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.repository.SurveyQuestionRepository;
import org.befun.adminx.repository.template.TemplateSurveyQuestionRepository;
import org.befun.adminx.service.QuestionService;
import org.befun.adminx.service.SurveyService;
import org.befun.adminx.service.UserService;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateSurveyQuestionService extends BaseService<TemplateSurveyQuestion, TemplateSurveyQuestionDto, TemplateSurveyQuestionRepository> {

    @Autowired
    SurveyQuestionRepository surveyQuestionRepository;

    @Autowired
    SurveyService surveyService;

    @Autowired
    QuestionService questionService;

    @Autowired
    TemplateSurveyQuestionRepository templateSurveyQuestionRepository;

    @Autowired
    UserService userService;


    public TemplateSurveyQuestion requireTemplateQuestion(Long questionId) {
        Optional<TemplateSurveyQuestion> questionOptional = templateSurveyQuestionRepository.findById(questionId);
        if (questionOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return questionOptional.get();
    }

}
