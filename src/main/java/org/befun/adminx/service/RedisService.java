package org.befun.adminx.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/10 16:40
 */
@Service
public class RedisService {

    private static final String COMMUNITY_USER_COUNT = "community:user:count";
    private static final String COMMUNITY_USER_SCORE = "community:user:score";
    private static final String COMMUNITY_VERIFY_CODE_KEY = "community:verify_code:%s:%s";
    private static final String COMMUNITY_USER_SURVEY_COMPLETE_COUNT = "community:user:survey:complete:count";

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    public void setCountUsers(Long count) {
        stringRedisTemplate.opsForValue().set(COMMUNITY_USER_COUNT, count.toString());
    }

    public Long getCountUsers() {
        String count = stringRedisTemplate.opsForValue().get(COMMUNITY_USER_COUNT);
        return StringUtils.isEmpty(count) ? 0l : Long.parseLong(count);
    }

    public void setCountScores(Long score) {
        stringRedisTemplate.opsForValue().set(COMMUNITY_USER_SCORE, score.toString());
    }

    public Long getCountScores() {
        String count = stringRedisTemplate.opsForValue().get(COMMUNITY_USER_SCORE);
        return StringUtils.isEmpty(count) ? 0l : Long.parseLong(count);
    }

    public void setCountSurveyComplete(Long cuid, long count) {
        stringRedisTemplate.opsForHash().put(COMMUNITY_USER_SURVEY_COMPLETE_COUNT, cuid, count);
    }

    public void addCountSurveyComplete(Long cuid) {
        Long count = getCountSurveyComplete(cuid);
        stringRedisTemplate.opsForHash().put(COMMUNITY_USER_SURVEY_COMPLETE_COUNT, cuid, count == null ? 1l : count +1 );
    }

    public Long getCountSurveyComplete(Long cuid) {
        Object count = stringRedisTemplate.opsForHash().get(COMMUNITY_USER_SURVEY_COMPLETE_COUNT, cuid);
        return count == null || StringUtils.isEmpty((String) count) ? null : Long.parseLong((String) count);
    }

    public String getVerifyCodeRedisKey(String source, String mobile) {
        return String.format(COMMUNITY_VERIFY_CODE_KEY,source,mobile);
    }

    public boolean verifyCodeFromRedis(String key, String code) {
        Object serverCode = stringRedisTemplate.opsForValue().get(key);
        if(serverCode == null)
            throw new BadRequestException("验证码已过期");

        boolean r = serverCode != null && serverCode.toString().equals(code);
        if (r) {
            return true;
        }
        return false;
    }

    public void setVerifyCode(String key, String code) {
        if(StringUtils.isEmpty(key) || StringUtils.isEmpty(code)) return;
        stringRedisTemplate.opsForValue().set(key, code, 300, TimeUnit.SECONDS);
    }

    public void clearVerifyCode(String key) {
        stringRedisTemplate.delete(key);
    }

}
