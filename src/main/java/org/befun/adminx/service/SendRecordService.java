package org.befun.adminx.service;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.*;
import org.befun.adminx.dto.ext.ResendDto;
import org.befun.adminx.dto.ext.SendFollowTaskDto;
import org.befun.adminx.dto.ext.SendTaskExDto;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.adminx.dto.sample.SamplesInfoDto;
import org.befun.adminx.dto.task.SendSelectedDto;
import org.befun.adminx.dto.task.SendSurveyByWechatTaskDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.dto.Additional;
import org.befun.adminx.repository.*;
import org.befun.adminx.task.SendSurveyByPhoneMsgTaskExecutor;
import org.befun.adminx.task.SendSurveyByWeChatTaskExecutor;
import org.befun.adminx.task.SendWeChatMsgTaskExecutor;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 10:00
 */
@Service
@Slf4j
public class SendRecordService extends BaseService<SendRecord, SendRecordDto, SendRecordRepository> {

    @Autowired
    private SendRecordRepository recordRepository;

    @Autowired
    private CommunityUserService communityUserService;

    @Autowired
    CommunityUserRepository communityUserRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private SendSurveyByWeChatTaskExecutor sendSurveyByWeChatTaskExecutor;

    @Autowired
    private SendSurveyByPhoneMsgTaskExecutor sendSurveyByPhoneMsgTaskExecutor;

    @Autowired
    private SendWeChatMsgTaskExecutor sendWeChatMsgTaskExecutor;

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;

    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;

    @Autowired
    private SurveySendRecordService surveySendRecordService;

    @Autowired
    private ResponseService responseService;

    @Value("${community.uri.user-scores-home-page}")
    private String userScoresHomePage;

    public void sendTask(SendTaskExDto task) {
        //过滤中英文逗号 "," "，"
        if(!StringUtils.isEmpty(task.getBlackList())) {
            String cuidContent = task.getBlackList().replaceAll("\n",",").replaceAll("，",",");
            task.setBlackList(cuidContent);
        }
        if (!StringUtils.isEmpty(task.getWhiteList())) {
            String whiteList = task.getWhiteList().replaceAll("\n", ",").replaceAll("，", ",");
            task.setWhiteList(whiteList);
        }

        //更新latest
        updateLatestTaskSend(task.getId());
        SendRecord record = SendRecord.builder()
                .taskId(task.getId())
                .sid(task.getSid())
                .enablePush(task.getEnablePush())
                .rules(JsonHelper.toJson(task.getSamples()))
                .latest(true)
                .blackList(task.getBlackList())
                .whiteList(task.getWhiteList())
                .sendType(task.getSendType())
                .build();
        recordRepository.save(record);
        //开启模板推送
        if(task.getEnablePush()) {
            //匹配用户
            List<CommunityUserSearchDto> users = communityUserService.searchSendTaskUser(task);
            if(users != null && users.size() > 0) {
                task.setTaskType(TaskType.DELIVERY);
                SendFollowTaskDto sendFollowTaskDto = new SendFollowTaskDto();
                TemplateInfoDto template = sendSurveyByWeChatTaskExecutor.getTemplateInfoDto(SendType.SEND_SURVEY);
                SendSelectedDto sendSelectedDto = sendFollowTaskDto.mapToSendSearch(task, users, template);
                sendSurveyByWeChatTaskExecutor.performCoordinator(sendSelectedDto);
            }
        }
    }

    /**
     * * 发送追放任务
     * @param sendSelectedDto
     */
    public void sendFollowTask(SendSelectedDto sendSelectedDto) {
        if (sendSelectedDto == null || sendSelectedDto.getUserList() == null) return;
        //更新latest
        updateLatestTaskSend(sendSelectedDto.getTask().getId());
        SendRecord record = SendRecord.builder()
                .taskId(sendSelectedDto.getTask().getId())
                .sid(sendSelectedDto.getTask().getSid())
                .enablePush(false)
                .rules("[]")
                .latest(true)
                .sendType(SendTaskType.DESIGN)
                .whiteList(sendSelectedDto.getUserList().stream().map(user -> String.valueOf(user.getCuid())).collect(Collectors.joining(",")))
                .build();
        recordRepository.save(record);
    }

    /**
     * 推送任务*
     * @param deliveryTaskDto
     * @param sendFollowTaskDto
     */
    public Integer sendFollowTask(DeliveryTaskDto deliveryTaskDto, SendFollowTaskDto sendFollowTaskDto) {
        if(sendFollowTaskDto == null || sendFollowTaskDto.getCuidList() == null || sendFollowTaskDto.getCuidList().size() == 0 )
            return 0;
        List<CommunityUserSearchDto> users = communityUserService.searchSendFollowTaskUser(sendFollowTaskDto);
        if(users == null || users.size() == 0) throw new BadRequestException("追访人数不能为空");

        if(StringUtils.isNotEmpty(sendFollowTaskDto.getSurveyId())) {
            users = responseService.getResponseIds(sendFollowTaskDto.getSurveyId(), users);
        }
        SendTaskExDto sendTaskExDto = new SendTaskExDto();
        BeanUtils.copyProperties(deliveryTaskDto, sendTaskExDto);

        TemplateInfoDto template;
        SendSelectedDto sendSelectedDto;
        switch (sendFollowTaskDto.getTaskSendMethod()) {
            case WECHAT_SERVICE:
                template = thirdPartyTemplateService.getWeChatTemplate(sendFollowTaskDto.getThirdPartTemplateId());
                sendSelectedDto = sendFollowTaskDto.mapToSendSelect(sendTaskExDto, users, template, sendFollowTaskDto.getSurveyId());
                //写入sendRecord
                sendFollowTask(sendSelectedDto);
                sendSurveyByWeChatTaskExecutor.performCoordinator(sendSelectedDto);
                break;
            case PHONE_MSG:
                template = thirdPartyTemplateService.getSmsTemplate(sendFollowTaskDto.getThirdPartTemplateId());
                sendSelectedDto = sendFollowTaskDto.mapToSendSelect(sendTaskExDto, users, template, sendFollowTaskDto.getSurveyId());
                //写入sendRecord
                sendFollowTask(sendSelectedDto);
                sendSurveyByPhoneMsgTaskExecutor.performCoordinator(sendSelectedDto);
                break;
            case WECHAT_MSG:
                if (users.size() < 2 || users.size() > 10000)
                    throw new BadRequestException("图文消息推送人数，不能小于2个或者大于10000个");
                template = thirdPartyTemplateService.getWeChatTemplate(sendFollowTaskDto.getThirdPartTemplateId());
                sendSelectedDto = sendFollowTaskDto.mapToSendSelect(sendTaskExDto, users, template, sendFollowTaskDto.getSurveyId());
                //写入sendRecord
                sendFollowTask(sendSelectedDto);
                sendWeChatMsgTaskExecutor.performCoordinator(sendSelectedDto);
                break;
            default:
                throw new BadRequestException("发送类型错误");
        }
        return users.size();
    }

    /**
     * 重发
     * @param task
     * @param resendDto
     */
    public void resend(DeliveryTask task, ResendDto resendDto) {
        if(task == null) return;
        List<Long> ids = surveySendRecordService.getLogIdsByCondition(task.getId(), resendDto);

        resendDto.setLogIds(ids);
        if(resendDto.getLogIds() == null || resendDto.getLogIds().size() == 0) return;

        List<SurveySendRecord> records = surveySendRecordRepository.findByIdInAndTaskId(resendDto.getLogIds(), task.getId());
        if(records == null || records.size() == 0) return;

        TemplateInfoDto template;
        switch(resendDto.getTaskSendMethod()) {
            case WECHAT_SERVICE:
                template = thirdPartyTemplateService.getWeChatTemplate(resendDto.getThirdPartTemplateId());
                sendSurveyByWeChatTaskExecutor.resend(task.getId(), records, template, resendDto.getParams());
                break;
            case PHONE_MSG:
                template = thirdPartyTemplateService.getSmsTemplate(resendDto.getThirdPartTemplateId());
                sendSurveyByPhoneMsgTaskExecutor.resend(task.getId(), records, template, resendDto.getParams());
                break;
            case WECHAT_MSG:
                List<Long> cuidList = records.stream().map(SurveySendRecord::getCuid).collect(Collectors.toList());
                if (cuidList.size() < 2 || cuidList.size() > 10000)
                    throw new BadRequestException("图文消息推送人数，不能小于2个或者大于10000个");
                template = thirdPartyTemplateService.getWeChatTemplate(resendDto.getThirdPartTemplateId());
                List<CommunityUserSearchDto> users = communityUserService.searchSendFollowTaskUser(cuidList);
                SendSelectedDto sendSelectedDto = SendSelectedDto.builder().userList(users).build();
                //设置模板
                sendSelectedDto.setTemplate(template);
                sendWeChatMsgTaskExecutor.resend(records, sendSelectedDto);
                break;
            default:
                throw new BadRequestException("发送类型错误");

        }
    }

    @Async
    public void sendAuditPassNotice(DeliveryTaskDto taskDto, CommunityUser user) {

        //如果用户绑定了手机号 发放短信通知
        SurveySendRecord surveySendRecord = new SurveySendRecord();
        //发放微信通知
        if(user.getWechatSubscribe() == WechatSubscribeStatus.SUBSCRIBE) {
            TemplateInfoDto template = sendSurveyByWeChatTaskExecutor.getTemplateInfoDto(SendType.SEND_SCORE_NOTICE);
            Map<String, Object> params = new HashMap<>();
            params.put("keyword1",taskDto.getName());
            params.put("keyword2",DateFormatter.getNowStringDate());
            params.put("keyword3","审核通过");
            params.put("keyword4","感谢您的填答，您的酬金奖励已到账");
            surveySendRecord.setAccount(user.getOpenId());
            surveySendRecord.setContent(sendSurveyByWeChatTaskExecutor.buildContent(template, params, userScoresHomePage));
            surveySendRecord.setSendUrl(userScoresHomePage);
            sendSurveyByWeChatTaskExecutor.sendWeChat((new SendSurveyByWechatTaskDto()).create(template),surveySendRecord);
        }
        //追访任务 短信通知
        if(StringUtils.isNotEmpty(user.getMobile()) && taskDto.getTaskType() == TaskType.FOLLOW) {
            TemplateInfoDto template = thirdPartyTemplateService.getSmsTemplateByType(SendType.SEND_SCORE_NOTICE);
            Map<String, Object> params = new HashMap<>();
            params.put("projectName",taskDto.getName());
            surveySendRecord.setAccount(user.getMobile());
            surveySendRecord.setContent(sendSurveyByPhoneMsgTaskExecutor.buildContent(template, params, userScoresHomePage));
            sendSurveyByPhoneMsgTaskExecutor.sendSms(null, surveySendRecord);
        }
    }

    /**
     * 更新推送规则 以最新一条sendRecord为准
     * @param taskId
     */
    public void updateLatestTaskSend(Long taskId) {
        recordRepository.updateLatestTaskSend(taskId);
    }

    /**
     * 过滤不符合指定样本投放规则的任务
     * @param dtoList
     * @param user
     * @return
     */
    public List<DeliveryTaskDto> matchSendTaskRule(List<DeliveryTaskDto> dtoList, Additional user) {
        if(dtoList == null || dtoList.size() == 0) return dtoList;
        //如果用户没填写个人资料 不展示问卷
        if(user == null) return dtoList;
        List<SendRecord> records = recordRepository.findByTaskIdInAndLatestIsTrue(dtoList.stream().map(i -> i.getId()).collect(Collectors.toList()));
        if(records == null || records.size() == 0) return dtoList;
        List<Long> taskIds = new ArrayList<>();
        records.forEach(record -> {
            List<String> blackList = new ArrayList<>();
            if(!StringUtils.isEmpty(record.getBlackList())) {
                blackList = List.of(record.getBlackList().split(","));
            }

            Boolean addToTask = true;
            //如果是RDS投放方式 且在白名单内
            if (record.getSendType().equals(SendTaskType.RDS) && !StringUtils.isEmpty(record.getWhiteList())) {
                addToTask = List.of(record.getWhiteList().split(",")).contains(user.getCuid().toString());
            }

            //符合规则 且不在黑名单
            if (authRule(record.getRules(), user)) {
                if (!blackList.contains(user.getCuid().toString()) && addToTask) {
                    taskIds.add(record.getTaskId());
                }
           }
        });
        //只返回符合投放规则的样本
        return dtoList.stream().filter(i -> taskIds.contains(i.getId())).collect(Collectors.toList());
    }

    /**
     * 校验用户是否符合推送任务的条件
     * @param rules
     * @param user
     * @return
     */
    @SneakyThrows
    public Boolean authRule(String rules, Additional user) {
        if (StringUtils.isEmpty(rules) || "[]".equals(rules)) return true;

        Boolean pass = true;
        List<SamplesDto> samples = JsonHelper.toList(rules,SamplesDto.class);
        if(samples.size() > 0 && user != null) {
            Integer age = DateFormatter.getAge(user.getBirthday());
            Integer gender = user.getGender();
            Integer education = user.getEducation();
            String province = user.getProvince();
            String city = user.getCity();
            for (int i = 0; i < samples.size(); i++) {
                AtomicReference<Boolean> boolLocation = new AtomicReference<>(true);
                Boolean boolAgeMin = true;
                Boolean boolAgeMax = true;
                Boolean boolGender = true;
                Boolean boolEducation = true;
                if(samples.get(i).getAgeMin() != null) boolAgeMin = age != null && age >= samples.get(i).getAgeMin();
                if(samples.get(i).getAgeMax() != null) boolAgeMax = age != null && age <= samples.get(i).getAgeMax();
                if(samples.get(i).getGender() != null) boolGender = gender == samples.get(i).getGender();
                if(samples.get(i).getEducations() != null && samples.get(i).getEducations().size() > 0)
                    boolEducation = samples.get(i).getEducations().contains(education);
//                if(samples.get(i).getLocations() != null && samples.get(i).getLocations().size() >0) {
//                    samples.get(i).getLocations().stream().forEach(location ->{
//                        boolLocation.set(false);
//                        if(location.size() == 1) {
//                            if(StringUtils.isNotEmpty(province) && province.equals(location.get(0))) boolLocation.set(true);
//                        }else if(location.size() == 2) {
//                            if(StringUtils.isNotEmpty(city) && city.equals(location.get(1))) boolLocation.set(true);
//                        }
//                    });
//                }
                if (CollectionUtils.isNotEmpty(samples.get(i).getLocations())) {
                    boolLocation.set(false);
                    for (List<String> location : samples.get(i).getLocations()) {
                        if(location.size() == 1) {
                            if(StringUtils.isNotEmpty(province) && province.equals(location.get(0))){
                                boolLocation.set(true);
                                break;
                            }
                        } else if(location.size() == 2) {
                            if(StringUtils.isNotEmpty(city) && city.equals(location.get(1))) {
                                boolLocation.set(true);
                                break;
                            }
                        }
                    }
                }
                pass = boolAgeMin && boolAgeMax && boolGender && boolEducation && boolLocation.get();
                if(pass){
                    return true;
                }
            }
        }
        return pass;
    }

    public SendRecord getSendRecordByTaskId(Long taskId) {
        Optional<SendRecord> sendRecord = recordRepository.findFirstByTaskIdAndLatestIsTrueOrderByIdDesc(taskId);
        return sendRecord.orElse(null);
    }

    /**
     * *校验用户是否在黑名单
     * @param record
     * @param cuid
     * @return
     */
    public Boolean authBlackList(SendRecord record, Long cuid) {
        if(record != null && StringUtils.isNotEmpty(record.getBlackList())) {
            List<String> blackList = List.of(record.getBlackList().split(","));
            if(blackList.contains(cuid.toString())) {
                return false;
            }
        }
        return true;
    }

    /**
     * *校验用户是否在白名单
     * @param record
     * @param cuid
     * @return
     */
    public Boolean authWhiteList(SendRecord record, Long cuid) {
        if(record != null && StringUtils.isNotEmpty(record.getWhiteList())) {
            List<String> whiteList = List.of(record.getWhiteList().split(","));
            if(whiteList.contains(cuid.toString())) {
                return true;
            }
        }
        return false;
    }

    /**
     * * 判断用户是否满足条件
     * @param task
     * @param user
     * @return
     */
    public Boolean checkConditionsByRule(DeliveryTask task, CommunityUser user) {
        SendRecord record = getSendRecordByTaskId(task.getId());
        if (record == null) return true;
        if (task.getTaskType() == TaskType.DELIVERY) {
            if (!authBlackList(record, user.getId())) return false;
            if (!authWhiteList(record, user.getId())) return false;
            if (!authRule(record.getRules(), communityUserService.getUserAdditional(user.getId()))) return false;
        } else if (task.getTaskType() == TaskType.FOLLOW) {
            //是否在追访任务选中的CUID名单之列
            if (!authWhiteList(record, user.getId())) return false;
        }
        return true;
    }

    public List<SendRecord> templateSendRecord(Long taskId) {
        List<SendRecord> sendRecords = repository.findByTaskIdAndTaskSendTypeOrderByCreateTimeDesc(taskId, TaskSendType.TEMPLATE_SEND);

        for (SendRecord sendRecord : sendRecords) {
            Optional<Admin> admin = adminRepository.findById(sendRecord.getOperator());
            if(admin.isPresent()){
                sendRecord.setOperatorName(admin.get().getUserName());
            }
            ThirdPartyTemplate template = thirdPartyTemplateRepository.findFirstByOpenId(sendRecord.getTemplateId());
            if(template != null){
                sendRecord.setTemplateName(template.getName());
            }
            parseRules(sendRecord);
        }
        return sendRecords;
    }

    private void parseRules(SendRecord sendRecord) {
        String rules = sendRecord.getRules();
        if(StringUtils.isEmpty(rules)) {
            return;
        }
        List<SamplesInfoDto> samplesInfoDtos = JsonHelper.toList(sendRecord.getRules(), SamplesInfoDto.class);
        samplesInfoDtos.forEach(samplesInfoDto ->{
            List<Integer> educations = samplesInfoDto.getEducations();
            StringJoiner joiner = new StringJoiner(", ");
            for (Integer education : educations) {
                String text = EducationType.values()[education].getText();
                joiner.add(text);
            }
            samplesInfoDto.setEducationLabels(joiner.toString());
            Integer gender = samplesInfoDto.getGender();
            if(gender != null && gender == 1){
                samplesInfoDto.setGenderLabel("男");
            }else if(gender != null && gender == 2){
                samplesInfoDto.setGenderLabel("女");
            }else {
                samplesInfoDto.setGenderLabel("不限");
            }
        });
        sendRecord.setRules(JsonHelper.toJson(samplesInfoDtos));
    }

}























