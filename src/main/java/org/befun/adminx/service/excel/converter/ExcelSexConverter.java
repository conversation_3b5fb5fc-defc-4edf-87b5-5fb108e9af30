package org.befun.adminx.service.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

public class ExcelSexConverter implements Converter<String> {

	public Class<?> supportJavaTypeKey() {
		return String.class;
	}

	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

		return cellData.getData().toString();
	}

	public String convertToJavaData(ReadConverterContext<?> context) throws Exception {
		return this.convertToJavaData(context.getReadCellData(), context.getContentProperty(), context.getAnalysisContext().currentReadHolder().globalConfiguration());
	}

	public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (StringUtils.isEmpty(value)) {
			return new WriteCellData<>("");
		}
		org.befun.adminx.converter.SexConverter sexConverter = new org.befun.adminx.converter.SexConverter();
		return new WriteCellData<>(sexConverter.convertToString(value));
	}

	public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
		return this.convertToExcelData(context.getValue(), context.getContentProperty(), context.getWriteContext().currentWriteHolder().globalConfiguration());
	}

}
