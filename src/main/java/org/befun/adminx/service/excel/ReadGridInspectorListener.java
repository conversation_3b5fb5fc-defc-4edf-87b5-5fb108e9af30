package org.befun.adminx.service.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.grid.ReadCommunityGridDto;
import org.befun.adminx.entity.CommunityGrid;
import org.befun.adminx.repository.CommunityGridRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ReadGridInspectorListener implements ReadListener<ReadCommunityGridDto> {

	private static int BATCH_COUNT = 50;
	private List<CommunityGrid> cacheDataList = new ArrayList<>();
	private static int recordCount = 0;

	private CommunityGridRepository communityGridRepository;

	public ReadGridInspectorListener(CommunityGridRepository gridInspectorRepository) {
		this.communityGridRepository = gridInspectorRepository;
	}

	@Override
	public void invoke(ReadCommunityGridDto data, AnalysisContext context) {
		if (StringUtils.isEmpty(data.getGridId())) {
			return;
		}
		CommunityGrid gridInspector = communityGridRepository.findFirstByGridId(data.getGridId());
		if (gridInspector == null) {
			cacheDataList.add(copyProperties(data));
			recordCount++;
		}
		if (cacheDataList.size() >= BATCH_COUNT) {
			communityGridRepository.saveAll(cacheDataList);
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		communityGridRepository.saveAll(cacheDataList);
		log.info("网格员数据导入成功！,共{}条记录", recordCount);
		recordCount = 0;
	}

	public CommunityGrid copyProperties(ReadCommunityGridDto gridDto) {
		CommunityGrid grid = new CommunityGrid();
		if (gridDto == null) {
			return grid;
		}
		grid.setGridId(gridDto.getGridId());
		grid.setCuid(gridDto.getCuid());
		grid.setGridName(gridDto.getGridName());
		grid.setGridInspectorName(gridDto.getGridInspectorName());
		grid.setContract(gridDto.getContract());
		return grid;
	}
}
