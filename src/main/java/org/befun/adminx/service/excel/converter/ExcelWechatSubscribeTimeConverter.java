package org.befun.adminx.service.excel.converter;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;

import java.time.format.DateTimeFormatter;
import java.util.Date;

public class ExcelWechatSubscribeTimeConverter implements Converter<String> {

	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	public Class<?> supportJavaTypeKey() {
		return String.class;
	}

	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

		return cellData.getData().toString();
	}

	public String convertToJavaData(ReadConverterContext<?> context) throws Exception {
		return this.convertToJavaData(context.getReadCellData(), context.getContentProperty(), context.getAnalysisContext().currentReadHolder().globalConfiguration());
	}

	public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (StringUtils.isEmpty(value)) {
			return new WriteCellData<>("");
		}
		Date date = new Date(Long.parseLong(value)*1000);
		String cellDate = DateHelper.formatDate(date);
		return new WriteCellData<>(cellDate);
	}

	public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
		return this.convertToExcelData(context.getValue(), context.getContentProperty(), context.getWriteContext().currentWriteHolder().globalConfiguration());
	}
}
