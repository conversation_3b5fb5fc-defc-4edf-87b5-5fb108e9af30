package org.befun.adminx.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.befun.adminx.dto.user.SimpleUserInfoDto;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TBD (move to core and auth)
 *
 * <AUTHOR>
 */
@Configuration()
@Service
@ConfigurationProperties(prefix = "befun.adminx")
public class JwtService {
    private String secret = "befun";
    private String issuer = "hanyi";
    private String algorithmName = "HMAC256";
    private Algorithm algorithm = null;
    private JWTVerifier verifier = null;

    @PostConstruct
    private void setup() {
        this.algorithm = this.buildAlgorithm();
        this.verifier = JWT.require(this.algorithm)
                .withIssuer(this.issuer)
                .build();
    }

    /**
     * encode map to jwt token
     * @param claims
     * @return
     */
    public String encode(Map<String, Object> claims) {
        JWTCreator.Builder builder = JWT.create()
                .withIssuer(this.issuer)
                .withHeader(claims);
        claims.entrySet().forEach(entry -> {
            builder.withClaim(entry.getKey(), entry.getValue().toString());
        });
        return builder.sign(algorithm);
    }

    /**
     * decode jwt token
     * @param token
     * @return
     */
    public SimpleUserInfoDto decode(String token) {
        try {
            DecodedJWT jwt = verifier.verify(token);
            Long userId = Long.valueOf(jwt.getClaim("userId").asString());
            String username = jwt.getClaim("username").asString();
            String sroles = jwt.getClaim("roles").asString();
            List<String> roles = Arrays.stream(sroles.replace("[", "").replace("]", "").split(",")).collect(Collectors.toList());
            return SimpleUserInfoDto.builder()
                    .roles(roles)
                    .userId(userId)
                    .username(username)
                    .build();
        } catch (JWTDecodeException exception){
            //Invalid token
            return null;
        }
    }

    /**
     *
     * @return
     */
    private Algorithm buildAlgorithm() {
        switch (this.algorithmName) {
            case "HMAC256":
                return Algorithm.HMAC256(this.secret);
            default:
                throw new RuntimeException("invalid jwt algorithm");
        }
    }
}
