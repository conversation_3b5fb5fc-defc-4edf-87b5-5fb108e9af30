package org.befun.adminx.service;

import javassist.expr.NewArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.fluent.Request;
import org.befun.adminx.constant.QuotaChannelType;
import org.befun.adminx.constant.SurveyQuotaStatus;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.adminx.dto.SurveyProcessDto;
import org.befun.adminx.dto.task.TaskSyncQuotaDto;
import org.befun.adminx.entity.survey.AdminxQuota;
import org.befun.adminx.entity.survey.AdminxQuotaDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyQuota;
import org.befun.adminx.repository.AdminxQuotaRepository;
import org.befun.adminx.repository.SurveyQuotaRepository;
import org.befun.adminx.repository.SurveyRepository;
import org.befun.adminx.service.quota.bean.QuotaStat;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;



@Service
@Slf4j
public class AdminxQuotaService extends BaseService<AdminxQuota, AdminxQuotaDto, AdminxQuotaRepository> {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private AdminxQuotaRepository adminxQuotaRepository;

    @Autowired
    private SurveyQuotaRepository surveyQuotaRepository;

    @Autowired
    private QuotaService quotaService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${befun.task.queue.adminx-sync-quota-key:}")
    private String adminxSyncQuotaKey;

    @Override
    public Page<AdminxQuotaDto> findAll(ResourceEntityQueryDto<AdminxQuotaDto> queryDto) {
        queryDto.setSorts(Sort.by("createTime").descending());
        queryDto.getQueryCriteriaList().forEach(x -> {
            if ("surveyTitle".equals(x.getKey())) {
                x.setOperator(QueryOperator.LIKE);
            }
        });
        Page<AdminxQuotaDto> surveyQuotaDtos = super.findAll(queryDto);
        List<Long> surveyIds = surveyQuotaDtos.getContent().stream().map(AdminxQuotaDto::getSid).collect(Collectors.toList());
        List<Survey> surveys = surveyRepository.findAllById(surveyIds);
        Map<Long, Survey> surveyMap = surveys.stream().collect(Collectors.toMap(Survey::getId, Function.identity()));
        surveyQuotaDtos.getContent().forEach(x -> {
            Survey survey = surveyMap.get(x.getSid());
            if (survey != null) {
                SurveyStatus status = SurveyStatus.values()[survey.getStatus()];
                x.setSurveyStatus(status);
                x.setEnableQuota(survey.getEnableQuota());
                x.setCemQuotaStatus(survey.getQuotaStatus());
                x.setAdminxEnableQuota(survey.getEnableAdminxQuota());
                x.setAdminxQuotaStatus(survey.getAdminxQuotaStatus());
            } else {
                x.setSurveyStatus(null);
            }
        });
        return surveyQuotaDtos;
    }

    @Override
    public <S extends BaseEntityDTO<AdminxQuota>> AdminxQuotaDto create(S data) {
        AdminxQuotaDto dto = (AdminxQuotaDto) data;
        AdminxQuota quota = repository.findFirstBySid(dto.getSid());
        //新增时，问卷中存在调研家社区配额，不能新增
        if (quota != null) {
            throw new BadRequestException("问卷已存在配额设置");
        }
        Survey survey = surveyRepository.findById(dto.getSid()).orElse(null);
        if (survey != null) {
            //adminx创建配额默认开启
            survey.setEnableAdminxQuota(true);
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            surveyRepository.save(survey);
        }
        return super.create(data);
    }

    @Override
    public Boolean deleteOne(long id) {
        AdminxQuota adminxQuota = repository.findById(id).orElseThrow(() -> new BadRequestException("删除失败"));
        List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySidAndChannelType(adminxQuota.getSid(), QuotaChannelType.SURVEY_PLUS);
        surveyQuotaRepository.deleteAll(surveyQuotas);
        return super.deleteOne(id);
    }

    /**
     * 发布问卷
     * <p>
     * 如果开启了配额需要做配额计算
     *
     * @param quotaId
     * @return
     */
    public AdminxQuotaDto publish(Long quotaId) {
        AdminxQuota quota = adminxQuotaRepository.findById(quotaId).orElseThrow();
        Survey survey = surveyRepository.findById(quota.getSid()).orElseThrow(() -> new RuntimeException("问卷不存在"));
        //如果配额stat redis key不存在 需要初始化配额
        if (quota != null) {
            initQuotaStat(survey.getId());
        }
        if (survey.getAdminxQuotaStatus() == SurveyQuotaStatus.CALCULATING) {
            throw new BadRequestException("您的问卷正在计算社区配额，请计算完之后再启用");
        }
        if (survey.getQuotaStatus() == SurveyQuotaStatus.CALCULATING) {
            throw new BadRequestException("您的问卷正在计算cem配额，请计算完之后再启用");
        }
        survey.setStatus(SurveyStatus.COLLECTING.ordinal());
        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        survey.setAdminxQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        surveyRepository.save(survey);

        AdminxQuotaDto quotaDto = mapToDto(quota);
        quotaDto.setSurveyStatus(SurveyStatus.COLLECTING);
        quotaDto.setCemQuotaStatus(survey.getQuotaStatus());
        quotaDto.setAdminxQuotaStatus(survey.getAdminxQuotaStatus());
        quotaDto.setEnableQuota(survey.getEnableQuota());
        quotaDto.setAdminxEnableQuota(survey.getEnableAdminxQuota());
        return quotaDto;
    }

    /**
     * 初始化配额统计数据
     * 先全部清空，然后重新计算
     */
    private void initQuotaStat(Long surveyId) {
        String statKey = "quota:stat:" +  surveyId.toString();
        List<SurveyQuota> quotaList = surveyQuotaRepository.findBySid(surveyId);
        if (quotaList != null && quotaList.size() > 0) {
            List<Long> quotaIds = new ArrayList<>();
            quotaList.forEach(quota ->{
                quotaIds.add(quota.getId());
                if(!stringRedisTemplate.opsForHash().hasKey(statKey,"max:"+ quota.getId().toString())) {
                    stringRedisTemplate.opsForHash().put(statKey, "max:" + quota.getId(), quota.getMax().toString());
                    stringRedisTemplate.opsForHash().put(statKey, "use:" + quota.getId(), "0");
                }
            });
            stringRedisTemplate.opsForHash().put(statKey, "ids", StringUtils.join(quotaIds,","));
        }
    }

    /**
     * *配额计算
     * @param quotaId
     * @return
     */
    public Boolean quotaSync(Long quotaId) {
        AdminxQuota quota = adminxQuotaRepository.findById(quotaId).orElseThrow();
        if (quota == null) throw new BadRequestException("您还未添加社区配额");

        Survey survey = surveyRepository.findById(quota.getSid()).orElseThrow(() -> new RuntimeException("问卷不存在"));
        if (!survey.getEnableAdminxQuota()) throw new BadRequestException("请打开社区配额");

        switch (survey.getAdminxQuotaStatus()) {
            case CALCULATING:
                throw new BadRequestException("社区配额正在计算当中，请勿重复操作");
            case CALCULATE_COMPLETE:
                throw new BadRequestException("社区配额已经计算完成，请启用问卷");
            default:
        }

        if (survey != null) {
            stringRedisTemplate.opsForList().leftPush(adminxSyncQuotaKey,
                    JsonHelper.toJson(new TaskSyncQuotaDto(false,survey.getId())));
        }
        return true;
    }

    /**
     * 暂停问卷
     *
     * @param quotaId
     * @return
     */
    public Boolean stop(Long quotaId) {
        AdminxQuota quota = adminxQuotaRepository.findById(quotaId).orElseThrow();
        Survey survey = surveyRepository.findById(quota.getSid()).orElseThrow(() -> new RuntimeException("问卷不存在"));
        survey.setStatus(SurveyStatus.STOPPED.ordinal());
        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        surveyRepository.save(survey);
        return true;
    }


}
