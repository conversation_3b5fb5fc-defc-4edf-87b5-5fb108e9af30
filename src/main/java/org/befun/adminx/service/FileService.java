package org.befun.adminx.service;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.audit.AuditRequestItemDto;
import org.befun.adminx.dto.community.UserInfoImportDto;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Service(value = "AdminFileService")
public class FileService {
    @SneakyThrows
    public List<UserInfoImportDto> parseCsv(InputStream stream) {
        List<UserInfoImportDto> userInfoList = new ArrayList<>();
        InputStreamReader reader = new InputStreamReader(stream, StandardCharsets.UTF_8);
        try (CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            for (CSVRecord record : csvParser) {
                log.info("cuid = {}",record.get(0));
                UserInfoImportDto userInfo = UserInfoImportDto.builder()
                        .cuid(StringUtils.isEmpty(record.get(0)) ? null : Long.parseLong(record.get(0).trim()))
                        .mobile(StringUtils.isEmpty(record.get(1)) ? null : record.get(1).trim())
                        .sex(StringUtils.isEmpty(record.get(2)) ? null : record.get(2).trim())
                        .birthday(StringUtils.isEmpty(record.get(3)) ? null : record.get(3).trim())
                        .education(StringUtils.isEmpty(record.get(4)) ? null : record.get(4).trim())
                        .province(StringUtils.isEmpty(record.get(5)) ? null : record.get(5).trim())
                        .city(StringUtils.isEmpty(record.get(6)) ? null : record.get(6).trim())
                        .area(StringUtils.isEmpty(record.get(7)) ? null : record.get(7).trim())
                        .build();
                userInfoList.add(userInfo);
            }
        }

        return userInfoList;
    }

    /**
     * 按照换行切割
     * @param dto
     * @return
     */
    public String[] parseContent(AuditRequestItemDto dto) {
        if(StringUtils.isEmpty(dto.getContent())) return null;
        String content = dto.getContent().replace("，","\n").replace(",", "\n").replace("\n\n","\n");
        return content.split("\n");
    }
}
