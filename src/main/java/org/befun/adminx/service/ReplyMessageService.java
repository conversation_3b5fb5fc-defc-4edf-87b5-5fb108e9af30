package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.ReplyMessage;
import org.befun.adminx.entity.ReplyMessageDto;
import org.befun.adminx.repository.ReplyMessageRepository;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/1 11:32
 */
@Service
@Slf4j
public class ReplyMessageService extends BaseService<ReplyMessage, ReplyMessageDto, ReplyMessageRepository> {

    public Map<String,String> loadAllMessage() {
        List<ReplyMessage> replyMessageList = repository.findAll();
        Map<String,String> replyMessage = new HashMap<>();
        replyMessageList.stream().forEach(r ->{
            replyMessage.put(r.getKeyword(),r.getContent());
        });
        return replyMessage;
    }

    public String getReplyMessage(String message) {
        Map<String,String> replyMessage = loadAllMessage();
        if(replyMessage.containsKey(message))
            return replyMessage.get(message);
        else
            return replyMessage.get("收到消息");
    }

}
