package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.AppVersion;
import org.befun.adminx.constant.XmPermissions;
import org.befun.adminx.constant.XmVersions;
import org.befun.adminx.dto.user.AddTaskChangeOrgVersionDto;
import org.befun.adminx.dto.user.VersionDto;
import org.befun.adminx.entity.Organization;
import org.befun.adminx.entity.XmPermission;
import org.befun.adminx.entity.XmPermissionDto;
import org.befun.adminx.entity.XmRole;
import org.befun.adminx.repository.OrganizationRepository;
import org.befun.adminx.repository.XmPermissionRepository;
import org.befun.adminx.repository.XmRoleRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 19:32
 */
@Service
@Slf4j
public class XmPermissionService extends BaseService<XmPermission, XmPermissionDto, XmPermissionRepository> {

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private XmRoleRepository roleRepository;

    @Value("${community.uri.change-org-version}")
    private String changeOrgVersionUrl;

    @Autowired
    private OrganizationService organizationService;

    public List<AppVersion> getCemVersions() {
        return List.of(AppVersion.values());
    }

    /**
     * *更新版本version
     * @param organization
     * @param version
     */
    public String updateOrgVersions(Organization organization, HashMap version) {
        if(organization == null) return null;
        if(version == null) return null;
        String oldCemVersion = (String) organization.getVersion().getOrDefault("cem_version",null);//修改前版本
        String cemVersion = (String) version.getOrDefault("cem_version",null);//修改后版本
        Map<String,Object> orgVersion = organization.getVersion();
        if(!StringUtils.isEmpty(cemVersion) && orgVersion != null && !organization.getVersion().get("cem_version").toString().equals(cemVersion)) {
            updateOrgCemVersions(organization.getId(), cemVersion);
            //免费版升级到其他付费版本 给用户发短信
            if(oldCemVersion.equals("free") && Arrays.asList("base","update","profession").contains(cemVersion)) {
                //把请求post到cem-worker 发短信逻辑在cem-worker实现的
                postChangeOrgVersion(organization.getId(), oldCemVersion, cemVersion);
            }
            //不同版本 optionalLimit 不一样
        }
        return cemVersion;
    }


    /**
     * 升降cem版本，必须在事务中执行
     *
     * @param ogrId
     * @param cemVersion
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrgCemVersions(Long ogrId, String cemVersion) {
        organizationRepository.findById(ogrId).ifPresentOrElse(organization -> {
            Optional.of(cemVersion).ifPresentOrElse(version -> {
                AppVersion ver = organizationService.parseOrgVersion(version);
                updateOrgColumnVersion(organization, ver);
                updateOrgRoleVersionPermission(organization, ver);
            }, () -> {
                throw new BadRequestException("Invalid app version");
            });

        }, () -> {
            throw new BadRequestException("Organization not found");
        });
    }

    private void updateOrgColumnVersion(Organization organization, AppVersion appVersion) {
        VersionDto orgVersion;
        if (organization.getVersion() == null) {
            orgVersion = new VersionDto("free","empty");
        } else {
            orgVersion = JsonHelper.toObject(organization.getVersion(), VersionDto.class);
        }
        orgVersion.setCemVersion(appVersion.getText());

        organization.setVersion(JsonHelper.toMap(orgVersion));
        organizationRepository.save(organization);
    }

    private void updateOrgRoleVersionPermission(Organization organization, AppVersion cemVersion) {
        List<String> versionPermissionPath = cemVersion.getPermissions().mapToString();
        Integer adminType = 1;

        roleRepository.findByOrgId(organization.getId()).stream().filter(r -> r.getType().equals(adminType)).forEach(role -> {
            List<XmPermission> currentPermissions = repository.findByRoleId(role.getId());
            updateRolePermission(role, versionPermissionPath, currentPermissions);
        });
    }

    private void updateRolePermission(XmRole role, List<String> versionPermissionPath, List<XmPermission> currentPermissions) {
        String module = "Action";

        List<XmPermission> toAddPermissions;
        List<XmPermission> toRemovePermissions;

        toAddPermissions = versionPermissionPath
                .stream()
                .filter(permission -> !currentPermissions.stream().anyMatch(p -> p.getPermission().equals(permission)))
                .map(path -> new XmPermission(role.getId(), module, path))
                .collect(Collectors.toList());

        toRemovePermissions = currentPermissions
                .stream()
                .filter(permission -> !versionPermissionPath.contains(permission.getPermission()))
                .collect(Collectors.toList());

        repository.saveAll(toAddPermissions);
        repository.deleteAll(toRemovePermissions);

    }

    /**
     * 版本变化，post到cem-worker
     * @param orgId
     * @param oldCemVersion
     * @param cemVersion
     */
    private void postChangeOrgVersion(Long orgId, String oldCemVersion, String cemVersion) {
        try{
            //设置请求头参数
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            AddTaskChangeOrgVersionDto dto = new AddTaskChangeOrgVersionDto(orgId,oldCemVersion,cemVersion);

            RestTemplate restTemplate = new RestTemplate();
            HttpEntity httpEntity = new HttpEntity(JsonHelper.toJson(dto), headers);
            //同步到cem-worker
            ResponseEntity<String> response = restTemplate.exchange(changeOrgVersionUrl, HttpMethod.POST, httpEntity, String.class);
            log.info("changeOrgVersion response：{}", response.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }

    }

}
