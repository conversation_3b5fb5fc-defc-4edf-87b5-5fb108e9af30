package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.entity.AuditRecord;
import org.befun.adminx.entity.AuditRecordDto;
import org.befun.adminx.repository.AuditRecordRepository;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/17 18:24
 */
@Service
@Slf4j
public class AuditRecordService extends BaseService<AuditRecord, AuditRecordDto, AuditRecordRepository> {

    @Autowired
    private AuditRecordRepository auditRecordRepository;

    @Override
    public Page<AuditRecordDto> findAll(ResourceEntityQueryDto<AuditRecordDto> queryDto) {
        queryDto.setSorts(Sort.by("id").descending());
        if(queryDto.getQueryCriteriaList().size() > 0) {
            queryDto.getQueryCriteriaList().stream().forEach(query ->{
                if(query.getKey().equals("name"))
                    query.setOperator(QueryOperator.LIKE);
            });
        }
        return super.findAll(queryDto);
    }

    /**
     * 通过审核名单
     * @param sid
     * @return
     */
    public List<AuditRecord> getAllPassAudit(Long sid){
        return auditRecordRepository.findBySidAndIsPassed(sid,true);
    }

    public AuditRecord reqireAuditRecord(Long taskId, Long responseId) {
        Optional<AuditRecord> optionalAuditRecord = auditRecordRepository.findAllByTaskIdAndResponseId(taskId, responseId);
        return optionalAuditRecord.orElse(null);
    }
}
