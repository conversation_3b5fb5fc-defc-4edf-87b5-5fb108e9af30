package org.befun.adminx.service;

import cn.hutool.core.util.HexUtil;
import lombok.SneakyThrows;
import org.befun.adminx.dto.sample.SimpleLoginRequestDto;
import org.befun.adminx.dto.sample.SimpleLoginResponseDto;
import org.befun.adminx.entity.Admin;
import org.befun.adminx.repository.AdminRepository;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
public class SimpleAuthService {
    @Autowired
    private JwtService jwtService;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    protected RedisTemplate<String, SimpleLoginResponseDto> tokenRedisTemplate;

    /**
     *
     * @param login
     */
    @SneakyThrows
    public SimpleLoginResponseDto login(SimpleLoginRequestDto login) {
        // TBD
        Optional<Admin> admin = adminRepository.findFirstByUserName(login.getUsername());
        if (admin.isPresent()) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            String encodedPwd = HexUtil.encodeHexStr(md.digest(login.getPassword().getBytes()));
            if (encodedPwd.equals(admin.get().getPassword())) {
                HashMap map = new HashMap<>();
                map.put("username", admin.get().getUserName());
                map.put("userId", admin.get().getId());
                map.put("roles", admin.get().getRoles());
                String token = jwtService.encode(map);
                String key = String.format("adminx:session:%s", token);
                SimpleLoginResponseDto response = SimpleLoginResponseDto.builder()
                        .token(token)
                        .build();
                response.setRoles(admin.get().getRoles());
                response.setUsername(admin.get().getUserName());
                response.setUserId(admin.get().getId());
                tokenRedisTemplate.opsForValue().set(key, response);
                return response;
            }
        }
        throw new BadRequestException("账号或密码错误");
    }

}
