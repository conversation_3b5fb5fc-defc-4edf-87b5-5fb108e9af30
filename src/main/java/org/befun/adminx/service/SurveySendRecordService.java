package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.SubmitStatus;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.adminx.dto.ext.ResendDto;
import org.befun.adminx.dto.query.ResendTaskQueryDto;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.entity.SurveySendRecordDto;
import org.befun.adminx.repository.SurveySendRecordRepository;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 10:00
 */
@Service
@Slf4j
public class SurveySendRecordService extends BaseService<SurveySendRecord, SurveySendRecordDto, SurveySendRecordRepository> {

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;

    @Override
    public Page<SurveySendRecordDto> findAll(ResourceEntityQueryDto<SurveySendRecordDto> queryDto) {
        return super.findAll(queryDto);
    }

    /**
     * *更新填答状态
     * @param taskId
     * @param cuid
     */
    public void updateSurveySendRecordStatus(Long taskId, SubmitStatus submitStatus, Long cuid, ResponseStatus status,Boolean completeStatus) {
        if(taskId == null || taskId == 0l || cuid == null)
            throw new BadRequestException("追访记录不存在");
        repository.updateSubmitStatusByTaskIdAndCuid(submitStatus, status, taskId, cuid,completeStatus);
    }

    public Page<SurveySendRecordDto> getListByTaskId(@NotNull Long taskId, ResourceEntityQueryDto<SurveySendRecordDto> queryDto) {
        queryDto.addCriteria(new ResourceQueryCriteria("taskId", taskId));
        Boolean flag = false;

        for (ResourceQueryCriteria entry : queryDto.getQueryCriteriaList()) {
            if(entry.getKey().equals("status")) {
                flag = true;
            }
        }
        //默认筛选未完成的发送记录
        if(!flag) {
            queryDto.addCriteria(new ResourceQueryCriteria("status",false));
        }
        return super.findAll(queryDto);
    }

    /**
     * 重发 筛选记录id
     * @param taskId
     * @param resendDto
     * @return
     */
    public List<Long> getLogIdsByCondition(@NotNull Long taskId, ResendDto resendDto) {
        if(taskId == null || taskId == 0l || resendDto == null) return List.of();
        if(resendDto.getLogIds() != null && resendDto.getLogIds().size() >0) return resendDto.getLogIds();
        ResendTaskQueryDto dto;
        if(resendDto != null && resendDto.get_condition() != null) {
            dto = JsonHelper.toObject(resendDto.get_condition(), ResendTaskQueryDto.class);
        } else {
            dto = JsonHelper.toObject(new HashMap<>(), ResendTaskQueryDto.class);
        }

        ResourceEntityQueryDto<SurveySendRecordDto> params = dto.transform();
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("status", false));
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("taskId", taskId));

        Field[] fields =  dto.getClass().getDeclaredFields();
        Arrays.stream(fields).forEach(field ->{
            String key = field.getName();
            String name = key.substring(0,1).toUpperCase() + key.substring(1);
            try {
                Method m = dto.getClass().getMethod("get" + name);
                Object value = m.invoke(dto);
                if(value != null && !key.equals("sendStatus_in")) {
                    params.getQueryCriteriaList().add(new ResourceQueryCriteria(key, value));
                } else if(value != null && key.equals("sendStatus_in")) {
                    String[] sendStatusList = ((String) value).split(",");
                    List<SendStatus> list = Arrays.stream(sendStatusList).map(SendStatus::valueOf).collect(Collectors.toList());
                    params.getQueryCriteriaList().add(new ResourceQueryCriteria("sendStatus", list, QueryOperator.IN));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return super.findAll(params).getContent().stream().map(SurveySendRecordDto::getId).collect(Collectors.toList());
    }

    /**
     * *批量获取问卷推送记录
     * @param taskId
     * @return
     */
    public Map<Long, SurveySendRecord> getSendRecordByTaskId(Long taskId) {
        if(taskId == null)  return null;
        List<SurveySendRecord> records = repository.findByTaskId(taskId);
        if(records == null || records.isEmpty()) return null;
        Map<Long,SurveySendRecord> map = new HashMap<>();
        records.forEach(u -> {
            if(u != null) {
                map.put(u.getCuid(),u);
            }
        });
        return map;
    }
}
