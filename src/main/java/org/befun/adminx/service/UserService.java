package org.befun.adminx.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.AppType;
import org.befun.adminx.constant.AppVersion;
import org.befun.adminx.constant.survey.TemplateType;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.clone.CloneAccountDto;
import org.befun.adminx.dto.user.*;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.dto.LoginAppDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.TemplateGroup;
import org.befun.adminx.entity.survey.TemplateGroupDto;
import org.befun.adminx.entity.survey.TemplateSurveyQuestion;
import org.befun.adminx.repository.CustomerStatisticsRepository;
import org.befun.adminx.repository.OrganizationRepository;
import org.befun.adminx.repository.UserRepository;
import org.befun.adminx.repository.template.TemplateSurveyQuestionRepository;
import org.befun.adminx.service.template.TemplateGroupService;
import org.befun.adminx.service.template.TemplateMapService;
import org.befun.adminx.service.template.TemplateSurveyQuestionService;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.eclipse.persistence.jpa.jpql.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 19:32
 */
@Service
@Slf4j
public class UserService extends BaseService<XmPlusUser, XmPlusUserDto, UserRepository> {

    @Autowired
    private JourneyMapService journeyMapService;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private CustomerStatisticsRepository customerStatisticsRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    @Autowired
    private XmPermissionService xmPermissionService;

    @Autowired
    private OrganizationConfigService organizationConfigService;

    @Autowired
    private TemplateGroupService templateGroupService;

    @Autowired
    private TemplateSurveyQuestionService templateSurveyQuestionService;

    @Autowired
    private TemplateMapService templateMapService;

    @Autowired
    private TemplateSurveyQuestionRepository templateSurveyQuestionRepository;

    @Autowired
    private  BackupService backupService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${community.source.org-id}")
    private Long sourceOrgId;

    @Value("${community.source.user-id}")
    private Long sourceUserId;

    @Value("${community.uri.copy-survey}")
    private String copySurveyUri;

    @Value("${community.uri.copy-journey-map}")
    private String copyJourneyMapUri;

    @Value("${community.uri.register-user}")
    private String userRegisterUri;

    public XmPlusUser requireUser(Long id) {
        XmPlusUser xmPlusUserOptional = repository.getOne(id);
        return xmPlusUserOptional;
    }

    @Override
    public <S extends BaseEntityDTO<XmPlusUser>> XmPlusUserDto create(S data) {
        ((XmPlusUserDto) data).setIsAdmin(1);
        return super.create(data);
    }

    @Override
    public Page<XmPlusUserDto> findAll(ResourceEntityQueryDto<XmPlusUserDto> queryDto) {
//        queryDto.addCriteria(new ResourceQueryCriteria("isAdmin", 1));
        AtomicReference<Boolean> equalStatus = new AtomicReference<>(true);
        //如果没有排序的话 默认根据id排序
        if (queryDto.getSorts().isEmpty()) queryDto.setSorts(Sort.by("id").descending());
        AtomicReference<AppType> app = new AtomicReference<>(AppType.cem);
        // 判断查询条件中是否有appTypes，提取app的类型
        queryDto.getQueryCriteriaList().stream().filter(q -> q.getKey().equals("appTypes")).forEach(q -> {
            app.set(AppType.valueOf(q.getValue().toString()));
        });

        queryDto.getQueryCriteriaList().stream().forEach(q -> {
            if (q.getKey().equals("status")) equalStatus.set(false);
            if (q.getKey().equals("name")) q.setOperator(QueryOperator.LIKE);
            if (q.getKey().equals("code")) q.setOperator(QueryOperator.LIKE);
            if (q.getKey().equals("mobile")) q.setOperator(QueryOperator.LIKE);
            if (q.getKey().equals("email")) q.setOperator(QueryOperator.LIKE);
            if (q.getKey().equals("appTypes")) q.setOperator(QueryOperator.LIKE);

            if (q.getParamKey().equals("organization.version")) {
                q.setOperator(QueryOperator.LIKE);
                q.setKey("organization.strVersion");
                q.setValue(String.format("%s_version\":\"%s",app.get().name(), q.getValue().toString()));
            }
        });

        //默认筛选条件过滤 status=3的客户 如果查询条件有这个条件 不过滤
        if (equalStatus.get()) {
            queryDto.addCriteria(new ResourceQueryCriteria("status", 3, QueryOperator.NOT_EQUAL));
        }
        //默认筛选主账号 is_admin = 1
        ResourceQueryCriteria isAdmin = new ResourceQueryCriteria("isAdmin",1,QueryOperator.EQUAL);
        isAdmin.setParamKey("isAdmin");
        queryDto.addCriteria(isAdmin);

        return super.findAll(queryDto);
    }

    @Override
    protected void afterMapToDto(List<XmPlusUser> entity, List<XmPlusUserDto> dto) {
        super.afterMapToDto(entity, dto);
        dto.forEach(user -> {
            user.setRealMobile(user.getMobile());
            user.setRealEmail(user.getEmail());
            user.setMobile(RegularExpressionUtils.hiddenMobile(user.getMobile()));
            user.setEmail(RegularExpressionUtils.hiddenEmail(user.getEmail()));
            if (user.getOrganization() != null) {
                Optional<CustomerStatistics> optionalCustomerStatistics = customerStatisticsRepository.findOneById(user.getOrganization().getId());
                user.setCustomerStatistics(optionalCustomerStatistics.orElse(null));
                String smsBalance = stringRedisTemplate.opsForValue().get("limiter.smsbalance." + user.getOrganization().getId());
                user.setSmsBalance(StringUtils.isEmpty(smsBalance) ? 0l : Long.parseLong(smsBalance));
                String aiPoint = stringRedisTemplate.opsForValue().get("ai-point-balance:" + user.getOrganization().getId());
                user.setAiPoint(StringUtils.isEmpty(aiPoint) ? 0l : Long.parseLong(aiPoint));
                user.setAppTypes(JsonHelper.toList(user.getOrganization().getAppTypes(), String.class));
                user.setLatestLogin(getOrgLastLoginTime(user.getOrganization().getId()));
                user.setSurveyContentAudit(organizationConfigService.isEnableSurveyContentAudit(user.getOrganization().getId()));
            }
        });
    }

    private String versionName(String versionJson) {
        VersionDto version = JsonHelper.toObject(versionJson, VersionDto.class);
        switch (version.getCemVersion()) {
            case "empty":
                return "不开放使用";
            case "free":
                return "免费版";
            case "base":
                return "基础版";
            case "update":
                return "团队专业版";
            case "profession":
                return "企业旗舰版";
            default:
                return "";
        }
    }

    /**
     * 获取管理员用户
     * @return
     */
    public List<XmPlusUser> getAdminUserList() {
        return repository.findByIsAdminAndStatus(1, 1);
    }

    public XmPlusUserDto getTemplate(String mobile) {
        Optional<XmPlusUser> optional = repository.findByMobile(mobile);
        Assert.isNotNull(optional.get(), "账号不存在");
        XmPlusUserDto dto = mapToDto(optional.get());

        //组织名称
        Optional<Organization> optionalOrganization = organizationRepository.findOneById(dto.getOrganization().getId());
        //客户旅程
        List<JourneyMap> journeyMaps = journeyMapService.getTemplateJourneyMap(sourceOrgId, sourceUserId);
        //问卷
        List<Survey> surveys = surveyService.getTemplateSurveys(sourceOrgId, sourceUserId);
        dto.setName(optionalOrganization.get().getName());
        dto.setSurveys(surveys);
        dto.setJourneyMaps(journeyMaps);
        return dto;
    }

    public List<Survey> getTemplateSurveys() {
        return surveyService.getTemplateSurveys(sourceOrgId, sourceUserId);
    }

    public List<JourneyMap> getTemplateJourneyMap() {
        return journeyMapService.getTemplateJourneyMap(sourceOrgId, sourceUserId);
    }

    public List<TemplateGroupDto> getTemplateSurveyQuestions() {
        ResourceEntityQueryDto<TemplateGroupDto> queryDto = new ResourceEntityQueryDto<>();
        queryDto.addCriteria(new ResourceQueryCriteria("orgId", sourceOrgId, QueryOperator.EQUAL));
        queryDto.addCriteria(new ResourceQueryCriteria("type", TemplateType.QUESTION, QueryOperator.EQUAL));
        return templateGroupService.findAll(queryDto);
    }

    /**
     * 推送模板
     *
     * @param dto
     */
    public void sendTemplate(@NotNull XmPlusUserDto user, @NotNull SendTemplateDto dto) {
        try {
            dto.setTargetOrgId(user.getOrganization().getId());
            dto.setTargetUserId(user.getId());
            //设置请求头参数
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");

            RestTemplate restTemplate = new RestTemplate();
            HttpEntity httpEntity = new HttpEntity(JsonHelper.toJson(dto), headers);
            //复制问卷
            ResponseEntity<String> surveyExchange = restTemplate.exchange(copySurveyUri, HttpMethod.POST, httpEntity, String.class);
            System.out.println("copySurvey response：" + surveyExchange.getBody());
            //复制旅程
            ResponseEntity<String> journeyMapExchange = restTemplate.exchange(copyJourneyMapUri, HttpMethod.POST, httpEntity, String.class);
            System.out.println("copyJourneyMap response：" + journeyMapExchange.getBody());
            //复制题库
            if (dto.getSourceTemplateQuestionsIds() != null && dto.getSourceTemplateQuestionsIds().size() > 0) {
                Map<Long, List<Long>> listMap = new HashMap<>();
                for (List<Long> d : dto.getSourceTemplateQuestionsIds()) {
                    Long key = d.get(0);
                    Long value = d.get(1);
                    listMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
                }
                if (listMap != null && listMap.size() > 0) {
                    for (Map.Entry<Long, List<Long>> entry : listMap.entrySet()) {
                        saveTemplate(entry.getKey(), entry.getValue(), dto.getTargetOrgId(), dto.getTargetUserId());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 保存推荐题库
     * @param groupId
     * @param tempQuestionIds
     * @param orgId
     */
    public void saveTemplate(Long groupId, List<Long> tempQuestionIds, Long orgId, Long userId) {

        try {
            TemplateGroup sourceGroup = templateGroupService.require(groupId);

            TemplateGroup group = templateGroupService.getEmptyGroup(orgId, sourceGroup);
            List<TemplateSurveyQuestion> templateSurveyQuestions = new ArrayList<>();
            tempQuestionIds.forEach(q -> {
                TemplateSurveyQuestion sourceQuestion = templateSurveyQuestionService.requireTemplateQuestion(q);
                TemplateSurveyQuestion templateSurveyQuestion = new TemplateSurveyQuestion();
                templateSurveyQuestion = templateMapService.mapToTemplate(sourceQuestion, templateSurveyQuestion, false);
                templateSurveyQuestion.setOrgId(orgId);
                templateSurveyQuestion.setUserId(userId);
                templateSurveyQuestion.setGroupId(group.getId());
                templateSurveyQuestions.add(templateSurveyQuestion);
            });
            templateSurveyQuestionRepository.saveAll(templateSurveyQuestions);
        } catch (Exception e) {
            log.error("题目转模板失败", e);
        }
    }

    /**
     * 禁用用户
     *
     * @param user
     * @return
     */
    public AuditResponseDto block(XmPlusUserDto user) {
        Organization organization = organizationRepository.findOneById(user.getOrganization().getId()).orElseThrow(() -> new BadRequestException("企业不存在"));
        organization.setIsBlock(1);
        organizationRepository.save(organization);
        //删除session 踢下线
        //deleteSession(user.getId());
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    public void deleteSession(Long userId) {
        //删除session 踢下线
        Set<String> sessionSet = stringRedisTemplate.opsForZSet().range(String.format("user.session:%s", userId), 0, -1);
        if (sessionSet.size() > 0) {
            Arrays.stream(sessionSet.toArray()).forEach(s -> {
                stringRedisTemplate.delete(String.format("session:%s", s.toString()));
            });
        }
    }

    /**
     * *修改短信余额
     *
     * @param orgId
     * @param smsBalance
     */
    public void updateSmsBalance(Long orgId, Long smsBalance) {
        if (orgId == null || orgId == 0l || smsBalance == null || smsBalance < 0l) return;
        String sql = String.format("update cem_platform.organization_wallet set sms = %d where org_id = %d;", smsBalance, orgId);
        jdbcTemplate.execute(sql);
        //更新redis
        stringRedisTemplate.opsForValue().set("limiter.smsbalance." + orgId.toString(), smsBalance.toString());
    }
    /**
     * *修改ai点数
     *
     * @param orgId
     * @param aiPoint
     */
    public void updateAiPoint(Long orgId, Long aiPoint) {
        if (orgId == null || orgId == 0l || aiPoint == null || aiPoint < 0l) return;
        String sql = String.format("update cem_platform.organization_wallet set ai_point = %d where org_id = %d;", aiPoint, orgId);
        jdbcTemplate.execute(sql);
        //更新redis
        stringRedisTemplate.opsForValue().set("ai-point-balance:" + orgId.toString(), aiPoint.toString());
    }

    /**
     * 启用用户
     *
     * @param user
     * @return
     */
    public AuditResponseDto unblock(XmPlusUserDto user) {
        Organization organization = organizationRepository.findOneById(user.getOrganization().getId()).orElseThrow(() -> new BadRequestException("企业不存在"));
        organization.setIsBlock(0);
        organizationRepository.save(organization);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 解绑手机号
     *
     * @param user
     * @return
     */
    public AuditResponseDto unbind(XmPlusUserDto user) {
        user.getEntity().setStatus(3);
        user.getEntity().setMobile(null);
        repository.save(user.getEntity());
        if (!StringUtils.isEmpty(user.getOrganization().getName()) && !StringUtils.isEmpty(user.getMobile())) {
            String sql = String.format(" update users set tel = '',is_enable = 2 where tel = '%s';", user.getMobile());
            jdbcTemplate.execute(sql);
        }
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * *修改版本
     *
     * @param id
     * @param update
     * @return
     */
    public AuditResponseDto updateVersion(Long id, UserInfoDto update) {
        XmPlusUser user = require(id);
        if (user == null) throw new BadRequestException("用户不存在");
        //更新user表
        if (!StringUtils.isEmpty(update.getPassword())) {
            user.setPassword(BCrypt.hashpw(update.getPassword(), BCrypt.gensalt()));
        }
        if (!StringUtils.isEmpty(update.getMobile())) {
//            user.setMobile(update.getMobile());
        }
        //更新organization表
        if (user.getOrganization() != null) {
            String cemVersion = null;
            if (update.getVersion() != null) {
                cemVersion = xmPermissionService.updateOrgVersions(user.getOrganization(), update.getVersion());
                user.getOrganization().setVersion(update.getVersion());
            }
            if (update.getAppTypes() != null) {
                String[] appType = update.getAppTypes().split(",");
                int loginSurveyPlus = Arrays.stream(appType).anyMatch(q -> q.equals("surveyplus")) ? 1 : 0;
                LoginAppDto loginAppDto = new LoginAppDto(1, loginSurveyPlus);
                user.getOrganization().setAppTypes(JsonHelper.toJson(appType));
                user.setAvailableSystems(JsonHelper.toJson(loginAppDto));
            }
            if (update.getAvailableDateBegin() != null)
                user.getOrganization().setAvailableDateBegin(update.getAvailableDateBegin());
            if (update.getAvailableDateEnd() != null)
                user.getOrganization().setAvailableDateEnd(update.getAvailableDateEnd());
            if (update.getOptionalLimit() != null)
                parseOrgOptionalLimit(user.getOrganization(), cemVersion, update.getOptionalLimit(), true);
            if (StringUtils.isNotEmpty(update.getAliasName())){
                user.getOrganization().setAliasName(update.getAliasName());
            }
            if (StringUtils.isNotEmpty(update.getRemark())){
                user.getOrganization().setRemark(update.getRemark());
            }

            //修改短信余额
            if (update.getSmsBalance() != null && update.getSmsBalance() >= 0l) {
                updateSmsBalance(user.getOrganization().getId(), update.getSmsBalance());
            }
            //修改ai点数
            if (update.getAiPoint() != null && update.getAiPoint() >= 0l) {
                updateAiPoint(user.getOrganization().getId(), update.getAiPoint());
            }
        }
        repository.save(user);

        //如果修改了密码 踢下线 重新登录
        if (!StringUtils.isEmpty(update.getPassword())) {
            //删除session 踢下线
            deleteSession(user.getId());
        }

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 不同权限 问卷、旅程、客户画像、子账号的数量限制不一样
     * @param org
     * @param cemVersion
     * @param optionalLimit
     * @param operation true:手动 false:自动
     */
    public void parseOrgOptionalLimit(Organization org, String cemVersion, HashMap optionalLimit, Boolean operation) {
        if (org == null || StringUtils.isEmpty(cemVersion)) return;
        OrganizationOptionalLimitDto limitDto;
        switch (cemVersion) {
            case "free":
                limitDto = AppVersion.FREE.getOptionalLimit();
                break;
            case "base":
                limitDto = AppVersion.BASE.getOptionalLimit();
                break;
            case "update":
                limitDto = AppVersion.UPDATE.getOptionalLimit();
                break;
            case "profession":
                limitDto = AppVersion.PROFESSION.getOptionalLimit();
                break;
            case "empty":
            default:
                limitDto = AppVersion.EMPTY.getOptionalLimit();
        }
        //如果自动降级 按照版本限制来
        if (!optionalLimit.containsKey("child_user_limit") || !operation) optionalLimit.put("child_user_limit", limitDto.getChildUserLimit());
        if (!optionalLimit.containsKey("customer_lifecycle_limit") || !operation) optionalLimit.put("customer_lifecycle_limit", limitDto.getCustomerLifecycleLimit());
        if (!optionalLimit.containsKey("surveys_limit") || !operation) optionalLimit.put("surveys_limit", limitDto.getSurveysLimit());
        if (!optionalLimit.containsKey("event_rules_limit") || !operation) optionalLimit.put("event_rules_limit", limitDto.getEventRulesLimit());
        if (!optionalLimit.containsKey("customer_person_limit") || !operation) optionalLimit.put("customer_person_limit", limitDto.getCustomerPersonLimit());
        if (!optionalLimit.containsKey("bi_dashboard_limit") || !operation) optionalLimit.put("bi_dashboard_limit", limitDto.getBiDashboardLimit());
        if (optionalLimit.containsKey("logo_change_limit")) optionalLimit.put("logo_change_limit", Boolean.parseBoolean(optionalLimit.get("logo_change_limit").toString()));
        if (optionalLimit.containsKey("bi_data_field_edit")) optionalLimit.put("bi_data_field_edit", Boolean.parseBoolean(optionalLimit.get("bi_data_field_edit").toString()));
        if (optionalLimit.containsKey("sys_manage_api_edit")) optionalLimit.put("sys_manage_api_edit", Boolean.parseBoolean(optionalLimit.get("sys_manage_api_edit").toString()));
        if (optionalLimit.containsKey("multi_language_limit"))
            optionalLimit.put("multi_language_limit", Boolean.parseBoolean(optionalLimit.get("multi_language_limit").toString()));
        org.setOptionalLimit(optionalLimit);
    }

    public void parseOrgVersion(Organization org, String cemVersion) {
        if (org == null || StringUtils.isEmpty(cemVersion) || org.getVersion() == null) return;
        org.getVersion().replace("cem_version",cemVersion);
    }

    /**
     * *同步所有的老调研家账号权限 到新调研家
     *
     * @return
     */
    public AuditResponseDto batchUpdateSurveyPlusVersion() {

        List<Organization> organizations = organizationRepository.findAll();
        organizations.stream().forEach(organization -> {
            if (organization.getVersion() != null) {
                String cem_version = organization.getVersion().getOrDefault("cem_version", "").toString();
                if (cem_version.equals("empty")) {
                    organization.setAppTypes("[\"surveyplus\"]");
                    repository.updateAvailableSystemsByOrganization_Id(JsonHelper.toJson(new LoginAppDto(1, 1)), organization.getId());
                    xmPermissionService.updateOrgCemVersions(organization.getId(), "free");
                }
            }
        });
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    public AuditResponseDto updateCemPermission(XmVersionUpdateDto dto) {
        Organization organization = organizationRepository.getOne(dto.getOrgId());
        organization.setAppTypes("[\"surveyplus\"]");
        repository.updateAvailableSystemsByOrganization_Id(JsonHelper.toJson(new LoginAppDto(1, 1)), organization.getId());
        xmPermissionService.updateOrgCemVersions(dto.getOrgId(), "free");
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 获取企业所有账号里面最后一个登录时间
     *
     * @param orgId
     * @return
     */
    private String getOrgLastLoginTime(Long orgId) {
        if (orgId == null || orgId == 0L) return null;
        Optional<XmPlusUser> optional = repository.findFirstByOrganization_IdAndStatusOrderByLatestLoginDesc(orgId, 1);
        return optional.map(XmPlusUser::getLatestLogin).orElse(null);
    }

    /**
     * adminx注册体验家账号
     *
     * @param dto
     * @return
     */
    public AuditResponseDto register(RegisterInfoDto dto) {
        dto.setVerifyCode("123456");
        //设置请求头参数
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity httpEntity = new HttpEntity(JsonHelper.toJson(dto), headers);
            //复制问卷
            ResponseEntity<String> surveyExchange = restTemplate.exchange(userRegisterUri, HttpMethod.POST, httpEntity, String.class);
            System.out.println("user register response：" + surveyExchange.getBody());

            JsonNode jsonNode = objectMapper.readTree(surveyExchange.getBody());
            Map<String, Object> response = objectMapper.convertValue(jsonNode, Map.class);

            if (response.get("code").equals(200) && dto.getClone().equals("clone")) {
                log.error("开始复制模版帐号");
                Map<String, Object> data = (Map<String, Object>) response.get("data");
                Long orgId = Long.valueOf(data.get("orgId").toString());
                CloneAccountDto cloneAccountDto = new CloneAccountDto();
                cloneAccountDto.setTargetOrgId(orgId);
                backupService.cloneAccount(cloneAccountDto);

                String redis_department_cache_key = String.format("department.tree:%d", orgId);
                stringRedisTemplate.delete(redis_department_cache_key);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    @Transactional
    public Boolean toggleSurveyContentAudit(Long userId) {
        XmPlusUser user = requireUser(userId);
        organizationConfigService.toggleSurveyContentAudit(user.getOrganization().getId());
        return true;
    }
}
