package org.befun.adminx.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.entity.OrganizationConfig;
import org.befun.adminx.entity.OrganizationConfigDto;
import org.befun.adminx.repository.OrganizationConfigRepository;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public class OrganizationConfigService extends BaseService<OrganizationConfig, OrganizationConfigDto, OrganizationConfigRepository> {

    public boolean isEnableSurveyContentAudit(long orgId) {
        OrganizationConfig config = repository.findFirstByOrgIdAndType(orgId, "surveyContentAudit");
        return isEnableSurveyContentAudit(config);
    }

    private boolean isEnableSurveyContentAudit(OrganizationConfig config) {
        if (config != null && StringUtils.isNotEmpty(config.getConfig())) {
            Map<String, Object> json = JsonHelper.toMap(config.getConfig());
            Object enable = json.get("surveyContentAudit");
            if (enable instanceof Boolean) {
                return Boolean.parseBoolean(enable.toString());
            }
        }
        return true;
    }

    public void toggleSurveyContentAudit(long orgId) {
        OrganizationConfig config = repository.findFirstByOrgIdAndType(orgId, "surveyContentAudit");
        boolean enable = isEnableSurveyContentAudit(config);
        if (config == null) {
            config = new OrganizationConfig();
            config.setType("surveyContentAudit");
            config.setOrgId(orgId);
        }
        Map<String, Object> json = Map.of("surveyContentAudit", !enable);
        config.setConfig(JsonHelper.toJson(json));
        repository.save(config);
    }
}
