package org.befun.adminx.service;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.QuotaChannelType;
import org.befun.adminx.constant.SurveyQuotaStatus;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.adminx.dto.ResetResponseDto;
import org.befun.adminx.dto.SurveyProcessDto;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.repository.SurveyCompletedRecordRepository;
import org.befun.adminx.repository.SurveyQuotaRepository;
import org.befun.adminx.repository.SurveyRepository;
import org.befun.adminx.repository.SurveyResponseRepository;
import org.befun.adminx.service.quota.QuotaAllMatchLimit;
import org.befun.adminx.service.quota.bean.QuotaInit;
import org.befun.adminx.service.quota.bean.QuotaSyncProgress;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuotaService extends BaseService<SurveyQuota, SurveyQuotaDto, SurveyQuotaRepository> {

    private static final String QUOTA_TOKEN = "quota:token:%s:%s";                      // set surveyId quotaId
    private static final String QUOTA_STAT = "quota:stat:%s";                           // hash surveyId
    private static final String QUOTA_STAT_MAX_HASH_KEY = "max:%s";                     // 配额统计信息 QUOTA_STAT 的hashKey
    private static final String QUOTA_STAT_USE_HASH_KEY = "use:%s";                     // 配额统计信息 QUOTA_STAT 的hashKey

    private static final String K_OPEN_ID = "cache:channel-limit:%d:%d:openid";    // surveyId channelId  zset value=openid, score=finishTime


    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private SurveyQuotaRepository surveyQuotaRepository;

    @Autowired
    private QuotaAllMatchLimit quotaAllMatchLimit;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private SurveyCompletedRecordRepository surveyCompletedRecordRepository;

    @Override
    protected void afterMapToDto(List<SurveyQuota> entity, List<SurveyQuotaDto> dto) {
        dto.forEach(q -> q.setCurrent(getOneQuotaUseStat(q.getSid(), q.getId())));
        super.afterMapToDto(entity, dto);
    }

    protected void requireSurveyId(Long surveyId) {
        if (surveyId == null || surveyId <= 0) {
            throw new BadRequestException("surveyId 不能为空");
        }
    }

    protected void requireQuotaId(Long quotaId) {
        if (quotaId == null || quotaId <= 0) {
            throw new BadRequestException("quotaId 不能为空");
        }
    }

    protected HashOperations<String, String, String> getHashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    protected String getQuotaStatKey(Long surveyId) {
        return String.format(QUOTA_STAT, surveyId.toString());
    }

    @Override
    public <S extends BaseEntityDTO<SurveyQuota>> SurveyQuotaDto create(S data) {
        SurveyQuotaDto dto = (SurveyQuotaDto) data;
        dto.setChannelType(QuotaChannelType.SURVEY_PLUS);
        Survey survey = surveyRepository.findById(dto.getSid()).orElseThrow(() -> new BadRequestException("问卷不存在"));
        if (survey.getStatus() == SurveyStatus.COLLECTING.ordinal()) {
            throw new BadRequestException("问卷启用中不能添加配额");
        }
        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        surveyRepository.save(survey);
        return super.create(data);
    }

    @SneakyThrows
    @Override
    public <S extends BaseEntityDTO<SurveyQuota>> SurveyQuotaDto updateOne(long id, S change) {

        SurveyQuotaDto dto = (SurveyQuotaDto) change;

        Optional<SurveyQuota> surveyQuotaOptional = surveyQuotaRepository.findById(id);
        if (surveyQuotaOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        SurveyQuota surveyQuota = surveyQuotaOptional.get();
        Survey survey = surveyRepository.findById(surveyQuota.getSid()).orElseThrow(() -> new RuntimeException("问卷不存在"));

        //对比表达式 如果不一样 需要更新配额计算状态
        String oldExpression = Optional.ofNullable(surveyQuota.getExpression()).orElse("");
        String newExpression = Optional.ofNullable(dto.getExpression()).orElse("");
        if (!oldExpression.equals(newExpression)) {
            //获取问卷信息
            //问卷配额状态 修改为计算中
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            surveyRepository.save(survey);
            //TODO 修改了配额表达式需要重新统计所有的配额
            surveyQuota.setExpressionHash(null);
        }

        //更新配额数量需要 更新quota:stats:surveyId hash的统计数据
        if (dto.getMax() != null && dto.getMax() > 0) {
            surveyQuota.setMax(dto.getMax());
            quotaAllMatchLimit.updateQuotaMax(surveyQuota.getSid(), surveyQuota.getId(), dto.getMax());
        }

        //获取配额使用的数据
        SurveyQuotaDto res = super.updateOne(id, change);
        res.setCurrent(quotaAllMatchLimit.getOneQuotaUseStat(surveyQuota.getSid(), surveyQuota.getId()));
        return res;
    }

    @Override
    public Page<SurveyQuotaDto> findAll(ResourceEntityQueryDto<SurveyQuotaDto> queryDto) {

        queryDto.addCriteria(new ResourceQueryCriteria("channelType", QuotaChannelType.SURVEY_PLUS, QueryOperator.EQUAL));
        Page<SurveyQuotaDto> surveyQuotaDtos = super.findAll(queryDto);
        surveyQuotaDtos.forEach(x->{
            x.setCurrent(quotaAllMatchLimit.getOneQuotaUseStat(x.getSid(),x.getId()));
        });
        return surveyQuotaDtos;
    }

    /**
     * 校验问卷配额 状态
     *
     * @param survey
     * @return
     */
    public Boolean checkSurveyQuota(@NotNull Survey survey) {
        List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySidAndChannelType(survey.getId(), QuotaChannelType.SURVEY_PLUS);
        if (survey.getEnableAdminxQuota() && !surveyQuotas.isEmpty())
            return true;
        else
            return false;
    }

    /**
     * 配额id获取配额使用情况
     *
     * @param surveyId
     * @param eid
     * @return
     */
    public Long getOneQuotaUseStat(Long surveyId, Long eid) {
        requireSurveyId(surveyId);
        requireQuotaId(eid);
        String use = getHashOpt().get(getQuotaStatKey(surveyId), getQuotaStatUseHashKey(eid));
        return !StringUtils.isNumeric(use) ? 0L : Long.parseLong(use);
    }

    /**
     * 配额统计hash use:surveyId
     *
     * @param quotaId
     * @return
     */
    protected String getQuotaStatUseHashKey(Long quotaId) {
        return String.format(QUOTA_STAT_USE_HASH_KEY, quotaId.toString());
    }

    /**
     * 根据问卷id获取配额
     *
     * @param surveyId
     * @return
     */
    public List<SurveyQuotaDto> getQuotasBySid(Long surveyId) {
        ResourceEntityQueryDto<SurveyQuotaDto> queryDto = new ResourceEntityQueryDto<>();
        queryDto.addCriteria(new ResourceQueryCriteria("sid", surveyId, QueryOperator.EQUAL));
        queryDto.setLimit(999);
        Page<SurveyQuotaDto> quotaList = super.findAll(queryDto);
        return quotaList.getContent();
    }


    /**
     * 重新计算配额
     * <p>
     * 只有在问卷停止时才能重新计算配额
     * 其他时候只会返回配额的计算进度
     * 新增、修改配额应该只跑变化的配额，但是不能分辨是不是在停用一段时间后再操作的，只有选择全部重新跑
     *
     * @param survey
     */
    @Deprecated(since = "1.10.7")
    @Transactional
    public SurveyProcessDto calculateQuota(@NotNull Survey survey) {

        SurveyProcessDto quotaProcessDto = new SurveyProcessDto();

        // 配额计算中只需要返回进度就行了
        if (survey.getAdminxQuotaStatus() == SurveyQuotaStatus.CALCULATING) {
            Optional<QuotaSyncProgress> process = quotaAllMatchLimit.syncProgress(survey.getId());

            if (process.isPresent()) {
                log.info("quotaId = {} , Parsed = {} , Total = {}", survey.getId(), process.get().getParsed(), process.get().getTotal());
                quotaProcessDto.setProcess(Double.valueOf(process.get().getParsed() * 100.0 / process.get().getTotal()).intValue());
                quotaProcessDto.setCompleted(process.get().completed);
                if (!process.get().completed) return quotaProcessDto;
            }
        }

        if ((survey.getStatus() != SurveyStatus.COLLECTING.ordinal())) {
            //只同步surveyplus渠道
            List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySidAndChannelType(survey.getId(), QuotaChannelType.SURVEY_PLUS);

            List<ResponseStatus> status = List.of(ResponseStatus.FINAL_SUBMIT, ResponseStatus.WAIT_AUDIT);
            Long countResponse = responseRepository.countBySurveyIdAndStatusInAndCollectorMethod(survey.getId(), status, SurveyCollectorMethod.SURVEY_PLUS);

            List<QuotaInit> allQuotas = surveyQuotas.stream().map(quota -> new QuotaInit(quota.getId(), quota.getMax())).collect(Collectors.toList());
            final List<SurveyQuota> syncQuotas = surveyQuotas.stream().filter(quota -> (
                            //  新增的配额ExpressionHash为空
                            //  修改的配额ExpressionHash和现在的Expression计算出来不同
                            quota.getExpressionHash() == null ||
                                    !DigestUtils.sha256Hex(quota.getExpression()).equals(quota.getExpressionHash())
                    )
            ).collect(Collectors.toList());

            log.info("calculate quota for survey: {} with sync quotas: {} with {} response", survey.getId(), syncQuotas.stream().map(Objects::toString).collect(Collectors.joining(",")), countResponse);

            quotaProcessDto.setCompleted(false);
            quotaProcessDto.setProcess(0);
            //同步配额
            this.syncQuotaStat(survey, allQuotas, syncQuotas, countResponse);
        }

        return quotaProcessDto;
    }

    /**
     * 同步配额
     *
     * @param survey
     * @param allQuotas
     * @param syncQuotas
     * @param countResponse
     */
    @Deprecated(since = "1.10.7")
    public void syncQuotaStat(Survey survey, List<QuotaInit> allQuotas, List<SurveyQuota> syncQuotas, Long countResponse) {
        quotaAllMatchLimit.syncQuotaStat(
                survey.getId(),
                allQuotas,
                syncQuotas.stream().map(SurveyQuota::getId).collect(Collectors.toList()),
                countResponse,
                (Long sId, List<Long> quotaIds, int page, int size) -> {

                    Map<Long, List<Long>> quotaIdsMap = new HashMap<>();

                    // 默认从第0页开始
                    Pageable pageable = PageRequest.of(page - 1, size, Sort.by("id").descending());
                    List<ResponseStatus> status = List.of(ResponseStatus.FINAL_SUBMIT, ResponseStatus.WAIT_AUDIT);
                    List<SurveyResponse> surveyResponses = responseRepository.findBySurveyIdAndStatusInAndCollectorMethod(survey.getId(), status, SurveyCollectorMethod.SURVEY_PLUS, pageable);
//                        List<SurveyQuota> quotaList = quotaRepository.findAllByIdIn(quotaIds);
                    log.info("syncQuota start page = {},size = {},time = {}", page, size, System.currentTimeMillis());
                    surveyResponses.forEach(surveyResponse -> {
                        try {
                            List<SurveyQuota> triggerQuotas = responseService.quotaTriggerResponse(syncQuotas, surveyResponse);
                            log.info("survey: {} response: {} trigger quotas: {}", survey.getId(), surveyResponse.getId(), triggerQuotas);

                            triggerQuotas.forEach(quota -> {
                                if (quotaIdsMap.containsKey(quota.getId())) {
                                    quotaIdsMap.get(quota.getId()).add(surveyResponse.getId());
                                } else {
                                    quotaIdsMap.put(quota.getId(), new ArrayList<>(Collections.singletonList(surveyResponse.getId())));
                                }
                            });

                        } catch (Throwable e) {
                            e.printStackTrace();
                        }

                    });
                    log.info("syncQuota end , time = {}", System.currentTimeMillis());
                    return quotaIdsMap;
                },
                (success, stat) -> {
                    if (success) {
                        List<SurveyQuota> surveyQuotas = surveyQuotaRepository.findBySidAndChannelType(survey.getId(), QuotaChannelType.SURVEY_PLUS);
                        //保存配额表达式的hashCode
                        surveyQuotas.forEach(quota -> {
                            quota.setExpressionHash(DigestUtils.sha256Hex(quota.getExpression()));
                        });
                        surveyQuotaRepository.saveAll(surveyQuotas);
                        //问卷配额状态 修改为正常
                        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
                        surveyRepository.save(survey);
                        log.info("survey: {} sync quota success", survey.getId());
                    } else {
                        log.error("survey: {} sync quota error", survey.getId());
                    }
                }
        );
    }

    /**
     * 投放 openid
     * 追访 clientId
     *
     * @param resetResponseDto
     * @return
     */

    public Boolean resetQuotaFullResponse(ResetResponseDto resetResponseDto) {
        List<SurveyResponse> responses = surveyResponseRepository.findAllById(resetResponseDto.getResponseIds());
        for (SurveyResponse response : responses) {
            try {
                if (response.getStatus() != ResponseStatus.QUOTA_FUll) {
                    continue;
                }
                response.setStatus(ResponseStatus.INIT);
                response.setFinishTime(null);
                response.setIsCompleted(false);
                response.setDurationSeconds(null);
                //openid只能填答一次限制
                if (StringUtils.isNotEmpty(response.getOpenid())) {
                    String openidKey = String.format(K_OPEN_ID, response.getSurveyId(), response.getChannelId());
                    stringRedisTemplate.opsForZSet().remove(openidKey, response.getOpenid());
                    //问卷完成记录，//公众号首页任务展示
                    surveyCompletedRecordRepository.deleteBySidAndOpenId(String.valueOf(response.getSurveyId()), response.getOpenid());
                    response.setOpenid(null);
                }else {
                    surveyCompletedRecordRepository.deleteBySidAndResponseId(String.valueOf(response.getSurveyId()), response.getId());
                }
                responseRepository.save(response);
            } catch (Exception e) {
                log.info("重置答卷：{}异常", response.getId());
                e.printStackTrace();
            }
        }
        return true;
    }
}

















