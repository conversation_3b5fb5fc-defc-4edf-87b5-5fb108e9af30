package org.befun.adminx.service;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.DownLoadType;
import org.befun.adminx.constant.survey.FormatType;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.survey.*;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.adminx.dto.user.UserStatisticsDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.survey.*;
import org.befun.adminx.repository.SurveyQuestionColumnRepository;
import org.befun.adminx.repository.SurveyQuestionItemRepository;
import org.befun.adminx.repository.SurveyResponseCellRepository;
import org.befun.adminx.repository.SurveyResponseRepository;
import org.befun.adminx.service.audit.AuditContent;
import org.befun.adminx.service.expression.ExpressionService;
import org.befun.adminx.utils.DateFormatter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResponseService extends CustomEmbeddedService<SurveyResponse, SurveyResponseDetailDto, SurveyResponseRepository> {

    @Autowired
    private SurveyResponseRepository repository;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    @Autowired
    ResponseService responseService;

    @Autowired
    private SurveyResponseCellRepository cellRepository;

    @Autowired
    CommunityUserService communityUserService;

    @Autowired
    QuestionService questionService;

    @Autowired
    SurveyQuestionItemRepository itemRepository;

    @Autowired
    SurveyQuestionColumnRepository columnRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private ExpressionService expressionService;


    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }

    /**
     * 下载时 过滤答卷状态
     */
    private static List<ResponseStatus> DownloadStatusList = List.of(
            ResponseStatus.FINAL_SUBMIT,
            ResponseStatus.INVALID,
            ResponseStatus.WAIT_AUDIT,
            ResponseStatus.AUDIT_FAIL
    );


    /**
     * 获取问卷下的所有答题数据
     *
     * @param survey
     * @return
     */
    public Optional<List<SurveyResponse>> getBySurvey(Survey survey, DownLoadType downLoadType) {
        if(downLoadType == DownLoadType.ALL) {
            return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(survey.getId());
        }else{
            return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(survey.getId(), SurveyCollectorMethod.SURVEY_PLUS, DownloadStatusList);
        }
    }

    /**
     * 获取问卷下的所有答题数据
     *
     * @param survey
     * @return
     */
    public Optional<List<SurveyResponse>> getBySurvey(Survey survey, Pageable pageable, DownLoadType downLoadType) {
        if(downLoadType == DownLoadType.ALL) {
            return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(survey.getId(), pageable);
        }else{
            return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(survey.getId(), SurveyCollectorMethod.SURVEY_PLUS, DownloadStatusList, pageable);
        }
    }

    /**
     * *获取问卷下所有的答卷id
     * @param surveyId
     * @param downLoadType
     * @return
     */
    public List<SimpleResponse> getSurveyResponseIds(Long surveyId, DownLoadType downLoadType) {
        if(downLoadType == DownLoadType.ALL) {
            return repository.findSimpledBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(surveyId);
        }else if(downLoadType == DownLoadType.QUOTA_FULL){
            return repository.findSimpledBySurveyIdAndStatusAndCollectorMethodAndIsCompletedIsTrueOrderByCreateTimeAsc(surveyId,ResponseStatus.QUOTA_FUll, SurveyCollectorMethod.SURVEY_PLUS);
        } else{
            return repository.findSimpleBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(surveyId, SurveyCollectorMethod.SURVEY_PLUS, DownloadStatusList);
        }
    }

    /**
     * 获取社区问卷下的所有答题数量
     *
     * @param surveyId
     * @return
     */
    public int countResponseBySurvey(Long surveyId) {
        return repository.countDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(surveyId, SurveyCollectorMethod.SURVEY_PLUS, DownloadStatusList);
    }

    /**
     * 获取问卷下的所有答题数量
     *
     * @param survey
     * @return
     */
    public int countAllResponseBySurvey(Survey survey) {
        return repository.countDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(survey.getId());
    }


    /**
     * 批量获取答卷下的答题数据
     *
     * @param survey
     * @param surveyResponseList
     * @return
     */
    public List<SurveyResponseCell> getCells(Survey survey, List<SurveyResponse> surveyResponseList) {
        return cellRepository.findAllBySurveyIdAndResponseIdIn(survey.getId(), surveyResponseList.stream().map(BaseEntity::getId).collect(Collectors.toList()));
    }

    /**
     * 更新审核状态
     * @param responseId
     * @param auditResultJsonDto
     */
    public void updateAuditStatus(Long responseId, ResponseStatus responseStatus, AuditResultJsonDto auditResultJsonDto) {
        SurveyResponse response = require(responseId);
        response.setAuditResult(JsonHelper.toJson(auditResultJsonDto));
        response.setStatus(responseStatus);
        repository.save(response);
    }

    /**
     * *更新survey_response 的openid
     * @param responseId
     * @param openid
     */
    public void updateOpenId(Long responseId, String openid) {
        SurveyResponse response = require(responseId);
        response.setOpenid(openid);
        repository.save(response);
    }

    /**
     * 获取第一条或者最后一条答卷数据
     * @param survey
     * @param fetchRequestDto
     * @return
     */
    public SurveyResponse getOneSurveyResponse(SurveyDto survey, SurveyResponseFetchRequestDto fetchRequestDto) {
        Sort sort = null;
        Optional<SurveyResponse> response = Optional.empty();
        switch (fetchRequestDto.getFetchType()){
            case MANUAL:
                response = repository.findById(fetchRequestDto.getResponseId());
                break;
            case FIRST:
                sort = Sort.by("id").ascending();
                response = repository.findFirstBySurveyIdAndIsCompletedIsTrueAndStatus(survey.getEntity().getId(),
                        ResponseStatus.FINAL_SUBMIT, sort);
                break;
            case LAST:
                sort = Sort.by("id").descending();
                response = repository.findFirstBySurveyIdAndIsCompletedIsTrueAndStatus(survey.getEntity().getId(),
                        ResponseStatus.FINAL_SUBMIT, sort);
                break;
            default:
                return null;
        }
        return response.orElse(null);
    }


    /**
     * 如果省市为未知 重新定位一下
     * @param response
     * @return
     */
    public SurveyResponse responseIpResolver(SurveyResponse response) {
        if(response != null &&  response.getIp() != null && (response.getProvince().equals("未知") || response.getCity().equals("未知"))) {
            SurveyTrackingDataDto trackingDataDto = trackingService.parseRequest(response.getIp());
            response.setProvince(trackingDataDto.getProvince());
            response.setCity(trackingDataDto.getCity());
            repository.save(response);
        }
        return response;
    }
    /**
     * 如果省市为未知 重新定位一下 new
     * @param auditContent
     * @return
     */
    public void responseIpResolverNew(AuditContent auditContent) {
        if(auditContent.getResponse() != null &&  auditContent.getResponse().getIp() != null && (auditContent.getResponse().getProvince().equals("未知") || auditContent.getResponse().getCity().equals("未知"))) {
            SurveyTrackingDataDto trackingDataDto = trackingService.parseRequest(auditContent.getResponse().getIp());
            auditContent.getResponse().setProvince(trackingDataDto.getProvince());
            auditContent.getResponse().setCity(trackingDataDto.getCity());
        }
    }

    /**
     * 获取答卷数据
     * @param response
     * @return
     */
    public LinkedHashMap<String, Object> getResponseContext(SurveyResponse response) {
        //如果ip定位为未知 重新百度定位下
        response = responseIpResolver(response);

        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
        //获取填答者社区用户信息
        CommunityUser communityUser = communityUserService.requireUser(response.getOpenid());
        //答卷基本信息
        LinkedHashMap<String, Object> responseMap =  converterResponse(response);
        //社区用户信息
        LinkedHashMap<String, Object> communityUserMap = converterCommunityUser(communityUser);
        //答题详情
        LinkedHashMap<String, Object> cellDataMap = converterCells(response);
        context.putAll(communityUserMap);
        context.putAll(cellDataMap);
        context.putAll(responseMap);
        return context;
    }
    /**
     * 获取答卷数据 new
     * @param auditContent
     * @return
     */
    public LinkedHashMap<String, Object> getResponseContextNew(AuditContent auditContent) {
        //如果ip定位为未知 重新百度定位下
        responseIpResolverNew(auditContent);

        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
        //答卷基本信息
        LinkedHashMap<String, Object> responseMap =  converterResponse(auditContent.getResponse());
        //社区用户信息
        LinkedHashMap<String, Object> communityUserMap = converterCommunityUser(auditContent.getUserInfo());
        //答题详情
        LinkedHashMap<String, Object> cellDataMap = converterCells(auditContent.getResponse());
        context.putAll(communityUserMap);
        context.putAll(cellDataMap);
        context.putAll(responseMap);
        return context;
    }

    /**
     * 转换答卷基本信息
     * @param surveyResponse
     * @return
     */
    public LinkedHashMap<String, Object> converterResponse(SurveyResponse surveyResponse) {
        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
        context.put("_duration",surveyResponse.getDurationSeconds());
        Long durationTotal = (surveyResponse.getFinishTime() != null && surveyResponse.getCreateTime() != null) ? (surveyResponse.getFinishTime().getTime() - surveyResponse.getCreateTime().getTime()) / 1000 : null;
        context.put("_durationTotal",durationTotal);
        context.put("_ip",surveyResponse.getIp());
        context.put("_ip_province",replaceProvince(surveyResponse.getProvince()));
        context.put("_ip_city",surveyResponse.getCity());
        return context;
    }

    /**
     * 替换ip定位中的 自治区和省
     * @param province
     * @return
     */
    private String replaceProvince(String province) {
        if(StringUtils.isEmpty(province)) return province;
        String _province = "";
        switch(province) {
            case "内蒙古":
            case "内蒙古自治区": _province = "内蒙古自治区";break;
            case "广西":
            case "广西壮族自治区": _province = "广西壮族自治区";break;
            case "宁夏":
            case "宁夏回族自治区": _province = "宁夏回族自治区";break;
            case "新疆":
            case "新疆维吾尔自治区": _province = "新疆维吾尔自治区";break;
            case "西藏":
            case "西藏自治区": _province = "西藏自治区";break;
            default:
                _province = province.replace("省","");
        }
        return _province;
    }

    /**
     * 转换答题者用户信息
     * @param communityUser
     * @return
     */
    public LinkedHashMap<String, Object> converterCommunityUser(CommunityUser communityUser) {
        String birthDay = null, province = null, city = null, gender = null, education = null, educationModified = null;
        Integer educationCode = null;
        if(communityUser != null && communityUser.getAdditional() != null){
            if(communityUser.getAdditional().getBirthday() != null){
                birthDay = DateFormatter.parseDateToString(communityUser.getAdditional().getBirthday().toString());
            }
            province = communityUser.getAdditional().getProvince();
            city     = communityUser.getAdditional().getCity();
            gender   = communityUser.getAdditional().getGender();
            education= communityUser.getAdditional().getEducation();
            educationCode = communityUser.getAdditional().educationConverter();
            if(communityUser.getAdditional().getEducationModified() != null){
                educationModified = DateFormatter.parseDateToString(communityUser.getAdditional().getEducationModified().toString());
            }
        }
        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
        context.put("_birthday",birthDay);
        context.put("_province",province);
        context.put("_city",city);
        context.put("_gender",gender);
        context.put("_education",education);
        context.put("_educationCode",educationCode);
        context.put("_educationModified",educationModified);
        return context;
    }

    /**
     * 转换答卷填答数据
     * @param surveyResponse
     * @return
     */
    public LinkedHashMap<String, Object> converterCells(SurveyResponse surveyResponse) {
        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
//        if(surveyResponse.getCells() == null ) return context;
        LinkedHashMap<String, Object> cellData = buildCellData(surveyResponse);
        context.putAll(cellData);
        return context;
    }

    /**
     * 构建答题时提交的数据
     *
     * @return
     */
    public LinkedHashMap<String, Object> buildCellData(SurveyResponse response) {
        LinkedHashMap<String, Object> cellData = new LinkedHashMap<>();

        if(response == null) return cellData;
        List<SurveyResponseCell> cellList = cellRepository.findAllBySurveyIdAndResponseId(response.getSurveyId(), response.getId());
        if(cellList == null || cellList.size() == 0) return cellData;
        cellList.forEach(
                surveyResponseCell -> {
                    SurveyQuestion q = questionService.requireQuestion(surveyResponseCell.getQuestionId());
                    if (q == null) return;
                    String code = q.getCode();
                    Object value = surveyResponseCell.getValue();
                    int index = 0;
                    Object target = new Object();

                    List<SurveyQuestionItem> itemList = itemRepository.findAllByQuestionOrderBySequence(q);
                    List<SurveyQuestionColumn> columnList = columnRepository.findAllByQuestionOrderBySequence(q);

                    if (q.getType() == QuestionType.BLANK) {
                        cellData.put(code,value);
                        return;
                    }

//                    if (!q.getItems().isEmpty()) {
                    if (itemList !=null && itemList.size() >0) {
//                        for (SurveyQuestionItem i : q.getItems()) {
                        for (SurveyQuestionItem i : itemList) {
                            switch (q.getType()){
                                case SINGLE_CHOICE:
                                case COMBOBOX:
                                    if(value.toString().equals(i.getValue())) {
                                        target = index;
                                    }
                                    index++;
                                    break;
                                case MULTIPLE_CHOICES:
                                    if(!(target instanceof List)) target = new ArrayList<>();
                                    if(((List<String>) value).contains(i.getValue())) {
                                        ((List<Boolean>) target).add(true);
                                    }else {
                                        ((List<Boolean>) target).add(false);
                                    }
                                    break;
                                case MATRIX_CHOICE:
                                case MATRIX_SCORE:
                                case MATRIX_SLIDER:
                                    List column = new ArrayList();
                                    String nameValue = Objects.toString(((LinkedHashMap) value).get(i.getValue()), "");
//                                    if(q.getColumns() != null && !q.getColumns().isEmpty()){
                                    if(columnList != null && columnList.size()>0){
//                                        for(SurveyQuestionColumn c: q.getColumns()){
                                        for(SurveyQuestionColumn c: columnList){
                                            if(c.getValue().equals(nameValue)) {
                                                column.add(true);
                                            }else {
                                                column.add(false);
                                            }
                                        }
                                        if(!(target instanceof List)) target = new ArrayList<>();
                                        ((List) target).add(column);
                                    }else {
                                        if(!(target instanceof ArrayList)) target = new ArrayList<>();
                                        if(StringUtils.isEmpty(nameValue)) ((ArrayList) target).add(null);
                                        else ((ArrayList) target).add(Double.parseDouble(nameValue));
                                    }
                                    break;
                                default:
                                    target = null;
                            }
                        }
                        if(target != null){
                            cellData.put(code,target);
                        }
                    }else {
                        switch (q.getType()) {
                            case LOCATION:
                                value = ((Map) value).getOrDefault("location", "").toString();break;
                            case  DATE:
                                value = DateFormatter.timeStampToDate(value.toString(), FormatType.DAY.toString());break;
                            default:
                        }
                        cellData.put(code,value);
                    }
                }
        );
        return cellData;
    }

    /**
     * 获取前一天的答题数据统计
     * @return
     */
    public List<SurveyResponseStatisticsDto> getSurveyStatistics(String startTime, String endTime) {
        if(StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) return null;
        String sql = String.format("select " +
                "openid,count(1) as total," +
                "sum(case when status = 1 then 1 else 0 end) as auditPass," +
                "sum(case when status = 2 then 1 else 0 end) as earlyCompleted," +
                "sum(case when status = 3 then 1 else 0 end) as invalid," +
                "sum(case when status = 4 then 1 else 0 end) as deleted," +
                "sum(case when status = 5 then 1 else 0 end) as waitAudit," +
                "sum(case when status = 6 then 1 else 0 end) as auditFail," +
                "sum(case when status = 7 then 1 else 0 end) as quotaFull," +
                "sum(case when status = 8 then 1 else 0 end) as auditError " +
                "from cem_platform.survey_response where connector_method = 5 and openid <> '' " +
                "and create_time >= '%s' and create_time <= '%s' group by openid;", startTime, endTime);
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SurveyResponseStatisticsDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 通过问卷id获取openid
     * @param surveyId
     * @return
     */
    public List<String> getOpenIdsBySurveyId(Long surveyId) {
        if(surveyId == 0l || surveyId == null) return List.of();
        List<SimpleResponse> responseList =  repository.findBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatus(surveyId,SurveyCollectorMethod.SURVEY_PLUS,ResponseStatus.FINAL_SUBMIT);
        return responseList.stream().map(x ->x.getOpenid()).collect(Collectors.toList());
    }

    /**
     * 通过问卷id和openId反查responseId
     * @param surveyId
     * @param users
     * @return
     */
    public List<CommunityUserSearchDto> getResponseIds(String surveyId, List<CommunityUserSearchDto> users) {
        if(StringUtils.isEmpty(surveyId) || users == null || users.size() == 0) return List.of();
        List<String> openIds = users.stream().map(CommunityUserSearchDto::getOpenid).collect(Collectors.toList());
        List<SurveyResponse> responseList = repository.findBySurveyIdAndOpenidIsInAndStatus(Long.parseLong(surveyId), openIds, ResponseStatus.FINAL_SUBMIT);
        if(responseList == null || responseList.size() == 0) return List.of();
        Map<String,Long> openidMap = responseList.stream().collect(Collectors.toMap(SurveyResponse::getOpenid,SurveyResponse::getId));
        users.forEach(user ->{
            Long responseId = openidMap.get(user.getOpenid());
            if( responseId != null) user.setResponseId(responseId.toString());
        });
        return users;
    }

    /**
     * 根据答卷状态获取答卷
     *
     * @param survey
     * @param status
     * @param pageable
     * @return
     */
    public List<SurveyResponse> getByStatus(Survey survey, ResponseStatus status, Pageable pageable) {
        return repository.findBySurveyIdAndStatus(survey.getId(), status, pageable);
    }
    /**
     * 根据问卷和答题状态获取答卷数据
     *
     * @param survey
     * @param status
     * @return
     */
    public Long numOfResponse(Survey survey, ResponseStatus status) {
        return repository.countBySurveyIdAndStatus(survey.getId(), status);
    }

    /**
     * 命中配额条件
     *
     * @param quotas
     * @param response
     * @return
     */
    public List<SurveyQuota> quotaTriggerResponse(List<SurveyQuota> quotas, SurveyResponse response) {
        List<SurveyQuota> quotaList = new ArrayList<>();

        quotas.forEach(quota -> {
            Boolean triggerResult = expressionService.triggerExpression(response.getId(), quota.getExpression(), null, false);
            log.info("quota: {} triggerResult: {}", quota.getId(), triggerResult);
            if (triggerResult) {
                quotaList.add(quota);
            }
        });

        return quotaList;
    }

    /**
     * 获取相似度的答卷
     * @param surveyId
     * @param content
     * @param createTime
     * @return
     */
    public List<SurveyResponseSimilarityDto> getSimilarityResponses(Long surveyId, String content, String createTime, Float percent) {
        if(surveyId == null || surveyId == 0 || StringUtils.isEmpty(content)) return null;
        String sql = String.format("select " +
                "r_id as responseId, " +
                "sequence, " +
                "MIN(create_time) as createTime, " +
                "MIN(ip) as ip, " +
                "GROUP_CONCAT(CONCAT_WS(':', q_code, i_val, s_val, j_val, t_val) SEPARATOR '^^') AS content, " +
                "cem_platform.diff_distance('%s',GROUP_CONCAT(CONCAT_WS(':', q_code, i_val, s_val, j_val, t_val) SEPARATOR '^^')) as diff " +
                "from " +
                "(select c.*, r.ip, r.sequence, q.code as q_code from cem_platform.survey_response_cell as c " +
                "left join cem_platform.survey_question as q on c.q_id = q.id " +
                "left join cem_platform.survey_response as r on r.id = c.r_id " +
                "where c.s_id = %s and r.status in (1,5) and r.create_time >= '%s' " +
                "order by c.id, q_id ) as t " +
                "GROUP BY r_id HAVING diff >= %.2f;", content, surveyId, createTime, percent);
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SurveyResponseSimilarityDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 拼接匹配相似度需要的content
     * @param surveyId
     * @param responseId
     * @return
     */
    public SurveyResponseSimilarityDto getSimilarityMatchContent(Long surveyId, Long responseId) {
        if(surveyId == null || responseId == null) return null;
        String sql = String.format("select " +
                "GROUP_CONCAT(CONCAT_WS(':', q_code, i_val, s_val, j_val, t_val) SEPARATOR '^^') AS content " +
                "from " +
                "(select c.*, q.code as q_code from cem_platform.survey_response_cell as c  " +
                "left join cem_platform.survey_question as q on c.q_id = q.id " +
                "where c.s_id = %s and c.r_id = %s " +
                "order by c.id, c.q_id) as t;", surveyId, responseId);
        try {
            return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(SurveyResponseSimilarityDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 标记修改相似度高的
     * @param similarityDtoList
     * @param similarityNo
     */
    public void updateSimilaritySurveyResponse(List<SurveyResponseSimilarityDto> similarityDtoList, Long similarityNo) {
        if(similarityDtoList == null || similarityDtoList.isEmpty() || similarityNo == null) return;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("UPDATE cem_platform.survey_response SET similarity_check = 0, similarity_no = "+ similarityNo + ", similarity_percent = CASE ");

        for (SurveyResponseSimilarityDto dto : similarityDtoList) {
            sqlBuilder.append("WHEN id = ").append(dto.getResponseId()).append(" THEN ").append(dto.getDiff()).append(" ");
        }

        sqlBuilder.append("ELSE similarity_percent END WHERE id IN (");

        // 添加所有 responseId
        for (SurveyResponseSimilarityDto dto : similarityDtoList) {
            sqlBuilder.append(dto.getResponseId()).append(", ");
        }

        // 去除末尾多余的逗号和空格
        sqlBuilder.setLength(sqlBuilder.length() - 2);

        sqlBuilder.append(")");
        log.info("update similarity surveyResponse : {}",sqlBuilder.toString());
        try {
            jdbcTemplate.update(sqlBuilder.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}