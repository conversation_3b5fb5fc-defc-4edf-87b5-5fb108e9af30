package org.befun.adminx.converter;


import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/13 14:29
 */
public class SexConverter implements AttributeConverter<String, Integer> {
    public SexConverter() {
    }

    public Integer convertToDatabaseColumn(String gender) {
        if(gender == null){
            return null;
        }
        Integer sex = 0;
        switch (gender){
            case "男":
            case "1":
                sex = 1;break;
            case "女":
            case "2":
                sex = 2;break;
            default:
        }
        return sex;
    }

    public String convertToEntityAttribute(Integer gender) {
        String sex = "";
        if(gender == null) return sex;
        switch (gender){
            case 1:
                sex = "男";break;
            case 2:
                sex = "女";break;
            default:
        }
        return sex;
    }

    public String convertToString(String gender) {
        String sex = "";
        switch (gender){
            case "男":
            case "1":
                sex = "男";break;
            case "女":
            case "2":
                sex = "女";break;
            default:
        }
        return sex;
    }
}
