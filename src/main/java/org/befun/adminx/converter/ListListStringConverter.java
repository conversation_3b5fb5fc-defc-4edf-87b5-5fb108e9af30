package org.befun.adminx.converter;

import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import java.io.IOException;
import java.util.List;
import javax.persistence.AttributeConverter;
import org.befun.core.converter.ListConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ListListStringConverter implements AttributeConverter<List<List<String>>, String> {
    private static final Logger log = LoggerFactory.getLogger(ListConverter.class);

    public ListListStringConverter() {
    }

    @Override
    public String convertToDatabaseColumn(List<List<String>> nestedList) {
        String jsonString = null;

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            jsonString = objectMapper.writeValueAsString(nestedList);
        } catch (JsonProcessingException var4) {
        }

        return jsonString;
    }

    @Override
    public List<List<String>> convertToEntityAttribute(String jsonString) {
        List<List<String>> nestedList = null;

        try {
            ObjectMapper objectMapper = JsonMapper.builder().enable(new JsonReadFeature[]{JsonReadFeature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER}).build();
            objectMapper.configure(Feature.ALLOW_SINGLE_QUOTES, true);
            nestedList = objectMapper.readValue(jsonString,
                    objectMapper.getTypeFactory().constructCollectionType(List.class,
                            objectMapper.getTypeFactory().constructCollectionType(List.class, String.class)));
        } catch (IllegalArgumentException | IOException var4) {
        }

        return nestedList;
    }
}
