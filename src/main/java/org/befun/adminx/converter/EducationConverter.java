package org.befun.adminx.converter;


import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/13 14:29
 */
public class EducationConverter implements AttributeConverter<String, Integer> {
    public EducationConverter() {
    }

    public Integer convertToDatabaseColumn(String education) {
        if(StringUtils.isEmpty(education)){
            return null;
        }
        Integer educationCode = 0;
        switch (education){
            case "小学及以下":
            case "1":
                educationCode = 1;break;
            case "初中":
            case "2":
                educationCode = 2;break;
            case "高中/中专/技校/职高":
            case "3":
                educationCode = 3;break;
            case "大专":
            case "4":
                educationCode = 4;break;
            case "大学本科":
            case "5":
                educationCode = 5;break;
            case "硕士及以上":
            case "6":
                educationCode = 6;break;
            default:
        }
        return educationCode;
    }

    public String convertToEntityAttribute(Integer educationCode) {
        String education = "";
        if(educationCode == null) return education;
        switch (educationCode) {
            case 1:
                education = "小学及以下";break;
            case 2:
                education = "初中";break;
            case 3:
                education = "高中/中专/技校/职高";break;
            case 4:
                education = "大专";break;
            case 5:
                education = "大学本科";break;
            case 6:
                education = "硕士及以上";break;
            default:
        }
        return education;
    }

    public String convertToString(String education) {
        String educationCode = "";
        switch (education){
            case "小学及以下":
            case "1":
                educationCode = "小学及以下";break;
            case "初中":
            case "2":
                educationCode = "初中";break;
            case "高中/中专/技校/职高":
            case "3":
                educationCode = "高中/中专/技校/职高";break;
            case "大专":
            case "4":
                educationCode = "大专";break;
            case "大学本科":
            case "5":
                educationCode = "大学本科";break;
            case "硕士及以上":
            case "6":
                educationCode = "硕士及以上";break;
            default:
        }
        return educationCode;
    }
}
