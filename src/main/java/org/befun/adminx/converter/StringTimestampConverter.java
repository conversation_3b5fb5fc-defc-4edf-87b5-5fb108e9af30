package org.befun.adminx.converter;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.FormatType;
import org.befun.adminx.utils.DateFormatter;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/8 14:33
 */
public class StringTimestampConverter implements AttributeConverter<String, Long> {

    public StringTimestampConverter (){

    }

    @Override
    public Long convertToDatabaseColumn(String attribute) {
        if(StringUtils.isEmpty(attribute)) return null;
        if(StringUtils.isNumeric(attribute))
            return Long.parseLong(attribute);
        else
            return DateFormatter.dateToTimeStamp(attribute);
    }

    @Override
    public String convertToEntityAttribute(Long dbData) {
        if(dbData == null || dbData == 0l) return null;
        int length = String.valueOf(dbData).length();
        String dbDataString;
        if(length == 10) {
            dbDataString = dbData + "000";
        } else if (length == 11) {
            //TODO 兼容数据库的关注时间11位的情况
            dbDataString = dbData + "00";
        } else {
            dbDataString = String.valueOf(dbData);
        }
        return DateFormatter.timeStampToDate(dbDataString, FormatType.DAY.toString());
    }
}
