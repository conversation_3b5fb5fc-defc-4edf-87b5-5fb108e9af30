package org.befun.adminx.converter;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.utils.CityAndCodeUtil;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/13 15:37
 */
public class PostalCodeConverter implements AttributeConverter<String, Integer> {
    public PostalCodeConverter() {
    }

    public Integer convertToDatabaseColumn(String address) {
        if(StringUtils.isNotEmpty(address) && StringUtils.isNumeric(address)) {
            return Integer.parseInt(address);
        }
        return CityAndCodeUtil.getCodeByCity(address);
    }

    public String convertToEntityAttribute(Integer postalCode) {
        if(postalCode == null) return "未知";
        return CityAndCodeUtil.getCityByCode(postalCode.toString());
    }
}