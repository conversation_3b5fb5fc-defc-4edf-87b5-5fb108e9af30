package org.befun.adminx.converter;

import org.befun.adminx.dto.user.VersionDto;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/10 18:19
 */
public class VersionConverter implements AttributeConverter<String, String> {
    public VersionConverter (){

    }

    @Override
    public String convertToDatabaseColumn(String attribute) {
        return null;
    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        String versionName = "";
        if(dbData == null ) return versionName;
        VersionDto version = JsonHelper.toObject(dbData,VersionDto.class);
        switch (version.getCemVersion()) {
            case "empty":
                versionName = "不开放使用";break;
            case "free":
                versionName = "免费版";break;
            case "base":
                versionName = "基础版";break;
            case "update":
                versionName = "团队专业版";break;
            case "profession":
                versionName = "企业旗舰版";break;
            default:
        }
        return versionName;
    }
}
