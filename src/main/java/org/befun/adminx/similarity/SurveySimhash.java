package org.befun.adminx.similarity;
import lombok.Getter;
import org.apache.commons.collections.MapUtils;
import org.befun.adminx.constant.similarity.FeatureDataType;

import java.math.BigInteger;
import java.util.*;


/**
 * simhash通用类
 *
 * <AUTHOR>
 */
public class SurveySimhash {
    public static final Map<FeatureDataType, String> MAP_SPLITTERS = Map.of(
        FeatureDataType.BIT,":",
        FeatureDataType.INTEGER, "%",
        FeatureDataType.TIMESTAMP, "@",
        FeatureDataType.STRING, "#"
    );

    public static Map<String, FeatureDataType> INVERT_MAP_SPLITTERS;
    public static String SPLITTERS_PATTERN;

    static {
        INVERT_MAP_SPLITTERS = MapUtils.invertMap(MAP_SPLITTERS);
        SPLITTERS_PATTERN = "[" + String.join("|", MAP_SPLITTERS.values()) + "]";
    }

    private Map<String, Object> features;
    private List<FeatureSchema> featureSchema = null;

    @Getter
    private BigInteger intFingerprint;

    @Getter
    private String strFingerprint;

    private int hashbits = 256;
    private int maxbits = 8;

    public SurveySimhash(Map<String, Object> features, List<FeatureSchema> schema) {
        this(features, schema, 256, 8);
    }

    public SurveySimhash(Map<String, Object> features, List<FeatureSchema> schema, int hashbits, int maxbits) {
        this.features = features;
        this.featureSchema = schema;
        this.hashbits = hashbits;
        this.maxbits = maxbits;
        this.intFingerprint = this.simHash();
    }

    /**
     * init from fingerprint
     * @param fingerprint
     * @param hashbits
     * @param maxbits
     */
    public SurveySimhash(String fingerprint, int hashbits, int maxbits) {
        this.strFingerprint = fingerprint;
        this.hashbits = hashbits;
        this.maxbits = maxbits;
    }

    public BigInteger simHash()  {
        // 定义特征向量/数组
        BigInteger fingerprint = hash(features, featureSchema, hashbits, maxbits);
        return fingerprint;
    }

    public BigInteger hash(Map<String, Object> features, List<FeatureSchema> featureSchema, int hashbits, int max) {
        // encoding
        BigInteger result = BigInteger.ZERO.setBit(hashbits - 1);
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (FeatureSchema schema : featureSchema) {
            Integer value = 0;
            String key = schema.getName();
            Object entry_value = features.get(key) == null ? 0 : features.get(key);
            BigInteger m_value = null;
            int left = hashbits - i;
            int len = 1;
            String splitter = MAP_SPLITTERS.get(schema.getDataType());
            if (entry_value != null) {
                switch (schema.getDataType()) {
                    case BIT:
                        m_value = BigInteger.valueOf(entry_value.equals(1) ? 1 : 0).shiftLeft(left);
                        break;
                    case INTEGER:
                        value = (Integer) entry_value;
                        len = max;
                        m_value = BigInteger.valueOf(Math.min(max, value)).shiftLeft(left);
                        break;
                    case TIMESTAMP:
                        Long timestamp = (Long) entry_value;
                        Integer tmp = (timestamp.intValue() / 60);
                        len = 32;
                        m_value = BigInteger.valueOf(tmp).shiftLeft(left);
                        break;
                    default:
                        throw new RuntimeException("Not Support " + schema.getDataType());
                }
            }
            result = result.xor(m_value);
            sb.append(String.format("%s%s%s ", key, splitter, entry_value.toString()));
            i += len;
        }
        strFingerprint = sb.toString().trim();
        return result;
    }

    public int hammingDistance(SurveySimhash other) {
        BigInteger x = this.intFingerprint.xor(other.intFingerprint);
        return x.bitCount();
    }

    /**
     * 原始距离 主要特点是位图，每一个feature只要不一样就是差距+1
     * @param other
     * @return
     */
    public int rawDistance(SurveySimhash other) {
        HashSet<String> sourceItems = new HashSet<String>(Arrays.asList(this.strFingerprint.split(" ")));
        HashSet<String> targetItems = new HashSet<String>(Arrays.asList(other.getStrFingerprint().split(" ")));

        Set<String> union = new HashSet<>(sourceItems);
        union.addAll(targetItems);

        Set<String> intersection = new HashSet<>(sourceItems);
        intersection.retainAll(targetItems);
        union.removeAll(intersection);

        return union.size();
    }
}