package org.befun.adminx.constant;

public enum XmVersions {
    empty("不开放使用", AppVersionPermissions.EMPTY),
    // 免费版
    free("免费版", AppVersionPermissions.SUPER_ADMIN_FREE),
    //   基础版
    base("基础版", AppVersionPermissions.SUPER_ADMIN_BASE),
    // 升级版
    update("团队专业版", AppVersionPermissions.SUPER_ADMIN_UPDATE),
    // 专业版
    profession("企业旗舰版", AppVersionPermissions.SUPER_ADMIN_PROFESSION);

    public final String text;
    public final AppVersionPermissions permissions;

    XmVersions(String text, AppVersionPermissions permissions) {
        this.text = text;
        this.permissions = permissions;
    }

}
