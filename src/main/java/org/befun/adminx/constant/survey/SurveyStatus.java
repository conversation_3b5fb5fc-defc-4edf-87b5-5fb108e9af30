package org.befun.adminx.constant.survey;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/7 6:34 下午
 */
@Getter
public enum SurveyStatus {

    STOPPED("停用中"),
    COLLECTING("启用中"),
    AUDITING("审核中"),
    APPROVAL("已通过"),
    REJECTED("已驳回"),
    CONTENT_AUDITING("内容审核中"),
    CONTENT_APPROVAL("内容审核已通过"),
    CONTENT_REJECTED("内容审核已驳回"),
    DISABLED("已禁用"),
    ;

    private final String text;

    SurveyStatus(String text) {
        this.text = text;
    }

    public static List<Integer> parseByNames(String names) {
        List<Integer> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(names)) {
            Map<String, Integer> map = Arrays.stream(SurveyStatus.values()).collect(Collectors.toMap(Enum::name, Enum::ordinal));
            Arrays.stream(names.split(",")).forEach(i -> {
                Optional.ofNullable(map.get(i)).ifPresent(list::add);
            });
        }
        return list;
    }

}
