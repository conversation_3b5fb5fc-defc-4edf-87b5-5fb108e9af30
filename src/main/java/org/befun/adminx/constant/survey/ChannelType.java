package org.befun.adminx.constant.survey;

import lombok.Getter;
/**
 * 保证问卷渠道的所有类型都已经包含在客户互动的渠道中
 */
@Getter
public enum ChannelType {
    COMMON("分享链接"),
    SHORT_LINK("短链接"),
    PHONE_MSG("手机短信"),
    WECHAT_SERVICE("微信服务号"),
    INJECT_WEB("网页嵌入"),
    SURVEY_PLUS("调研家社区"),
    SCENE_INTERACTION("场景互动"),
    MP("小程序嵌入"),
    APP("App嵌入"),
    EMAIL("邮件")
    ;

    private final String text;

    ChannelType(String text) {
        this.text = text;
    }


}
