package org.befun.adminx.constant.survey;

import lombok.Getter;

@Getter
public enum FormatType {
    //地区题的格式类型
    PROVINCE("省"),
    CITY("省-市"),
    COUNTY("省-市-区"),
    ADDRESS("省-市-区-详细地址"),

    //日期题的格式类型
    YEAR("yyyy"),
    MONTH("yyyy-MM"),
    DAY("yyyy-MM-dd"),
    HOUR("yyyy-MM-dd hh"),
    MINUTE("yyyy-MM-dd hh:mm"),

    //文件上传的格式
    IMG("图片"),
    OTHER("其他格式"),

    //评价题数量
    THREE("3个"),
    FIVE("5个"),
    TWO("2个"),

    //联合实验题 组合样式
    TABLE("表格"),
    CARD("卡片"),
    TEXT("文字");
    private final String format;

    FormatType(String format) {
        this.format = format;
    }

}
