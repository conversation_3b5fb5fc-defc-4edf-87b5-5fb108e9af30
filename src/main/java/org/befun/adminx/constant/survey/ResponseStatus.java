package org.befun.adminx.constant.survey;

import lombok.Getter;

@Getter
public enum ResponseStatus {
    INIT("未提交"),
    FINAL_SUBMIT("审核通过"),
    EARLY_COMPLETED("提前结束"),
    INVALID("无效"),
    DELETED("删除"),
    WAIT_AUDIT("待审核"),
    AUDIT_FAIL("审核不通过"),
    QUOTA_FUll("配额已满"),
    AUDIT_ERROR("审核异常")
    ;

    private final String text;

    ResponseStatus(String text) {
        this.text = text;
    }
}
