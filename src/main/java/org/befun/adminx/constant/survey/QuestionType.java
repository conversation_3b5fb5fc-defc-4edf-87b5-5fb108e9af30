package org.befun.adminx.constant.survey;

import lombok.Getter;

@Getter
public enum QuestionType {
    EMPTY("空题型"),
    SEPARATOR("分割符"),
    TEXT("文本题"),
    SINGLE_CHOICE("单选题"),
    MULTIPLE_CHOICES("多选题"),
    MARK("备注题"),
    SCORE("量表题"),
    NUMBER("数字题"),
    MOBILE("手机"),
    EMAIL("邮箱"),
    MATRIX("矩阵题"),
    MATRIX_SCORE("矩阵量表题"),
    AREA("地区题"),
    DATE("日期题"),
    MATRIX_CHOICE("矩阵单选题"),    
    DROP_DOWN("逐级下拉题"),
    MATRIX_SLIDER("滑动条"),
    FILE("文件上传"),
    MEDIA("多媒体"),
    EVALUATION("评价题"),
    ORGANIZE("组织架构题"),
    EXPERIMENT("联合实验题"),
    GROUP("题组"),
    RANKING("排序题"),
    COMBOBOX("下拉框"),
    SCORE_EVALUATION("打分-评价题"),
    LOCATION("地理位置题"),
    NPS("NPS题"),
    BLANK("横向填空题"),
    MULTIPLE_BLANK("多项填空题"),
    SIGNATURE("签名题"),
    ;

    final String text;
    QuestionType(String text){
        this.text = text;

    }
}
