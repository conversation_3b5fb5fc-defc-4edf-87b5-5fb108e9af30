package org.befun.adminx.constant;

import lombok.Getter;

@Getter
public enum ScoreType {
    R("R"),//回答问卷奖励
    U("U"),//等级升级奖励
    A("A"),//完善个人账号信息奖励
    M("M"),//积分兑换码兑换
    F("F"),//冻结积分
    F_P("F-P"),//冻结积分审核通过
    F_N("F-N"),//冻结积分审核不通过
    I_V("I-V"),//邀请的用户问卷通过审核的奖励(默认10%)
    G_V("G-V"),//网格催答的用户问卷通过审核的奖励(默认10%)
    G_F("G-F"),//网格催答的用户问卷未通过审核
    I_A("I-A"),//转介绍奖励（首次）
    D_C("D-C"),//drawCash 积分提现-成功
    D_F("D-F"),//drawCash 积分提现-失败
    D_H("D-H"),//handDrawCash 后台提现积分-手动
    D_A("D-A"),//handDrawCash 后台增加积分-手动
    T_A("T-A"),//转账提现成功
    T_F("T-F")//转账提现失败
    ;

    private final String text;

    ScoreType(String text) {
        this.text = text;
    }
}
