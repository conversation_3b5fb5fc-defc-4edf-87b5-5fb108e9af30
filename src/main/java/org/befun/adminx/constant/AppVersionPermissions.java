package org.befun.adminx.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static org.befun.adminx.constant.XmPermissions.*;


/**
 * <AUTHOR>
 */

@Getter
public enum AppVersionPermissions {
    EMPTY(new ArrayList<>()),
    SUPER_ADMIN_FREE(AppVersion.FREE),
    SUPER_ADMIN_BASE(AppVersion.BASE),
    SUPER_ADMIN_UPDATE(AppVersion.UPDATE),
    SUPER_ADMIN_PROFESSION(AppVersion.PROFESSION),
    COSTUMER_MANAGER_BASE(new ArrayList<>()),
    COSTUMER_MANAGER_UPDATE(new ArrayList<>()),
    COSTUMER_MANAGER_PROFESSION(new ArrayList<>()),
    MEMBER(List.of(
            CUSTOMER_LIFE_VIEW,
            TOUCH_MANAGE_SURVEY_VIEW,
            EVENTS_EVENT_WARNING_VIEW,
            EVENTS_EVENT_ACTION_VIEW,
            CUSTOMER_CENTRE_VIEW
    ));

    private final AppVersion version;
    private final List<XmPermissions> permissions;

    AppVersionPermissions(List<XmPermissions> permissions) {
        this.version = AppVersion.EMPTY;
        this.permissions = permissions;
    }

    AppVersionPermissions(AppVersion version) {
        this.version = version;
        this.permissions = new ArrayList<>();
    }

    public List<String> mapToString() {
        if (permissions.isEmpty()) {
            return XmPermissions.permissions(version).stream().map(XmPermissions::getPath).collect(Collectors.toList());
        }
        return permissions.stream().map(XmPermissions::getPath).collect(Collectors.toList());
    }
}
