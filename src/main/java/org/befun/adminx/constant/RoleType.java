package org.befun.adminx.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum RoleType {
    // editable: 0 不可以 1 可以编辑删除 2 只能编辑
    SUPER_ADMIN(1, "超级管理员", "系统默认角色，拥有所有权限，不可更改权限范围", "cem", 0),
    COSTUMER_MANAGER(2, "客户管理", "客户中心管理员，拥有查看和编辑层级所有客户权限", "cem", 0),
    MEMBER(3, "成员", "邀请子账户时的默认角色", "cem", 2),
    OTHER(4, "", "", "cem", 1), // 企业用户自建的角色
    ;

    private final int type;
    private final String name;
    private final String description;
    private final String platform;
    private final int editable;

    RoleType(int type, String name, String description, String platform, int editable) {
        this.type = type;
        this.name = name;
        this.description = description;
        this.platform = platform;
        this.editable = editable;
    }

}
