package org.befun.adminx.constant;

import lombok.Getter;

@Getter
public enum RechargeType {

    recharge_wechat(BillType.wechat, true, true, true),
    recharge_alipay(BillType.alipay, true, true, true),
    recharge_red_packet(BillType.platform, false, false, false),
    recharge_platform(BillType.platform, false, false, false),
    ;

    private final BillType billType;
    private final boolean checkMinimumAmount;
    private final boolean enableMock;
    private final boolean enableExternalRecharge; // 支持外部充值的方式

    RechargeType(BillType billType, boolean checkMinimumAmount, boolean enableMock, boolean enableExternalRecharge) {
        this.billType = billType;
        this.checkMinimumAmount = checkMinimumAmount;
        this.enableMock = enableMock;
        this.enableExternalRecharge = enableExternalRecharge;
    }
}
