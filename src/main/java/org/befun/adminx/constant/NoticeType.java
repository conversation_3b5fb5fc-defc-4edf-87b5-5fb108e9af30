package org.befun.adminx.constant;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/3/14 16:55
 */
@Getter
public enum NoticeType {
    AGREE_PROTOCOL("同意协议"),
    GET_AWARD("领取升级奖励");
    private final String text;

    NoticeType(String text) {
        this.text = text;
    }

    public static Set<NoticeType> parse(String value) {
        if(StringUtils.isEmpty(value)) {
            return new HashSet<>();
        }

        List<String> valueString  =  Arrays.asList(value.split(","));

        return Arrays.stream(values()).filter(v->valueString.contains(v.name())).collect(Collectors.toSet());
    }

    public static String format(Set<NoticeType> value) {
        if(CollectionUtils.isEmpty(value)) {;
            return null;
        }
        return value.stream().map(NoticeType::name).collect(Collectors.joining(","));
    }
}
