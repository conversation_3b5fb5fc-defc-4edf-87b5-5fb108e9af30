//package org.befun.adminx.kafka;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.adminx.constant.survey.SurveyCollectorMethod;
//import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
//import org.befun.adminx.service.DeliveryService;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//@ConditionalOnProperty(name = "community.enable-kafka", havingValue = "true")
//public class SurveyResponseConsumer {
//
//    @Autowired
//    private DeliveryService deliveryService;
//
//    @KafkaListener(topics = "${community.event.survey-response-topic:survey_response}", groupId = "${community.event.default-group}")
//    public void onSurveyResponse(String message) {
//        SurveyResponseMessageDto surveyResponseMessage = JsonHelper.toObject(message, SurveyResponseMessageDto.class);
//
//        if(surveyResponseMessage == null || StringUtils.isEmpty(surveyResponseMessage.getSurveyId().toString()) || surveyResponseMessage.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS) {
//            return;
//        }
//        log.info("start consumer survey_response");
//        log.info("topic survey_response message: {}", message);
//        try {
//            deliveryService.surveyResponseConsumer(surveyResponseMessage);
//        } catch (Exception ex) {
//            log.warn("monitor survey process message error ", ex);
//        }
//        return;
//    }
//
//}
