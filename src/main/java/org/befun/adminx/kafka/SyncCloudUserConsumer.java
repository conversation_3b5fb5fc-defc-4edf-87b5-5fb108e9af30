//package org.befun.adminx.kafka;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.befun.adminx.dto.sync.SyncCloudUserMessageDto;
//import org.befun.adminx.service.CloudUserService;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//@ConditionalOnProperty(name = "community.enable-kafka", havingValue = "true")
//public class SyncCloudUserConsumer {
//
//    @Autowired
//    private CloudUserService cloudUserService;
//
//    @KafkaListener(topics = "${community.event.cloud-user-update-topic:sync-cloud}", groupId = "${community.event.default-group}")
//    public void syncCloudUser(String message) {
//        log.info("topic sync-cloud message: {}", message);
//
//        try {
//            SyncCloudUserMessageDto messageDto = new ObjectMapper().readValue(message,SyncCloudUserMessageDto.class);
//
//            if(messageDto == null) {
//                return;
//            }
//            cloudUserService.setCloudUser(messageDto);
//        } catch (Exception ex) {
//            log.warn("monitor survey process message error {}", ex.getMessage());
//        }
//    }
//
//}
