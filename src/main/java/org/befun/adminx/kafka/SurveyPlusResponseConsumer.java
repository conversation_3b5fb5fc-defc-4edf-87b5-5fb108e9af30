//package org.befun.adminx.kafka;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.adminx.dto.survey.SurveyPlusResponseMessageDto;
//import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
//import org.befun.adminx.service.DeliveryService;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//@ConditionalOnProperty(name = "community.enable-kafka", havingValue = "true")
//public class SurveyPlusResponseConsumer {
//
//    @Autowired
//    private DeliveryService deliveryService;
//
//    @KafkaListener(topics = "${community.event.survey-plus-response-topic:queuing-survey-anwser}", groupId = "${community.event.default-group}")
//    public void onSurveyResponse(String message) {
//        log.info("topic queuing-survey-anwser message: {}", message);
//        SurveyPlusResponseMessageDto surveyPlusResponseMessageDto = JsonHelper.toObject(message, SurveyPlusResponseMessageDto.class);
//
//        if(surveyPlusResponseMessageDto == null) {
//            return;
//        }
//
//        try {
//            deliveryService.surveyPlusResponseConsumer(surveyPlusResponseMessageDto);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            log.warn("monitor survey process message error {}", ex.getMessage());
//        }
//    }
//
//}
