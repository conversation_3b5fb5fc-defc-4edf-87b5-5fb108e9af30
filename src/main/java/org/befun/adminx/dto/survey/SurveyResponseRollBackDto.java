package org.befun.adminx.dto.survey;

import lombok.*;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SurveyResponseRollBackDto extends BaseDTO {

    private Long surveyId;
    private Long responseId;
    private ResponseStatus status = ResponseStatus.AUDIT_FAIL;
}
