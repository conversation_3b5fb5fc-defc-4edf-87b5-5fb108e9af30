package org.befun.adminx.dto.survey;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class QuestionDropDownDto {
    private List<String> props = new ArrayList<>();
    private List datas = new ArrayList<>();
}
