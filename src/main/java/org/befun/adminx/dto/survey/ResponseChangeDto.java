package org.befun.adminx.dto.survey;

import org.befun.adminx.constant.survey.ResponseStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;


/**
 * 答卷状态修改通知到kafka
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class ResponseChangeDto extends BaseDTO {

    private Long surveyId;
    private Long responseId;
    private ResponseStatus status;
}
