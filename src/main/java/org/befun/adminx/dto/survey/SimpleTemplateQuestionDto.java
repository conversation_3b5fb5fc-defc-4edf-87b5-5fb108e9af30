package org.befun.adminx.dto.survey;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/5/10 15:57
 */
@Value
@Setter
@Getter
public class SimpleTemplateQuestionDto {
    @JsonView(ResourceViews.Basic.class)
    private Long id;
    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private Long groupId;
}
