package org.befun.adminx.dto.survey;

import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AbstractSurvey extends BaseDTO {
    private String name;
    private String title;
    private String id;
    private String url;
    private Boolean status;
    private List<AbstractSurveyChannel> channels = new ArrayList<>();
}
