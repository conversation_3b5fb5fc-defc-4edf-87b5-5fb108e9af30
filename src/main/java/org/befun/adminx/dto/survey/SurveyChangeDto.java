package org.befun.adminx.dto.survey;

import org.befun.adminx.constant.survey.SurveyChangeStatus;
import org.befun.adminx.constant.survey.SurveyChangeType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.context.TenantContext;


/**
 * 问卷状态修改通知到kafka
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class SurveyChangeDto extends BaseDTO {

    private Long userId = TenantContext.getCurrentUserId();
    private Long orgId = TenantContext.getCurrentTenant();
    private Long surveyId;
    private Long entityId;
    private String entityName;
    private SurveyChangeType entityType;
    private SurveyChangeStatus status;
}
