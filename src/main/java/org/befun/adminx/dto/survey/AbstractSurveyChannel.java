package org.befun.adminx.dto.survey;

import lombok.*;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AbstractSurveyChannel extends BaseDTO {
    private String name;
    private Long id;
    private String url;

    public AbstractSurveyChannel(SurveyChannel channel) {
        this.name = channel.getName();
        this.id = channel.getId();
        this.url = String.format("/lite/{}", id);
    }
}
