package org.befun.adminx.dto.survey;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.befun.core.dto.BaseDTO;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.JoinColumn;
import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SurveyPlusResponseMessageDto extends BaseDTO {
    @JsonAlias("id")
    private Long responseId;

    @JsonAlias("sid")
    private String surveyId;

    private String openid;

    private String country;
    private String province;
    private String city;

    @JsonAlias("survey_name")
    private String surveyName;

    @JsonAlias("collector_id")
    private Long channelId;

    @JsonAlias("is_early_end")
    private String isEarlyEnd = "N";



}
