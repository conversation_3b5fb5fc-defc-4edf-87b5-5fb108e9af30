package org.befun.adminx.dto.survey;

import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/1/31 16:17
 */
@Getter
@Setter
@Builder
public class SurveySimpleDto {
    private String surveyId;
    private String surveyTitle;
    private String nickName;
    private List<Long> cuidList = new ArrayList<>();

    public SurveySimpleDto(String surveyId, String title, String nickName) {
        this.surveyId = surveyId;
        this.surveyTitle = title;
        this.nickName = StringUtils.isNotEmpty(nickName) ? nickName : "*****";
    }

    public SurveySimpleDto(String surveyId, String title, String nickName, List<Long> cuidList) {
        this.surveyId = surveyId;
        this.surveyTitle = title;
        this.nickName = null;
        this.cuidList = cuidList;
    }

    public SurveySimpleDto() {}
}
