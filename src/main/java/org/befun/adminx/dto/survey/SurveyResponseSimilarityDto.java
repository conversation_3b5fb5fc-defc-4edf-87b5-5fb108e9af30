package org.befun.adminx.dto.survey;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseSimilarityDto {

    private Long responseId;
    private Date createTime;
    private String ip;
    private Long sequence;
    private String content;
    private Float diff;
}
