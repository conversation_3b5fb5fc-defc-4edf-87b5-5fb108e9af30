package org.befun.adminx.dto.survey;

import org.befun.adminx.constant.survey.FormatType;
import org.befun.adminx.constant.survey.QuestionType;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.befun.adminx.utils.RegularExpressionUtils.replaceHtml;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SurveyResponseDataDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    private Object comment;
    @JsonView(ResourceViews.Basic.class)
    private String code;
    @JsonView(ResourceViews.Basic.class)
    private Long questionId;
    @JsonView(ResourceViews.Basic.class)
    private QuestionType type;
    private Map<String, QuestionsItemsDto> questionsItems;
    @JsonView(ResourceViews.Basic.class)
    private String inapplicableLabel;
    @JsonView(ResourceViews.Basic.class)
    private FormatType formatType;
    @JsonView(ResourceViews.Basic.class)

    public void convertText(){
        setTitle(replaceHtml(title));

        Iterator itemsIterator = questionsItems.values().iterator();
        if(itemsIterator.hasNext()) {
            ObjectMapper objectMapper = new ObjectMapper();
            QuestionsItemsDto items = objectMapper.convertValue(itemsIterator.next(), QuestionsItemsDto.class);
            // 日期题
            if (type == QuestionType.DATE && items.getValue() != null && formatType !=null && Objects.toString(items.getValue()).matches("^-?[1-9]\\d*$")) {
                value = new SimpleDateFormat(formatType.getFormat()).format(new Date(Long.parseLong(String.valueOf(items.getValue()))));
            }

            if (items.getItems() != null) {

                String otherString = "other";
                HashMap<Object, Object> convertMap = new HashMap<>();
                ArrayList<Object> convertList = new ArrayList<>();
                AtomicReference<String> convertString = new AtomicReference<>();
                Object itemValue = items.getValue();

                List<QuestionsItemDto> itemsList = items.getItems();
                List<String> itemsNameList = itemsList.stream().map(x -> x.getName()).collect(Collectors.toList());

                itemsList.forEach(item -> {
                    String name = item.getName();
                    if (itemValue == null) {
                        return;
                    }

                    if (itemValue instanceof Map) {
                        if (((Map) itemValue).containsKey(name)) {
                            Object mapValue = ((Map) itemValue).get(name);
                            Optional<String> columnsTextOptional = items.getColumns() == null ? Optional.empty(): items.getColumns().stream().filter(x -> x.getName().equals(mapValue)).map(x -> x.getText()).findFirst();
                            if(!columnsTextOptional.isEmpty()){
                                convertMap.put(replaceHtml(item.getText()), columnsTextOptional.get());
                            }else{
                                convertMap.put(replaceHtml(item.getText()),mapValue );
                            }
                        }
                    } else if (itemValue instanceof List) {
                        if (((List<?>) itemValue).contains(name)) {
                            convertList.add(replaceHtml(item.getText()));
                        }
                    } else {
                        if (itemValue.equals(name)) {
                            convertString.set(replaceHtml(item.getText()));
                        }else{
                            // 单选其他
                            if(convertString.get() == null){
                                // 单选其他为空
                                if(otherString.equals(itemValue) && Objects.toString(comment).isEmpty()){
                                    convertString.set(comment !=null ? comment.toString() : "");
                                }else{
                                    convertString.set(replaceHtml((String) itemValue));
                                }
                            }
                        }
                    }
                });
                // 多选其他
                if(itemValue instanceof List){
                    Optional<?> otherValue = ((List<?>) itemValue).stream().filter(i -> !itemsNameList.contains(i)).findFirst();
                    // 有其他值，但是其他值不为空
                    if (otherValue.isPresent() && otherString.equals(Objects.toString(otherValue.get()))) {
                        convertList.add(comment != null ? comment.toString() : "");
                    }
                }

                setValue(convertList.isEmpty() ? (convertMap.isEmpty() ? convertString.get() : convertMap) : convertList);

            }
            // 逐级下拉类似多选，但是没有选项需要单独处理

            if(this.getType() != null && this.getType().equals(QuestionType.DROP_DOWN)){
                if(value != null){
                    value = ((List) value).isEmpty() ? null : value;
                }
            }
        }
    }



}
