package org.befun.adminx.dto.survey;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.entity.survey.Survey;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/2 16:52
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseStatisticsDto {
    private String openid;
    private Integer total = 0;
    private Integer auditPass = 0;
    private Integer earlyCompleted = 0;
    private Integer invalid = 0;
    private Integer deleted = 0;
    private Integer waitAudit = 0;
    private Integer auditFail = 0;
    private Integer quotaFull = 0;
    private Integer auditError = 0;
}
