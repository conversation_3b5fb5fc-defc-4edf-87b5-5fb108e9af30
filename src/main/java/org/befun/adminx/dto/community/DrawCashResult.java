package org.befun.adminx.dto.community;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/10/31/0031 13:53:14
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class DrawCashResult {
    private boolean status;
    private String message;
    private String state;
    private String packageInfo;


    public DrawCashResult(String errCode) {
        this.status = false;
        boolean exist = Arrays.stream(DrawCashResultCode.values()).anyMatch(x->x.name().equals(errCode));
        if (exist) {
            System.out.println("存在");
            this.message = DrawCashResultCode.valueOf(errCode).desc;
        } else {
            System.out.println("不存在");

            this.message = errCode == null ? "提现失败" : "提现失败"+errCode;
        }
    }

    public DrawCashResult(boolean status, String message) {
        this.status = status;
        this.message = message;
    }

    /**
     * 需要什么添加什么
     */
    private enum DrawCashResultCode {
        SENDNUM_LIMIT("超过平台的当日提现次数，请明天再提现"),
        SENDAMOUNT_LIMIT("超过平台的当日提现金额上限"),
        RCVDAMOUNT_LIMIT("超过平台的当日提现金额上限");
        private final String desc;

        DrawCashResultCode(String desc) {
            this.desc = desc;
        }
    }
}
