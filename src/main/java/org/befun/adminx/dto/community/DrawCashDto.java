package org.befun.adminx.dto.community;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/28 11:02
 */
@Getter
@Setter
public class DrawCashDto {
    @Schema(name = "提现积分")
    private Integer amount;
    @Schema(name = "增加积分")
    private Integer addAmount;
    private String token;
    private String openId;
}
