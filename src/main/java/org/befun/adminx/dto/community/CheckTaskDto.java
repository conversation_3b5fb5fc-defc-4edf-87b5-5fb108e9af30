package org.befun.adminx.dto.community;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/11/28 14:14
 */
@Getter
@Setter
public class CheckTaskDto {
    private Long trackId;
    @JsonAlias({"inviteId", "invite"})
    private Long inviteId;
    private String openid;
    private String gridId;
    private Float latitude;
    private Float longitude;
}
