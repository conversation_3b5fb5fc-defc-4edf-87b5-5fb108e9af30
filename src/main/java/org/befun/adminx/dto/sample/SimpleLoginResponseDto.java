package org.befun.adminx.dto.sample;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
public class SimpleLoginResponseDto extends BaseDTO {
    private String token;
    private Long userId;
    private String username;
    private List<String> roles;
}
