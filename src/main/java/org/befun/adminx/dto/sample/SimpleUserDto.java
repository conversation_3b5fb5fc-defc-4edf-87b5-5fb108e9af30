package org.befun.adminx.dto.sample;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Value;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/27 17:54
 */
@Value
public class SimpleUserDto {

    @JsonView(ResourceViews.Basic.class)
    private String nickName;

    @JsonView(ResourceViews.Basic.class)
    private String avatar;

    @JsonView(ResourceViews.Basic.class)
    public Date createTime;

}
