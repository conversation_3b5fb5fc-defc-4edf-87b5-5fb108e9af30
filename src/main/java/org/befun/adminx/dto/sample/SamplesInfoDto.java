package org.befun.adminx.dto.sample;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


@Getter
@Setter
public class SamplesInfoDto extends SamplesDto{
    @JsonView(ResourceViews.Basic.class)
    private String educationLabels;
    @JsonView(ResourceViews.Basic.class)
    private String genderLabel;

}
