package org.befun.adminx.dto.sample;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.befun.core.rest.view.ResourceViews;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/15 10:48
 */
@Getter
@Setter
public class SamplesDto {
    @JsonView(ResourceViews.Basic.class)
    private Integer ageMin;
    @JsonView(ResourceViews.Basic.class)
    private Integer ageMax;
    @JsonView(ResourceViews.Basic.class)
    private List<Integer> educations;
    @JsonView(ResourceViews.Basic.class)
    private Integer gender;
    @JsonView(ResourceViews.Basic.class)
    private List<List<String>> locations;
    @JsonView(ResourceViews.Basic.class)
    private Integer communityLevel;
    @JsonView(ResourceViews.Basic.class)
    private String registerTimeStart;
    @JsonView(ResourceViews.Basic.class)
    private String registerTimeEnd;
    @JsonView(ResourceViews.Basic.class)
    private String followTimeStart;
    @JsonView(ResourceViews.Basic.class)
    private String followTimeEnd;
    @JsonView(ResourceViews.Basic.class)
    private FilterCondition completion;
    @JsonView(ResourceViews.Basic.class)
    private FilterCondition pass;
    @JsonView(ResourceViews.Basic.class)
    private FilterCondition passRate;


    public Boolean completionIsNull() {
        return conditionIsNull(completion);
    }

    public Boolean passIsNull() {
        return conditionIsNull(pass);
    }

    public Boolean passRateIsNull() {
        return conditionIsNull(passRate);
    }

    public Boolean conditionIsNull(FilterCondition condition) {
        if (condition == null) return true;
        if (!StringUtils.isEmpty(condition.getCondition()) && condition.getValue() != null) return false;
        return true;
    }

    public MutablePair<String, String> sumCaseCompleted(FilterCondition condition) {
        MutablePair<String, String> pair = new MutablePair<>();
        pair.setLeft("");
        pair.setRight("");
        if (!conditionIsNull(condition)) {
            if (StringUtils.isEmpty(condition.getRange())) {
                pair.setLeft("SUM(completed) as t_completed");
            } else {
                pair.setLeft("SUM(CASE WHEN `date` >= '" + convertConditionDate(condition) + "' THEN completed ELSE 0 END) as t_completed");
            }
            pair.setRight("t_completed" + condition.getCondition() + condition.getValue());
        }
        return pair;

    }

    public MutablePair<String, String> sumCasePass(FilterCondition condition) {
        MutablePair<String, String> pair = new MutablePair<>();
        pair.setLeft("");
        pair.setRight("");
        if (!conditionIsNull(condition)) {
            if (StringUtils.isEmpty(condition.getRange())) {
                pair.setLeft("SUM(audit_pass) as t_pass");
            } else {
                pair.setLeft("SUM(CASE WHEN `date` >= '" + convertConditionDate(condition) + "' THEN audit_pass ELSE 0 END) as t_pass");
            }
            pair.setRight("t_pass" + condition.getCondition() + condition.getValue());
        }
        return pair;
    }

    public MutablePair<String, String> sumCasePassRate(FilterCondition condition) {
        MutablePair<String, String> pair = new MutablePair<>();
        pair.setLeft("");
        pair.setRight("");
        if (!conditionIsNull(condition)) {
            if (StringUtils.isEmpty(condition.getRange())) {
                pair.setLeft("SUM(audit_pass)/SUM(completed) * 100 as t_pass_rate");
            } else {
                pair.setLeft("SUM(CASE WHEN `date` >= '" + convertConditionDate(condition) + "' THEN audit_pass ELSE 0 END)/SUM(CASE WHEN `date` >= '" + convertConditionDate(condition) + "' THEN completed ELSE 0 END) * 100 as t_pass_rate");
            }
            pair.setRight("t_pass_rate" + condition.getCondition() + condition.getValue());
        }
        return pair;
    }

    public String convertConditionDate(FilterCondition condition) {
        if (StringUtils.isEmpty(condition.getRange())) return "";
        LocalDate today = LocalDate.now();
        LocalDate monthsAgo = today.minusMonths(Long.parseLong(condition.getRange()));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return monthsAgo.format(formatter);
    }

    @Getter
    @Setter
    public static class FilterCondition {
        @JsonView(ResourceViews.Basic.class)
        private String range;

        @JsonView(ResourceViews.Basic.class)
        private String condition;

        @JsonView(ResourceViews.Basic.class)
        private Float value;
    }
}
