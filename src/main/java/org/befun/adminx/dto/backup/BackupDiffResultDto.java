package org.befun.adminx.dto.backup;

import lombok.*;
import org.befun.core.dto.BaseDTO;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackupDiffResultDto extends BaseDTO {
   private Integer count1 = 0;
   private Integer count2 = 0;
   private Integer sameCount = 0;

   private List<BackupRecordDto> news = new ArrayList<>();
   private List<BackupRecordDto> deletes = new ArrayList<>();
   private List<BackupRecordDto> updates = new ArrayList<>();
   private List<BackupRecordDto> sames = new ArrayList<>();

   public void addSame(BackupRecordDto r) {
      this.sameCount++;
      this.sames.add(r);
   }

   public void addUpdate(BackupRecordDto r) {
      this.updates.add(r);
   }

   public void addDel(BackupRecordDto r) {
      this.deletes.add(r);
   }

   public void addNew(BackupRecordDto r) {
      this.news.add(r);
   }
}