package org.befun.adminx.dto.backup;

import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.HashMap;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackupRecordDto extends BaseDTO {
   private String c;
   private String t;
   private Long id;
   private String s;

   private HashMap<String, Object> raw;  // 新增字段，用于保存原始的列数据

   public String indexKey() {
      return t + '_' + id;
   }
}