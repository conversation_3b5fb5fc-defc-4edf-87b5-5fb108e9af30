package org.befun.adminx.dto.download;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.annotation.DownloadField;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.adminx.dto.survey.QuestionDropDownDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.utils.DateFormatter;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.befun.core.utils.JsonHelper;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
@Slf4j
public class DownloadFileDto {

    public static List<QuestionType> EX_INCLUDE_TYPE = List.of(
            QuestionType.MARK,
            QuestionType.SEPARATOR,
            QuestionType.EMPTY,
            QuestionType.MEDIA,
            QuestionType.GROUP
    );
    protected static List<String> DYNAMIC_FIELD = List.of(
            "departmentId",
            "externalUserId",
            "customerId",
            "customerGender",
            "customerName",
            "departmentName",
            "departmentCode",
            "externalCompanyId",
            "defaultPa",
            "defaultPb",
            "defaultPc",
            "parameters",
            "totalScore",
            "name",
            "phone"
    );

    @DownloadField(display = "id", displayCode = "id")
    private String id;

    @DownloadField(display = "微信openId", displayCode = "openID")
    private String openid;

    @DownloadField(display = "答卷编号", displayCode = "QID")
    private String sequence;

    @DownloadField(display = "回收类型", displayCode = "send_type")
    private String collectorMethod;

    @DownloadField(display = "姓名", displayCode = "name")
    private String name;

    @DownloadField(display = "手机号码", displayCode = "phone")
    private String phone;

    @DownloadField(display = "提交时间", displayCode = "end_time")
    private String finishTime;

    @DownloadField(display = "开始时间", displayCode = "start_time")
    private String startTime;

    @DownloadField(display = "答题状态", displayCode = "result_code")
    private String status;

    @DownloadField(display = "审核方式", displayCode = "audit_type")
    private String auditType = "";

    @DownloadField(display = "审核得分", displayCode = "audit_score")
    private String auditScore = "";

    @DownloadField(display = "失败规则", displayCode = "fail_rule")
    private String failRule = "";

    @DownloadField(display = "ip", displayCode = "ip")
    private String ip;

    @DownloadField(display = "答题时长(秒)", displayCode = "duration_seconds")
    private Integer durationSeconds;

    @DownloadField(display = "答题总时长(秒)", displayCode = "duration_total")
    private Long durationTotal;

    @DownloadField(display = "国家(ip)", displayCode = "ip_country")
    private String country;

    @DownloadField(display = "省份(ip)", displayCode = "ip_province")
    private String province;

    @DownloadField(display = "城市(ip)", displayCode = "ip_city")
    private String city;

    @DownloadField(display = "外部客户ID", displayCode = "externalUserId")
    private String externalUserId;

    @DownloadField(display = "外部参数", displayCode = "external_parameters")
    private String parameters;

//    @DownloadField(display = "总得分")
//    private String totalScore;

    /*******以下字段是社区用户信息字段*******/

    @DownloadField(display = "CUID", displayCode = "cuid")
    private String cuid;

    @DownloadField(display = "推荐码", displayCode = "invite_code")
    private String userName;

    @DownloadField(display = "性别", displayCode = "gender")
    private String gender;

    @DownloadField(display = "年龄", displayCode = "age")
    private String age;

    @DownloadField(display = "学历", displayCode = "education")
    private String education;

    @DownloadField(display = "学历更新时间", displayCode = "education_update_time")
    private String educationModified;

    @DownloadField(display = "省份", displayCode = "_province")
    private String _province;

    @DownloadField(display = "城市", displayCode = "_city")
    private String _city;

    @DownloadField(display = "县/区", displayCode = "_area")
    private String _area;

    /*******以上字段是社区用户信息字段*******/

    public List buildHeaders(List<SurveyQuestion> surveyQuestion, List<SurveyResponse> surveyResponses, Boolean transCode) {

        /**
         * 外部参数 departmentId externalUserId customerId
         * 暂不考虑答题有一部分会带一部分不带
         */
        revertClassAnnotation();

        Field[] fields = this.getClass().getDeclaredFields();
        List<String> attributes = Arrays.stream(fields)
                .filter(x -> x.getAnnotationsByType(DownloadField.class).length > 0)
                .map(field -> {
                    if (DYNAMIC_FIELD.contains(field.getName())) {
                        // 当初商量是一份问卷的答题要么同时会有动态带ID的字段,要么不带
                        // 现在动态添加
                        List<SurveyResponse> surveyResponseWithId = surveyResponses.stream().filter(surveyResponse -> {
                            try {
                                Object value = PropertyUtils.getProperty(surveyResponse, field.getName());
                                if (value != null && CollectionUtils.size(value) > 0) {
                                    return true;
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            return false;
                        }).collect(Collectors.toList());

                        if (surveyResponseWithId.size() < 1) {
                            // 动态修改注解里的属性，用于后面判断是否取动态ID的值
                            try {
                                DownloadField downloadField = field.getAnnotation(DownloadField.class);
                                InvocationHandler h = Proxy.getInvocationHandler(downloadField);
                                Field hField = h.getClass().getDeclaredField("memberValues");
                                hField.setAccessible(true);
                                Map memberValues = (Map) hField.get(h);
                                memberValues.put("show", false);
                                return null;
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    return transCode ? field.getAnnotation(DownloadField.class).displayCode() : field.getAnnotation(DownloadField.class).display();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> questions = surveyQuestion.stream()
                .filter(x -> !EX_INCLUDE_TYPE.contains(x.getType()))
                .map(
                        question -> {

                            List<String> questionsItems;
                            AtomicInteger atomicInteger = new AtomicInteger(1);

                            if (question.getItems().isEmpty()) {
                                // 不带选项的题型
                                List<String> headerText;
                                switch (question.getType()) {
                                    case AREA:
                                        // 地区题型不带item 还需要拆分省市区地址到单元格
                                        headerText = transCode
                                                ? Arrays.stream(question.getAreaType().getFormat().split("-"))
                                                .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                .collect(Collectors.toList())
                                                : Arrays.stream(question.getAreaType().getFormat().split("-"))
                                                .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                .collect(Collectors.toList());
                                        break;
                                    case DROP_DOWN:
                                        // 逐级下拉不带item 还需要拆分到单元格
                                        QuestionDropDownDto config = JsonHelper.toObject(question.getConfigure(), QuestionDropDownDto.class);
                                        if (config == null) {
                                            // 新建题型时 前端不会存储configure
                                            // 产品需求默认使用一级选项, 二级选项
                                            List<String> config_default = List.of("一级选项", "二级选项");
                                            headerText = transCode
                                                    ? config_default
                                                    .stream()
                                                    .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                    .collect(Collectors.toList())
                                                    : config_default
                                                    .stream()
                                                    .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                    .collect(Collectors.toList());
                                            break;
                                        }
                                        headerText = transCode
                                                ? config.getProps()
                                                .stream()
                                                .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                .collect(Collectors.toList())
                                                : config.getProps()
                                                .stream()
                                                .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                .collect(Collectors.toList());
                                        break;
                                    case LOCATION:
                                        String title = RegularExpressionUtils.replaceHtml(question.getTitle());
                                        headerText = transCode
                                                ? List.of(String.format("%s_%s", question.getCode(), "datasource")
                                                , String.format("%s_%s", question.getCode(), "longitude")
                                                , String.format("%s_%s", question.getCode(), "latitude")
                                                , String.format("%s_%s", question.getCode(), "location"))
                                                : List.of(String.format("%s_%s", question.getCode(), "数据来源")
                                                , String.format("%s_%s_%s", question.getCode(), title, "经度")
                                                , String.format("%s_%s_%s", question.getCode(), title, "纬度")
                                                , String.format("%s_%s_%s", question.getCode(), title, "位置名称"));
                                        break;
                                    default:
                                        headerText = transCode
                                                ? List.of(question.getCode())
                                                : List.of(String.format(
                                                "%s_%s",
                                                question.getCode(),
                                                RegularExpressionUtils.replaceHtml(question.getTitle()))
                                        );
                                }
                                questionsItems = new ArrayList<>(headerText);
                            } else {
                                // 带选项题型
                                questionsItems = new ArrayList<>();
                                switch (question.getType()) {
                                    //联合实验题
                                    case EXPERIMENT:
                                        String text = question.getConfigure();
                                        //所有属性和水平
//                                        List<Map<String, Object>> attrs = (List<Map<String, Object>>) JSONArray.parse(text);
                                        List<Map> attrs = JsonHelper.toList(text, Map.class);
                                        //组合个数
                                        for (int i = 0; i < question.getItems().size(); i++) {
                                            //属性个数
                                            for (int j = 0; j < attrs.size(); j++) {
                                                questionsItems.add(transCode
                                                        //M1_1_1
                                                        ? String.format("%s_%s_%s", question.getCode(), i + 1, j + 1)
                                                        //M1_实验题_组合1_属性1
                                                        : String.format("%s_%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        RegularExpressionUtils.replaceHtml(question.getItems().get(i).getText()),
                                                        RegularExpressionUtils.replaceHtml(attrs.get(j).get("attribute").toString()))
                                                );
                                            }
                                        }
                                        break;
                                    //单选只保留一个选项内容
                                    case SINGLE_CHOICE:
                                    case COMBOBOX:
                                        questionsItems.add(transCode
                                                ? String.format("%s", question.getCode())
                                                : String.format("%s_%s", question.getCode(),
                                                RegularExpressionUtils.replaceHtml(question.getTitle()))
                                        );
                                        //如果选项设置文本输入
                                        for (SurveyQuestionItem item : question.getItems()) {
                                            if (item.getEnableTextInput()) {
                                                questionsItems.add(transCode
                                                        ? String.format("%s_text", question.getCode(), atomicInteger.getAndIncrement())
                                                        : String.format("%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        "文本输入")
                                                );
                                                break;
                                            }
                                        }
                                        break;
                                    case EVALUATION:
                                    case SCORE_EVALUATION:
                                        int order = 1;
                                        if (question.getType().equals(QuestionType.SCORE_EVALUATION)) {
                                            questionsItems.add(transCode
                                                    ? String.format("%s_%s", question.getCode(), order++)
                                                    : String.format("%s_%s_打分", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        }
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_评价", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_标签", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_评价文本", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        break;
                                    case NPS:
                                        questionsItems.add(transCode
                                                ? String.format("%s_1", question.getCode())
                                                : String.format("%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_2", question.getCode())
                                                : String.format("%s_%s_标签", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_3", question.getCode())
                                                : String.format("%s_%s_评价文本", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        break;
                                    default:
                                        for (SurveyQuestionItem item : question.getItems()) {
                                            Integer index = atomicInteger.getAndIncrement();
                                            questionsItems.add(transCode
                                                    ? String.format("%s_%s", question.getCode(), index)
                                                    : String.format("%s_%s_%s", question.getCode(),
                                                    RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                    RegularExpressionUtils.replaceHtml(item.getText()))
                                            );
                                            if (item.getEnableTextInput()) {
                                                questionsItems.add(transCode
                                                        ? String.format("%s_%s_text", question.getCode(), index)
                                                        : String.format("%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        "文本输入")
                                                );
                                            }
                                        }
                                }
                            }
                            //TODO 兼容单选多选的其他选项
                            if (question.getHasOther() && Arrays.asList(QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES).contains(question.getType())) {
                                questionsItems.add(transCode
                                        ? String.format("%s_%s", question.getCode(), questionsItems.size() + 1)
                                        : String.format("%s_%s_%s", question.getCode(),
                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                        question.getOtherLabel()
                                ));
                            }
                            return questionsItems;
                        }
                ).
                collect(Collectors.toList()).
                stream().
                flatMap(Collection::stream).
                collect(Collectors.toList());

        return ListUtils.union(attributes, questions);
    }

    public List<Object> buildResponse(SurveyResponse surveyResponse, CommunityUser user) throws Exception {
        if(surveyResponse == null) return null;
        String finishTime = surveyResponse.getFinishTime() == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(surveyResponse.getFinishTime());

        this.id = surveyResponse.getId().toString();
        this.openid = Objects.toString(surveyResponse.getOpenid(), "");
        this.sequence = Objects.toString(surveyResponse.getSequence(), "");
        this.collectorMethod = surveyResponse.getCollectorMethod().getText();
        this.name = Objects.toString(surveyResponse.getName(), "");
        this.phone = Objects.toString(surveyResponse.getPhone(), "");
        this.finishTime = finishTime;
        this.startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(surveyResponse.getCreateTime());
        this.status = surveyResponse.getStatus().toString();
        //审核信息
        if(!StringUtils.isEmpty(surveyResponse.getAuditResult())) {
            AuditResultJsonDto dto = JsonHelper.toObject(surveyResponse.getAuditResult(), AuditResultJsonDto.class);
            if(dto.getType() != null) {
                this.auditType = dto.getType().getText();
            } else {
                //TODO 兼容以前审核方式
                if(dto.getAutoAudit() == null) this.auditType = "半自动";
                else this.auditType = dto.getAutoAudit() ? "自动":"手动";
            }
            this.auditScore = dto.getScore().toString();
            this.failRule = Objects.toString(dto.getFailRuleName(), "");
        }
        this.ip = Objects.toString(surveyResponse.getIp(), "");
        this.durationSeconds = surveyResponse.getDurationSeconds() == null ? 0 : surveyResponse.getDurationSeconds();
        this.durationTotal = (surveyResponse.getFinishTime() != null && surveyResponse.getCreateTime() != null) ? (surveyResponse.getFinishTime().getTime() - surveyResponse.getCreateTime().getTime()) / 1000 : 0l;
        this.country = surveyResponse.getCountry();
        this.province = surveyResponse.getProvince();
        this.city = surveyResponse.getCity();
        this.externalUserId = Objects.toString(surveyResponse.getExternalUserId(), "");
        this.parameters = surveyResponse.getParameters().isEmpty() ? "" : JsonHelper.toJson(surveyResponse.getParameters());

//        this.totalScore = surveyResponse.getTotalScore() != null ? surveyResponse.getTotalScore().toString() : "";
        /*******社区用户信息********/
        this.cuid = (user == null) ? "":user.getId().toString();
        this.userName = (user == null) ? "":user.getUserName();
        Boolean additional = user == null || user.getAdditional() == null;
        this.gender = !additional && user.getAdditional().getGender() != null ? user.getAdditional().getGender():"";
        this.age =!additional && user.getAdditional().getBirthday() != null ? DateFormatter.getAge(user.getAdditional().getBirthday()).toString():"";
        this.education = !additional && user.getAdditional().getEducation() != null ? user.getAdditional().getEducation():"";
        this.educationModified = !additional && user.getAdditional().getEducationModified() != null ? user.getAdditional().getEducationModified().toString():"";
        this._province = !additional && user.getAdditional().getProvince() != null ? user.getAdditional().getProvince():"";
        this._city = !additional && user.getAdditional().getCity() != null ? user.getAdditional().getCity():"";
        this._area = !additional && user.getAdditional().getArea() != null ? user.getAdditional().getArea():"";
        /************************/
        Field[] fields = this.getClass().getDeclaredFields();

        return Arrays.stream(fields).filter(x -> x.getAnnotationsByType(DownloadField.class).length > 0).map(field -> {
            try {
                Object value = PropertyUtils.getProperty(this, field.getName());
                if (DYNAMIC_FIELD.contains(field.getName())) {

                    if (field.getAnnotation(DownloadField.class).show()) {
                        // 动态ID字段如果其中有人带了参数，其他人没有就为空
                        return value;
                    }

                    if (value == null || String.valueOf(value).isEmpty()) {
                        return null;
                    }

                }
                return value;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void revertClassAnnotation() {
        // 由于对使用getClass修改了注解，导致后面使用的时候实际上是已经修改过了
        // 所以在下载完成后需要通过getClass把注解还原成
        Field[] fields = this.getClass().getDeclaredFields();
        Arrays.stream(fields)
                .filter(x -> x.getAnnotationsByType(DownloadField.class).length > 0)
                .forEach(field -> {
                    if (DYNAMIC_FIELD.contains(field.getName())) {
                        try {
                            DownloadField downloadField = field.getAnnotation(DownloadField.class);
                            InvocationHandler h = Proxy.getInvocationHandler(downloadField);
                            Field hField = h.getClass().getDeclaredField("memberValues");
                            hField.setAccessible(true);
                            Map memberValues = (Map) hField.get(h);
                            memberValues.put("show", true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }
}