package org.befun.adminx.dto.grid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import org.befun.adminx.constant.WechatSubscribeStatus;

/**
 * 网格成员
 */
@Getter
@Setter
public class GridMemberDetailDto {

	@Schema(description = "昵称")
	private String nickName;

	private Long cuid;

	@Schema(description = "头像")
	private String avatar;

	@Schema(description = "公众号关注状态")
	private WechatSubscribeStatus wechatSubscribe;

	@Schema(description = "当前审核数")
	private Integer waitAuditCount;

	@Schema(description = "累计通过次数")
	private Integer passAuditCount;

	@Schema(description = "成员备注")
	private String remark;

}
