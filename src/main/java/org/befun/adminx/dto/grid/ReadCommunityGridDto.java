package org.befun.adminx.dto.grid;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReadCommunityGridDto {

    @Schema(description = "网格id：上传者自行定义")
    @ExcelProperty(value = "ID")
    private String gridId;

    @Schema(description = "网格cuid")
    @ExcelProperty(value = "网格员CUID")
    private Long cuid;

    @Schema(description = "网格名称")
    @ExcelProperty(value = "网格名称")
    private String gridName;

    @Schema(description = "网格员名称")
    @ExcelProperty(value = "网格员名称")
    private String gridInspectorName;

    @Schema(description = "网格id：上传者自行定义")
    @ExcelProperty(value = "联系方式")
    private String contract;


}
