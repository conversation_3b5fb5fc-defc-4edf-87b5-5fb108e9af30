package org.befun.adminx.dto.grid;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import org.befun.adminx.constant.WechatSubscribeStatus;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * 网格成员
 */
@Value
public class SimpleGridMemberDto {


	@JsonView(ResourceViews.Basic.class)
	private String nickName;

	@JsonView(ResourceViews.Basic.class)
	private String avatar;

	@JsonView(ResourceViews.Basic.class)
	public Long id;

	@JsonView(ResourceViews.Basic.class)
	public String gridId;

	@JsonView(ResourceViews.Basic.class)
	public Date createTime;

}
