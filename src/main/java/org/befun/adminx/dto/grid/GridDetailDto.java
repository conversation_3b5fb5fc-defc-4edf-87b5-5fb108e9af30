package org.befun.adminx.dto.grid;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.Days360;
import org.befun.adminx.dto.MetaDto;
import org.befun.adminx.entity.CommunityUser;
import org.springframework.data.domain.Page;

import java.util.List;

@Setter
@Getter
public class GridDetailDto {
	@Schema(description = "title")
	private String title;
	@Schema(description = "待审核")
	private long waitAuditCount;
	@Schema(description = "审核通过")
	private long passAuditCount;
	@Schema(description = "未完成成员")
	private List<CommunityUser> unCompleteUser;
	@Schema(description = "分页数据")
	private MetaDto meta;
}
