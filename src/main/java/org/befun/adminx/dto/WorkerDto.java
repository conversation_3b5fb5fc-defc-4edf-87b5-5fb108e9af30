package org.befun.adminx.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.WorkerType;
import org.befun.task.BaseTaskDetailDto;

/**
 * <AUTHOR>
 * @date 2023/10/17/0017 15:20:46
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WorkerDto extends BaseTaskDetailDto {
    WorkerType type;
    Long workerId;
    boolean consumerAsync;
    boolean withNewPool = false;
}
