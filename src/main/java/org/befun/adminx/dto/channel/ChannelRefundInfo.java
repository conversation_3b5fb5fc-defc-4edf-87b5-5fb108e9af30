package org.befun.adminx.dto.channel;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.OrganizationOrder;
import org.befun.adminx.entity.SampleOrder;
import org.befun.adminx.entity.SampleOrderDto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Getter
@Setter
public class ChannelRefundInfo {
    //  {
    //      "type": "full",
    //      "unitPrice": 1,
    //      "quantity": 101,
    //      "recycle": 100,
    //      "serviceRate": 0.06,
    //      "refundAmount": 100
    //  }
    private String type;
    private Integer unitPrice;
    private Integer quantity;
    private Integer recycle;
    private Double serviceRate;
    private String payType;
    private Long refundAmount;
    private Date refundTime;

    public static ChannelRefundInfo build(SampleOrder sampleOrder, OrganizationOrder order) {
        return _build(sampleOrder.getUnitPrice2(), sampleOrder.getQuantity(), sampleOrder.getRecycle(), order.getPayType().name());
    }

    public static ChannelRefundInfo build(SampleOrderDto sampleOrder, OrganizationOrder order) {
        return _build(sampleOrder.getUnitPrice2(), sampleOrder.getQuantity(), sampleOrder.getRecycle(), order.getPayType().name());
    }

    private static ChannelRefundInfo _build(BigDecimal unitPrice, Integer quantity, Integer recycle, String payType) {
        unitPrice = unitPrice == null ? BigDecimal.valueOf(0) : unitPrice;
        quantity = quantity == null ? 0 : quantity;
        recycle = recycle == null ? 0 : recycle;
        ChannelRefundInfo refundInfo = new ChannelRefundInfo();
        refundInfo.setPayType(payType);
        if (recycle <= 0) {
            refundInfo.setType("full");
        } else {
            refundInfo.setType("part");
        }
        refundInfo.setUnitPrice(unitPrice.multiply(BigDecimal.valueOf(100)).intValue());
        refundInfo.setQuantity(quantity);
        refundInfo.setRecycle(recycle);
        refundInfo.setServiceRate(0.06);
        long refundNumber = quantity - recycle;
        long serviceAmount = refundNumber * 6 * refundInfo.getUnitPrice() / 100;
        long refundAmount = refundNumber * refundInfo.getUnitPrice();
        refundInfo.setRefundAmount(serviceAmount + refundAmount);
        return refundInfo;
    }

    public static String refundPrice(SampleOrderDto sampleOrder) {
        return _refundPrice(sampleOrder.getUnitPrice2(), sampleOrder.getQuantity(), sampleOrder.getRecycle());
    }

    public static String refundPrice(SampleOrder sampleOrder) {
        return _refundPrice(sampleOrder.getUnitPrice2(), sampleOrder.getQuantity(), sampleOrder.getRecycle());
    }

    private static String _refundPrice(BigDecimal unitPrice, Integer quantity, Integer recycle) {
        unitPrice = unitPrice == null ? BigDecimal.valueOf(0) : unitPrice;
        quantity = quantity == null ? 0 : quantity;
        recycle = recycle == null ? 0 : recycle;
        unitPrice = unitPrice.multiply(BigDecimal.valueOf(100));
        long refundNumber = quantity - recycle;
        if (refundNumber <= 0) {
            return "0";
        }
        long serviceAmount = unitPrice.multiply(BigDecimal.valueOf(6 * refundNumber)).divide(BigDecimal.valueOf(100), RoundingMode.DOWN).longValue();
        long refundAmount = unitPrice.multiply(BigDecimal.valueOf(refundNumber)).longValue();
        return String.valueOf((serviceAmount + refundAmount) * 1.0 / 100);
    }
}
