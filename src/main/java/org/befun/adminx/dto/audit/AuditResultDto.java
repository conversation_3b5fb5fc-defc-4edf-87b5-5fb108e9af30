package org.befun.adminx.dto.audit;

import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class AuditResultDto extends BaseDTO {
    private Integer score = 0;
    private Boolean pass;
    private String log;
    private String failRuleName;
    private List<AuditResultItemDto> items = new ArrayList<>();
}
