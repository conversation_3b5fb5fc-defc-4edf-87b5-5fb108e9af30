package org.befun.adminx.dto.audit;

import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditContext extends BaseDTO {
    private Long responseId;
    private Long surveyId;
    private String name;
    private Integer age;
    private Map<String, Object> context = new HashMap<>();
}
