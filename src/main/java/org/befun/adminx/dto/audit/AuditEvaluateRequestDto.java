package org.befun.adminx.dto.audit;

import lombok.*;
import org.befun.adminx.constant.AuditScriptLanguage;
import org.befun.adminx.constant.AutoAuditType;
import org.befun.adminx.entity.dto.AuditRule;
import org.befun.core.dto.BaseDTO;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditEvaluateRequestDto extends BaseDTO {
    private AutoAuditType auditType = AutoAuditType.SCRIPT;
    private Integer threshold = 0;
    private List<AuditRule> rules = new ArrayList<>();
    private AuditScriptLanguage language;
    private String code;
    private Map<String, Object> context = new HashMap<>();
}
