package org.befun.adminx.dto.audit;

import lombok.*;
import org.befun.adminx.constant.AuditType;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/20 14:07
 */
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class AuditResultDownLoadDto {
    private String auditType;
    private String auditScore;
    private String failRule;
    private Integer points;

}
