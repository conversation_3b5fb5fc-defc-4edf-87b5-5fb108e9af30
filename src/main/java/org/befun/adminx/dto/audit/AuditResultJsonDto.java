package org.befun.adminx.dto.audit;

import io.swagger.models.auth.In;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.*;

import javax.persistence.Column;
import javax.persistence.Enumerated;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/20 14:07
 */
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class AuditResultJsonDto {
    private Integer score = 0;
    private String failRuleName = "";
    private Boolean autoAudit;
    private AuditType type;
    private Integer points;

    public AuditResultJsonDto(Integer score, AuditType type, String failRuleName) {
        this.score = score;
        this.type = type;
        this.failRuleName = failRuleName;
    }
}
