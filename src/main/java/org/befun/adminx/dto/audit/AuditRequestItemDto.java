package org.befun.adminx.dto.audit;

import lombok.*;
import org.befun.adminx.constant.TaskType;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class AuditRequestItemDto extends BaseDTO {
    private String openId;
    private Long responseId;
    private String content;
    private Boolean autoAudit = false;

    //追访积分奖励
    private Integer scoreChange;
    //任务类型
    private TaskType taskType = TaskType.DELIVERY;
}
