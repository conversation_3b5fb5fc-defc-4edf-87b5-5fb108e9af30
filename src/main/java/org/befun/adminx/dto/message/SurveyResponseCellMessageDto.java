package org.befun.adminx.dto.message;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.survey.FormatType;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.survey.QuestionsItemDto;
import org.befun.adminx.dto.survey.QuestionsItemsDto;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.befun.adminx.utils.RegularExpressionUtils.replaceHtml;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SurveyResponseCellMessageDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    private Object comment;
    @JsonView(ResourceViews.Basic.class)
    private String code;
    @JsonView(ResourceViews.Basic.class)
    private Long questionId;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tags;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tagsAll;
    @JsonView(ResourceViews.Basic.class)
    private QuestionType type;
    private Map<String, QuestionsItemsDto> questionsItems;
    @JsonView(ResourceViews.Basic.class)
    private String inapplicableLabel;
    @JsonView(ResourceViews.Basic.class)
    private FormatType formatType;

    public SurveyResponseCellMessageDto(SurveyQuestion question, SurveyResponseCell cell) {

        QuestionsItemsDto items = new QuestionsItemsDto();
        items.setValue(cell.getValue());

        if (!question.getItems().isEmpty()) {
            ArrayList itemValue = new ArrayList();
            ArrayList columnValue = new ArrayList();
            question.getItems().forEach(x -> {
                SurveyResponseQuestionsItemDto item = new SurveyResponseQuestionsItemDto();
                item.setName(x.getValue());
                item.setText(x.getText());
                itemValue.add(item);
            });

            question.getColumns().forEach(x -> {
                SurveyResponseQuestionsItemDto column = new SurveyResponseQuestionsItemDto();
                column.setName(x.getValue());
                column.setText(x.getText());
                columnValue.add(column);
            });
            items.setItems(itemValue);
            items.setColumns(columnValue);
        }

        this.questionsItems = Map.of(question.getName(), items);

        // question
        this.type = question.getType();
        this.title = question.getTitle();
        this.questionId = question.getId();
        this.value = cell.getValue();
        this.comment = cell.getCommentValue();
        this.tags = cell.getTags() == null ? null : Arrays.stream(cell.getTags().split(",")).collect(Collectors.toList());
        Optional.ofNullable(question.getItems()).ifPresent(x -> {
            x.stream().filter(y -> y.getValue().equals(this.value)).anyMatch(z -> {
                var ts = z.getConfigure();
                // 打分评价题特殊推送所有标签
                if (List.of(QuestionType.SCORE_EVALUATION, QuestionType.EVALUATION).contains(this.type) && StringUtils.isNotEmpty(ts)) {
                    this.tagsAll = Arrays.stream(ts.split(",")).collect(Collectors.toList());
                }
                return false;
            });
        });
        this.code = question.getCode();
        this.inapplicableLabel = question.getInapplicableLabel();
        this.formatType = question.getAreaType();
    }

    public void convertText() {
        convertText(true);
    }

    public void convertText(Boolean trimHtml) {
        String textTitle = null;

        if (List.of(QuestionType.BLANK, QuestionType.MULTIPLE_BLANK).contains(type)) {
            textTitle = type.getText();
        } else {
            textTitle = trimHtml ? replaceHtml(title) : title;
        }
        setTitle(replaceHtml(title));

        Iterator itemsIterator = questionsItems.values().iterator();
        if (itemsIterator.hasNext()) {
            ObjectMapper objectMapper = new ObjectMapper();
            QuestionsItemsDto items = objectMapper.convertValue(itemsIterator.next(), QuestionsItemsDto.class);
            // 日期题
            if (type == QuestionType.DATE && items.getValue() != null && formatType != null && Objects.toString(items.getValue()).matches("^-?[1-9]\\d*$")) {
                value = new SimpleDateFormat(formatType.getFormat()).format(new Date(Long.parseLong(String.valueOf(items.getValue()))));
            }

            if (items.getItems() != null) {

                String otherString = "other";
                HashMap<Object, Object> convertMap = new HashMap<>();
                ArrayList<Object> convertList = new ArrayList<>();
                AtomicReference<String> convertString = new AtomicReference<>();
                Object itemValue = items.getValue();

                List<QuestionsItemDto> itemsList = items.getItems();
                List<String> itemsNameList = itemsList.stream().map(x -> x.getName()).collect(Collectors.toList());

                HashMap<Object, Object> finalConvertMap = convertMap;
                itemsList.forEach(item -> {
                    String name = item.getName();
                    if (itemValue == null) {
                        return;
                    }

                    if (itemValue instanceof Map) {
                        if (((Map) itemValue).containsKey(name)) {
                            Object mapValue = ((Map) itemValue).get(name);
                            Optional<String> columnsTextOptional = items.getColumns() == null ? Optional.empty() : items.getColumns().stream().filter(x -> x.getName().equals(mapValue)).map(x -> x.getText()).findFirst();
                            if (!columnsTextOptional.isEmpty()) {
                                finalConvertMap.put(replaceHtml(item.getText()), columnsTextOptional.get());
                            } else {
                                finalConvertMap.put(replaceHtml(item.getText()), mapValue);
                            }
                        }
                    } else if (itemValue instanceof List) {
                        if (((List<?>) itemValue).contains(name)) {
                            convertList.add(replaceHtml(item.getText()));
                        }
                    } else {
                        if (itemValue.equals(name)) {
                            convertString.set(replaceHtml(item.getText()));
                        } else {
                            // 单选其他
                            if (convertString.get() == null) {
                                // 单选其他为空
                                if (otherString.equals(itemValue) && Objects.toString(comment).isEmpty()) {
                                    convertString.set(comment != null ? comment.toString() : "");
                                } else {
                                    convertString.set(replaceHtml((String) itemValue));
                                }
                            }
                        }
                    }
                });
                // 多选其他
                if (itemValue instanceof List) {
                    Optional<?> otherValue = ((List<?>) itemValue).stream().filter(i -> !itemsNameList.contains(i)).findFirst();
                    // 有其他值，但是其他值不为空
                    if (otherValue.isPresent() && otherString.equals(Objects.toString(otherValue.get()))) {
                        convertList.add(comment != null ? comment.toString() : "");
                    }
                }

                if (QuestionType.RANKING.equals(type) && !convertMap.isEmpty()) {
                    // 排序题根据选择排序
                    HashMap<Object, Object> convertMapSort = new LinkedHashMap<>();
                    ((Map) convertMap).entrySet().stream().sorted(Map.Entry.comparingByValue()).forEachOrdered(x -> {
                        convertMapSort.put(((Map.Entry<?, ?>) x).getKey(), ((Map.Entry<?, ?>) x).getValue());
                    });
                    convertMap = convertMapSort;
                }

                setValue(convertList.isEmpty() ? (convertMap.isEmpty() ? convertString.get() : convertMap) : convertList);

            }
            // 逐级下拉类似多选，但是没有选项需要单独处理

            if (this.getType() != null && Arrays.asList(QuestionType.DROP_DOWN, QuestionType.ORGANIZE).contains(this.getType())) {
                if (value != null) {
                    value = ((List) value).isEmpty() ? null : value;
                }
            }
        }
    }

}
