package org.befun.adminx.dto.surveyaudit;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.SurveyAuditRecord;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SurveyAuditRecordExtDto extends BaseEntityDTO<SurveyAuditRecord> {

    public SurveyAuditRecordExtDto() {
    }

    public SurveyAuditRecordExtDto(SurveyAuditRecord entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "企业代码")
    private String orgCode;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "企业名称")
    private String orgName;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "手机号")
    private String mobile;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "账号状态")
    private Integer orgStatus;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷标题")
    private String surveyTitle;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "回收量")
    private long countResponse = 0;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷状态")
    private Integer surveyStatus = -1;
}
