package org.befun.adminx.dto.surveyaudit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.task.BaseTaskDetailDto;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyContentAuditDto extends BaseTaskDetailDto {
    private Long orgId;
    private Long userId;
    private Long surveyId;
    private Long auditId;
    private String reason;
    private List<String> content;
    private String requestTime;
}