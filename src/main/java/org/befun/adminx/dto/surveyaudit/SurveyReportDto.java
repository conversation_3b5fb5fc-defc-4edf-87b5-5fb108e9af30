package org.befun.adminx.dto.surveyaudit;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.exception.BadRequestException;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class SurveyReportDto {
    private String token;
    private Long responseId;  // 填写的举报内容的答卷id
    private Long finishTime;
    private Map<String, Object> parameters = new HashMap<>(); // token reportSurveyId 被举报的问卷id
    private List<Map<String, Object>> data = new ArrayList<>();

    public void checkToken(String token) {
        if (StringUtils.isNotEmpty(token)) {
            Object value = parameters.get("token");
            if (value != null && token.equals(value.toString())) {
                return;
            }
        }
        throw new BadRequestException("token 无效");
    }

    public long parseSurveyId() {
        Object value = parameters.get("reportSurveyId");
        if (value != null && NumberUtils.isDigits(value.toString())) {
            return Long.parseLong(value.toString());
        }
        throw new BadRequestException("问卷不存在");
    }

    public String parseReason() {
        return data.stream().filter(i -> {
            Object code = i.get("code");
            return code != null && "Q1".equalsIgnoreCase(code.toString());
        }).findFirst().map(i -> {
            Object value = i.get("value");
            if (value == null) {
                return "";
            } else if (value instanceof List<?>) {
                return ((List<?>) value).stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(","));
            } else {
                return value.toString();
            }
        }).orElse("");
    }

    public String parseContent() {
        return data.stream().filter(i -> {
            Object code = i.get("code");
            return code != null && "Q2".equalsIgnoreCase(code.toString());
        }).findFirst().map(i -> {
            Object value = i.get("value");
            if (value == null) {
                return "";
            } else if (value instanceof List<?>) {
                return ((List<?>) value).stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(","));
            } else {
                return value.toString();
            }
        }).orElse("");
    }

    public Date parseRequestTime() {
        Date date = null;
        try {
            date = new Date(finishTime);
        } catch (Throwable e) {
            // ignore
        }
        if (date == null) {
            date = new Date();
        }
        return date;
    }
}