package org.befun.adminx.dto.surveyaudit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.annotation.ResourceCustomQueryParam;
import org.befun.adminx.annotation.ResourceCustomSubQueryParam;
import org.befun.adminx.constant.survey.SurveyAuditSource;
import org.befun.adminx.constant.survey.SurveyAuditStatus;
import org.befun.adminx.entity.Organization;
import org.befun.adminx.entity.survey.Survey;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SurveyAuditRecordQueryDto extends ResourceCustomQueryDto {

    @ResourceCustomSubQueryParam(subClass = Organization.class, subProperty = "code")
    @Schema(description = "企业代码") // 子查询 org
    private String orgCode;
    @ResourceCustomSubQueryParam(subClass = Organization.class, subProperty = "name", operator = QueryOperator.LIKE)
    @Schema(description = "企业名称") // 子查询 org
    private String orgName;
    @ResourceCustomSubQueryParam(subClass = Organization.class, subProperty = "isBlock")
    @Schema(description = "企业状态") // 子查询 org
    private Integer orgStatus;

    @ResourceCustomSubQueryParam(subClass = Survey.class, subProperty = "id")
    @Schema(description = "问卷id") // 子查询 survey
    private Long surveyId;
    @ResourceCustomSubQueryParam(subClass = Survey.class, subProperty = "title", operator = QueryOperator.LIKE)
    @Schema(description = "问卷标题") // 子查询 survey
    private String surveyTitle;
    @ResourceCustomSubQueryParam(subClass = Survey.class, subProperty = "status")
    @Schema(description = "问卷状态")  // 子查询 survey
    private Integer surveyStatus;

    @ResourceCustomQueryParam
    @Schema(description = "审核状态") // record
    private SurveyAuditStatus status;
    @ResourceCustomQueryParam
    @Schema(description = "类型") // record
    private SurveyAuditSource source;
    @ResourceCustomQueryParam(property = "requestTime", operator = QueryOperator.GREATER_THAN_EQUAL)
    @Schema(description = "提交时间-开始") // record
    private Date requestTime_gte = null;
    @ResourceCustomQueryParam(property = "requestTime", operator = QueryOperator.LESS_THAN_EQUAL)
    @Schema(description = "提交时间-结束") // record
    private Date requestTime_lte = null;

    public Map<Class<?>, String> subQueryRootProeprtyMap() {
        return Map.of(Organization.class, "orgId", Survey.class, "surveyId");
    }

    @Override
    public List<String> supportSortProperties() {
        return List.of("createTime", "id");
    }
}
