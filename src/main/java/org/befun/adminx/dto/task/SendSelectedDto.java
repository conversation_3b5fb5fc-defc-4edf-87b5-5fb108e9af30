package org.befun.adminx.dto.task;

import lombok.*;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.dto.ext.SendTaskExDto;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.wechat.WechatNotifyTemplateDto;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendSelectedDto extends SendBaseDto {

    private SendTaskExDto task;
    private SendType type;
    private List<CommunityUserSearchDto> userList;
    private String baseLineSurveyId;

}
