package org.befun.adminx.dto.task;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SendSurveyBySmsTaskDto extends SendSurveyTaskDto {

    private String signId;
    private String templateId;

    public static SendSurveyBySmsTaskDto create(TemplateInfoDto template) {
        SendSurveyBySmsTaskDto dto = new SendSurveyBySmsTaskDto();
        dto.setSignId(template.getSmsSignId());
        dto.setTemplateId(template.getSmsTemplateId());
        return dto;
    }

}
