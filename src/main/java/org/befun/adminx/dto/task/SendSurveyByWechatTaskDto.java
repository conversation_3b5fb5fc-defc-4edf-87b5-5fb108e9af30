package org.befun.adminx.dto.task;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SendSurveyByWechatTaskDto extends SendSurveyTaskDto {
    private String appId;
    private String templateId;

    public static SendSurveyByWechatTaskDto create(TemplateInfoDto template) {
        SendSurveyByWechatTaskDto dto = new SendSurveyByWechatTaskDto();
        dto.setAppId(template.getWeChatAppId());
        dto.setTemplateId(template.getWeChatTemplateId());
        return dto;
    }
}
