package org.befun.adminx.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.SendType;
import org.befun.task.BaseTaskDetailDto;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateInfoDto  extends BaseTaskDetailDto {

    private String smsSignId;
    private String smsTemplateId;

    private String weChatAppId;
    private String weChatTemplateId;

    private String example;

    private SendType sendType;


    public static TemplateInfoDto createSms(String smsTemplateId, String smsSignId, String example, SendType sendType) {
        return new TemplateInfoDto(smsSignId, smsTemplateId, null, null, example, sendType);
    }

    public static TemplateInfoDto createWeChat(String weChatAppId, String weChatTemplateId, SendType sendType) {
        return new TemplateInfoDto(null, null, weChatAppId, weChatTemplateId, null, sendType);
    }
}
