package org.befun.adminx.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.task.dto.PageableTaskDetailDto;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SendSurveyTaskDto extends PageableTaskDetailDto {

    private String surveyId;
    private long channelId;
    private int realSize; // 本次处理的真实数量
    private String logIds;

    @Override
    public int countComplete() {
        return realSize;
    }

    public void formatLogIds(List<Long> logIds) {
        if (CollectionUtils.isNotEmpty(logIds)) {
            realSize = logIds.size();
            this.logIds = logIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        }
    }

    public List<Long> parseLogIds() {
        return Optional.ofNullable(logIds).map(i -> Arrays.stream(i.split(",")).map(Long::valueOf).collect(Collectors.toList())).orElse(new ArrayList<>());
    }
}
