package org.befun.adminx.dto.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.utils.DateHelper;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
public class SendBaseDto {

    private boolean isAppend;   // true 追加发送 false 首次发送
    public String sendTime; // 发送时间，没有此项则立即发送
    private Long surveyId;
    private Long taskId;

    private Map<String, Object> contentTemplate;// 内容模板

    private TemplateInfoDto template;


    @JsonIgnore
    private Date _sendTime;

    public Date transformSendDate() {
        if (_sendTime == null) {
            _sendTime = Optional.ofNullable(DateHelper.toDate(DateHelper.parseAdjust(sendTime))).orElse(new Date());
        }
        return _sendTime;
    }
}
