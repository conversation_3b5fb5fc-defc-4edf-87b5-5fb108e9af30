package org.befun.adminx.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @see
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskSyncQuotaDto {
    private boolean cem;
    private long taskProgressId;
    private long surveyId;
    private int page; // 从 0 开始
    private int size;
    private int realSize;
    private List<Long> syncQuotas;

    public TaskSyncQuotaDto(Boolean cem, Long surveyId) {
        this.cem = cem;
        this.surveyId = surveyId;
    }
}
