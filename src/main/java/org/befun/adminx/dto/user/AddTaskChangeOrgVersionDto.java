package org.befun.adminx.dto.user;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Locale;


@Getter
@Setter
@NoArgsConstructor
public class AddTaskChangeOrgVersionDto {

    private Long orgId;
    private String fromVersion;
    private String toVersion;

    public AddTaskChangeOrgVersionDto(Long orgId, String fromVersion, String toVersion) {
        this.orgId = orgId;
        this.fromVersion = fromVersion.toUpperCase(Locale.ROOT);
        this.toVersion = toVersion.toUpperCase(Locale.ROOT);
    }
}
