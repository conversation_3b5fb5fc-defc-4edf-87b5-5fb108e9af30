package org.befun.adminx.dto.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/10 18:30
 */
@Getter
@Setter
@NoArgsConstructor
public class VersionDto {

    @JsonProperty("cem_version")
    private String cemVersion;

    @JsonProperty("surveyplus_version")
    private String surveyPlusVersion;

    public VersionDto(String cemVersion, String surveyPlusVersion) {
        this.cemVersion = cemVersion;
        this.surveyPlusVersion = surveyPlusVersion;
    }
}
