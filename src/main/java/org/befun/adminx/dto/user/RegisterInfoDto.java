package org.befun.adminx.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.AppType;
import org.befun.core.dto.BaseDTO;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterInfoDto extends BaseDTO {

    @NotNull
    @Schema(description = "应用类型")
    private AppType app;
    @NotEmpty(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "手机号只能为11位")
    private String mobile;
    //    @Length(max = 15, min = 6, message = "密码长度在6-14之间")
//    private String password;
    @Schema(description = "明文密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String password;
    @Schema(description = "加密密码，（明文密码和加密密码最少需要一个，优先使用明文密码）")
    private String encryptedPassword;
    private String verifyCode;
    @NotEmpty(message = "姓名不能为空")
    @Length(max = 15, message = "姓名不能超过15位")
    private String name;
    @Length(max = 15, message = "公司名不能超过15位")
    @NotEmpty(message = "公司名不能为空")
    private String companyName;
    @NotEmpty(message = "邮件不能为空")
    @Email(message = "邮件格式错误")
    private String email;
    private String appVersion;
    private String availableDateEnd;
    private String clone = "no";
}
