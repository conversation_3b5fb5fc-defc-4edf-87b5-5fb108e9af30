package org.befun.adminx.dto.user;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.AppVersion;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationOptionalLimitDto {

    @JsonAlias("child_user_limit")
    @JsonProperty("child_user_limit")
    @Schema(description = "子账户数目")
    private Integer childUserLimit;

    @JsonAlias("customer_lifecycle_limit")
    @JsonProperty("customer_lifecycle_limit")
    @Schema(description = "客户旅程数")
    private Integer customerLifecycleLimit;

    @JsonAlias("surveys_limit")
    @JsonProperty("surveys_limit")
    @Schema(description = "问卷数")
    private Integer surveysLimit;

    @JsonAlias("event_rules_limit")
    @JsonProperty("event_rules_limit")
    @Schema(description = "事件预警数")
    private Integer eventRulesLimit;

    @JsonAlias("customer_person_limit")
    @JsonProperty("customer_person_limit")
    @Schema(description = "客户画像数")
    private Integer customerPersonLimit;

    @JsonAlias("bi_dashboard_limit")
    @JsonProperty("bi_dashboard_limit")
    @Schema(description = "BI仪表盘数")
    private Integer biDashboardLimit;

    public void defaultIfNull(Supplier<AppVersion> appVersion) {
        if (childUserLimit == null
                || customerLifecycleLimit == null
                || surveysLimit == null
                || eventRulesLimit == null
                || customerPersonLimit == null
                || biDashboardLimit == null) {
            OrganizationOptionalLimitDto defaultDto = appVersion.get().getOptionalLimit();
            defaultIfNull(childUserLimit, defaultDto.childUserLimit, this::setChildUserLimit);
            defaultIfNull(customerLifecycleLimit, defaultDto.customerLifecycleLimit, this::setCustomerLifecycleLimit);
            defaultIfNull(surveysLimit, defaultDto.surveysLimit, this::setSurveysLimit);
            defaultIfNull(eventRulesLimit, defaultDto.eventRulesLimit, this::setEventRulesLimit);
            defaultIfNull(customerPersonLimit, defaultDto.customerPersonLimit, this::setCustomerPersonLimit);
            defaultIfNull(biDashboardLimit, defaultDto.biDashboardLimit, this::setBiDashboardLimit);
        }
    }

    private void defaultIfNull(Integer value, Integer defaultValue, Consumer<Integer> setDefault) {
        if (value == null) {
            setDefault.accept(defaultValue);
        }
    }

}
