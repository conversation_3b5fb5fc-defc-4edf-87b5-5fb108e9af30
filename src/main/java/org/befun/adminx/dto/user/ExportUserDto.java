package org.befun.adminx.dto.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.service.excel.converter.*;

import java.io.Serializable;

@Getter
@Setter
public class ExportUserDto {

	private String cuid;
	private String openid;
	@ExcelProperty("微信昵称")
	private String wechatNickname;
	@ExcelProperty("手机号")
	private String tel;
	@ExcelProperty("所属网格")
	private String gridId;
	@ExcelProperty("网格名称")
	private String gridName;
	@ExcelProperty("使用积分")
	private String accumulativeScore;
	@ExcelProperty("冻结积分")
	private String freezeScore;
	@ExcelProperty(value = "关注状态",converter = ExcelWechatSubscribeConverter.class)
	private String wechatSubscribe;
	@ExcelProperty(value = "关注时间",converter = ExcelWechatSubscribeTimeConverter.class)
	private String wechatSubscribeTime;
	@ExcelProperty(value = "取关时间",converter = ExcelWechatUnSubscribeTimeConverter.class)
	private String wechatUnsubscribeTime;
	@ExcelProperty("社区等级")
	private String level;
	@ExcelProperty("邀请人数")
	private String numberOfInvited;
	@ExcelProperty(value = "性别",converter = ExcelSexConverter.class)
	private String sex;
	@ExcelProperty("出生日期")
	private String birthday;
	@ExcelProperty(value = "学历",converter = ExcelEducationConverter.class)
	private String education;
	@ExcelProperty("省")
	private String province;
	@ExcelProperty("市")
	private String city;
	@ExcelProperty("区")
	private String area;
	@ExcelProperty("资料填写时间")
	private String createTime;
	@ExcelProperty("学历更新时间")
	private String educationModified;
	@ExcelProperty("GPS更新时间")
	private String gpsModified;
	@ExcelProperty("问卷打开次数")
	private String total;
	@ExcelProperty("提前结束次数")
	private String earlyCompleted;
	@ExcelProperty("配额结束次数")
	private String quotaFull;
	@ExcelProperty("问卷完成次数")
	private String completed;
	@ExcelProperty("问卷完成率")
	private String completedRate;
	@ExcelProperty("问卷通过次数")
	private String auditPass;
	@ExcelProperty("问卷通过率")
	private String passRate;

}
