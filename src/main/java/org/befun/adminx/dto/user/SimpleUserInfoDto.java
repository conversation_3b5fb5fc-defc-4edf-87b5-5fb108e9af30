package org.befun.adminx.dto.user;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.dto.UserDto;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
public class SimpleUserInfoDto extends BaseDTO {
    private Long userId;
    private String username;
    private List<String> roles;

    public UserDto toUserDto() {
        return UserDto.builder()
                .id(userId)
                .username(username)
                .build();
    }
}
