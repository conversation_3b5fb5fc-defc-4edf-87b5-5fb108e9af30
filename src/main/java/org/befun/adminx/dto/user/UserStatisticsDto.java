package org.befun.adminx.dto.user;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/11 17:25
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStatisticsDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    private String date = null;

    @JsonView(ResourceViews.Basic.class)
    private Integer cuid;

    @JsonView(ResourceViews.Basic.class)
    private String total;

    @JsonView(ResourceViews.Basic.class)
    private String completed;

    @JsonView(ResourceViews.Basic.class)
    private String quotaFull;

    @JsonView(ResourceViews.Basic.class)
    private String earlyCompleted;

    @JsonView(ResourceViews.Basic.class)
    private String auditPass;

    @JsonView(ResourceViews.Basic.class)
    private String completedRate;

    @JsonView(ResourceViews.Basic.class)
    private String passRate;
}
