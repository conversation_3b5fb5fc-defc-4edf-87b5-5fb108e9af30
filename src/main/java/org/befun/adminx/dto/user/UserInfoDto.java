package org.befun.adminx.dto.user;

import lombok.*;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.converter.ListConverter;
import org.befun.core.dto.BaseDTO;

import javax.persistence.Convert;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoDto extends BaseDTO {
    private HashMap version;
    private String appTypes;
    private Date availableDateBegin;
    private Date availableDateEnd;
    private String password;
    private String mobile;
    private Long smsBalance;
    private Long aiPoint;
    private HashMap optionalLimit;
    private String type;
    private String aliasName;
    private String remark;
}
