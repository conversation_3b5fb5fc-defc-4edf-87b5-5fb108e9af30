package org.befun.adminx.dto.user;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/11 18:33
 */
@Setter
@Getter
public class SendTemplateDto {
    private List<Long> sourceJourneyMapIds;
    private List<Long> sourceSurveyIds;
    private List<Long> sourceSurveyQuestionIds;
    private List<List<Long>> sourceTemplateQuestionsIds;
    private Long targetOrgId;
    private Long targetUserId;
    private String copyToken = "5fa84f3f9648477387332cb1048d20ff";
}
