package org.befun.adminx.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/15 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WechatNotifyRequestDto {
    @Schema(description = "开发者微信号")
    @JsonProperty(value = "ToUserName")
    private String ToUserName;

    @Schema(description = "发送方帐号（一个OpenID）")
    @JsonProperty(value = "FromUserName")
    private String FromUserName;

    @Schema(description = "消息创建时间 （整型）")
    @JsonProperty(value = "CreateTime")
    private String CreateTime;

    @Schema(description = "消息类型")
    @JsonProperty(value = "MsgType")
    private String MsgType;

    @Schema(description = "事件类型，subscribe(订阅)、unsubscribe(取消订阅)")
    @JsonProperty(value = "Event")
    private String Event;

    @Schema(description = "文本消息内容")
    @JsonProperty(value = "Content")
    private String Content;

    @Schema(description = "地理位置纬度")
    @JsonProperty(value = "Latitude")
    private Float latitude;

    @Schema(description = "地理位置经度")
    @JsonProperty(value = "Longitude")
    private Float longitude;

    @Schema(description = "地理位置精度")
    @JsonProperty(value = "Precision")
    private Float precision;
}
