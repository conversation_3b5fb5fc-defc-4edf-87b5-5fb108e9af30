package org.befun.adminx.dto.wechat;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WechatSignatureResponseDto {
    private String appId;
    private String nonceStr;
    private long timestamp;
    private String url;
    private String signature;

}
