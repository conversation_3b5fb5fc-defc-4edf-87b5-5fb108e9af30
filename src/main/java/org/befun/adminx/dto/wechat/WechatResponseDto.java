package org.befun.adminx.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/13 15:45
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WechatResponseDto {
    @Schema(description = "开发者微信号")
    @JsonProperty(value = "ToUserName")
    private String ToUserName;

    @Schema(description = "发送方帐号（一个OpenID）")
    @JsonProperty(value = "FromUserName")
    private String FromUserName;

    @Schema(description = "消息创建时间 （整型）")
    @JsonProperty(value = "CreateTime")
    private Long CreateTime;

    @Schema(description = "消息类型")
    @JsonProperty(value = "MsgType")
    private String MsgType = "text";

    @Schema(description = "文本消息内容")
    @JsonProperty(value = "Content")
    private String Content;
}
