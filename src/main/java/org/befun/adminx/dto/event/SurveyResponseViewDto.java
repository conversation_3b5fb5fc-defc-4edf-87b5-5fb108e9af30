package org.befun.adminx.dto.event;

import lombok.*;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.task.BaseTaskDetailDto;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/4 17:54
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseViewDto extends BaseTaskDetailDto {

    private Long surveyId;
    private Long responseId;
    private String clientId;
    private String externalUserId;
    private String trackId;
    private String openid;
    private String customerName;
    private SurveyCollectorMethod collectorMethod;
    private ResponseStatus status;


    public SurveyResponseViewDto(SurveyResponse response) {
        this.surveyId = response.getSurveyId();
        this.responseId = response.getId();
        this.clientId = response.getClientId();
        this.externalUserId = response.getExternalUserId();
        this.trackId = response.getTrackId();
        this.openid = response.getOpenid();
        this.customerName = response.getCustomerName();
        this.collectorMethod = response.getCollectorMethod();
        this.status = response.getStatus();
    }

}
