package org.befun.adminx.dto.event;

import org.befun.adminx.dto.worker.EventAdminxChannelOrderQuoted;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderRefund;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderReject;
import org.befun.adminx.dto.worker.EventAdminxChannelOrderStartEnd;

public enum EventType {

    RESPONSE_VIEW("response", EventResponseDto.class),
    RESPONSE_SUBMIT("response", EventResponseDto.class),
    SURVEY_REPORT("survey", EventSurveyReportDto.class),
    SURVEY_MANUAL_CHECK("survey", EventSurveyManualCheckDto.class),

    RESPONSE_SUBMIT_FINAL("response", EventResponseDto.class),
    RESPONSE_DELETE_BY_SURVEY("response", EventResponseDto.class),
    RESPONSE_DELETE_BY_CHANNEL("response", EventResponseDto.class),

    CHANNEL_CREATE("response", EventChannelDto.class),
    CHANNEL_PAUSE("response", EventChannelDto.class),
    CHANNEL_CLOSE("response", EventChannelDto.class),

    ADMINX_CHANNEL_ORDER_REJECT("adminxChannel", EventAdminxChannelOrderReject.class),
    ADMINX_CHANNEL_ORDER_QUOTED("adminxChannel", EventAdminxChannelOrderQuoted.class),
    ADMINX_CHANNEL_ORDER_START_END("adminxChannel", EventAdminxChannelOrderStartEnd.class),
    ADMINX_CHANNEL_ORDER_REFUND("adminxChannel", EventAdminxChannelOrderRefund.class),
    ADMINX_CHANNEL_ORDER_RE_REFUND("adminxChannel", EventAdminxChannelOrderRefund.class),
    ;

    public final String group;
    public final Class<?> paramClass;

    EventType(String group, Class<?> paramClass) {
        this.group = group;
        this.paramClass = paramClass;
    }
}
