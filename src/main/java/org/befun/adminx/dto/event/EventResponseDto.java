package org.befun.adminx.dto.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.task.BaseTaskDetailDto;

@Setter
@Getter
@NoArgsConstructor
public class EventResponseDto extends BaseTaskDetailDto {
    private Long surveyId;
    private Long responseId;
    private Long channelId;
    private boolean finalSubmit;

    public EventResponseDto(Long surveyId) {
        this.surveyId = surveyId;
    }

    public EventResponseDto(Long surveyId, Long responseId) {
        this.surveyId = surveyId;
        this.responseId = responseId;
    }

    public EventResponseDto(Long surveyId, Long responseId, Long channelId) {
        this.surveyId = surveyId;
        this.responseId = responseId;
        this.channelId = channelId;
    }

    public EventResponseDto(Long surveyId, Long responseId, boolean finalSubmit) {
        this.surveyId = surveyId;
        this.responseId = responseId;
        this.finalSubmit = finalSubmit;
    }
}
