package org.befun.adminx.dto.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.task.BaseTaskDetailDto;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderPayDto extends BaseTaskDetailDto {
    private Long orgId;
    private Long userId;
    private Long surveyId;
    private Long channelId;
    private Long rechargeId;
    private Long refundId;
    private String status;
}