package org.befun.adminx.dto.sms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class SmsNotifyTextInfo extends SmsNotifyBaseInfo {
    private String mobile;
    private String content;
    private Map<String, Object> params;

    public SmsNotifyTextInfo(String mobile, String content, Map<String, Object> params) {
        this.mobile = mobile;
        this.content = content;
        this.params = params;
    }
}
