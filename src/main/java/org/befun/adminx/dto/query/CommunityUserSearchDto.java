package org.befun.adminx.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.befun.adminx.constant.WechatSubscribeStatus;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/12 15:42
 */
@Getter
@Setter
@NoArgsConstructor
public class CommunityUserSearchDto{
    @JsonProperty("cuid")
    private Long cuid;
    @JsonProperty("openid")
    private String openid;
    @JsonProperty("nickname")
    private String nickname;
    @JsonProperty("mobile")
    private String mobile;
    @JsonProperty("wechat_subscribe")
    private Integer wechatSubscribe;
    private String responseId;

    public CommunityUserSearchDto(Long cuid, String openid, String nickname, String mobile,Integer wechatSubscribe) {
        this.cuid = cuid;
        this.openid = openid;
        this.nickname = nickname;
        this.mobile = mobile;
        this.wechatSubscribe = wechatSubscribe;
    }
}
