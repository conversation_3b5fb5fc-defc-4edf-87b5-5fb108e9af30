package org.befun.adminx.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.annotation.UserQueryField;
import org.befun.core.dto.query.ResourceCustomQueryDto;

import java.util.Date;


@Getter
@Setter
public class CommunityUserExportQueryDto extends ResourceCustomQueryDto {

    @JsonProperty("id")
    private Long cuid;
    @JsonProperty("nickName")
    private String wechatNickname;
    @JsonProperty("level")
    private Long level;
    @JsonProperty("mobile")
    private String tel;
    @JsonProperty("openId")
    private String openId;
    @JsonProperty("wechatSubscribe")
    private Integer wechatSubscribe;
    @JsonProperty("wechatSubscribeTime_lte")
    private String wechatSubscribeTime_lte;
    @JsonProperty("wechatSubscribeTime_gte")
    private String wechatSubscribeTime_gte;
    @JsonProperty("wechatUnSubscribeTime_lte")
    private String wechatUnSubscribeTime_lte;
    @JsonProperty("wechatUnSubscribeTime_gte")
    private String wechatUnSubscribeTime_gte;
    @JsonProperty("createTime_lte")
    private String createTime_lte;
    @JsonProperty("createTime_gte")
    private String createTime_gte;

    @JsonProperty("additionalCreateTime_lte")
    private String additionalCreateTime_lte;
    @JsonProperty("additionalCreateTime_gte")
    private String additionalCreateTime_gte;
    @JsonProperty("gender")
    private Integer sex;
    @JsonProperty("education")
    private Integer education;
    @JsonProperty("birthday_lte")
    private String birthday_lte;
    @JsonProperty("birthday_gte")
    private String birthday_gte;
    @JsonProperty("province_in")
    private String province_in;
    @JsonProperty("city_in")
    private String city_in;

//    @JsonProperty("date_gte")
//    private String date_gte;
//
//    @JsonProperty("date_lte")
//    private String date_lte;
//
    @JsonProperty("total")
    private String total;
    
    @JsonProperty("earlyCompleted")
    private String earlyCompleted;
    
    @JsonProperty("quotaFull")
    private String quotaFull;
    
    @JsonProperty("completed")
    private String completed;
    
    @JsonProperty("auditPass")
    private String auditPass;
    
    @JsonProperty("completedRate")
    private String completedRate;
    
    @JsonProperty("passRate")
    private String passRate;

}
