package org.befun.adminx.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.annotation.UserQueryField;
import org.befun.core.dto.query.ResourceCustomQueryDto;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/12 15:42
 */
@Getter
@Setter
public class CommunityUserQueryDto extends ResourceCustomQueryDto {

    @JsonProperty("id")
    private Long id;
    @JsonProperty("gridId")
    private Long gridId;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("nickName")
    private String nickName;
    @JsonProperty("level")
    private Long level;
    @JsonProperty("mobile")
    private String mobile;
    @JsonProperty("openId")
    private String openId;
    @JsonProperty("wechatSubscribe")
    private Integer wechatSubscribe;
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("wechatSubscribeTime_lte")
    private Date wechatSubscribeTimeLte;
    @JsonProperty("wechatSubscribeTime_gte")
    private Date wechatSubscribeTimeGte;
    @JsonProperty("wechatUnSubscribeTime_lte")
    private Date wechatUnSubscribeTimeLte;
    @JsonProperty("wechatUnSubscribeTime_gte")
    private Date wechatUnSubscribeTimeGte;
    @JsonProperty("createTime_lte")
    private Date createTimeLte;
    @JsonProperty("createTime_gte")
    private Date createTimeGte;

    @JsonProperty("additionalCreateTime_lte")
    private Date additionalCreateTimeLte;
    @JsonProperty("additionalCreateTime_gte")
    private Date additionalCreateTimeGte;
    @JsonProperty("gender")
    private Integer gender;
    @JsonProperty("education")
    private Integer education;
    @JsonProperty("birthday_lte")
    private Date birthdayLte;
    @JsonProperty("birthday_gte")
    private Date birthdayGte;
    @JsonProperty("province_in")
    private String provinceIn;
    @JsonProperty("city_in")
    private String cityIn;
    @JsonProperty("numberOfInvited")
    private String numberOfInvited;

    @UserQueryField
    @JsonProperty("date_gte")
    private String dateGte;
    @UserQueryField
    @JsonProperty("date_lte")
    private String dateLte;
    @UserQueryField
    @JsonProperty("total")
    private String total;
    @UserQueryField
    @JsonProperty("earlyCompleted")
    private String earlyCompleted;
    @UserQueryField
    @JsonProperty("quotaFull")
    private String quotaFull;
    @UserQueryField
    @JsonProperty("completed")
    private String completed;
    @UserQueryField
    @JsonProperty("auditPass")
    private String auditPass;
    @UserQueryField
    @JsonProperty("completedRate")
    private String completedRate;
    @UserQueryField
    @JsonProperty("passRate")
    private String passRate;

}
