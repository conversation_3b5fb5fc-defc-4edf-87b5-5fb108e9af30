package org.befun.adminx.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.annotation.UserQueryField;
import org.befun.adminx.constant.SubmitStatus;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.constant.WechatSubscribeStatus;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.core.dto.query.ResourceCustomQueryDto;

import javax.persistence.Convert;
import java.util.Date;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/6/15 15:42
 */
@Getter
@Setter
public class ResendTaskQueryDto extends ResourceCustomQueryDto {

    private Boolean status;
    private Integer sendCount;
    private Integer sendWechatCount;
    private Integer sendMessageCount;
    private TaskSendMethod sendMethod;
    private SubmitStatus submitStatus;
    private WechatSubscribeStatus subscribeStatus;
    private Boolean phoneStatus;
    private String failMsg;
    private String sendStatus_in;

}
