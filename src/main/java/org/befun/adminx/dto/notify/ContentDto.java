package org.befun.adminx.dto.notify;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ContentDto {

        private String title = "样本订单通知";
        private String contacts;
        private String surveyId;
        private String surveyTitle;
        private String channelId;
        private String totalPrice;
        private String createTime;

        public ContentDto(String contacts, String surveyId, String surveyTitle, String channelId, String totalPrice, String createTime) {
                this.contacts = contacts;
                this.surveyId = surveyId;
                this.surveyTitle = surveyTitle;
                this.channelId = channelId;
                this.totalPrice = totalPrice;
                this.createTime = createTime;
        }
}
