package org.befun.adminx.dto.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.TaskSendMethod;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ResendDto {

    @Schema(description = "记录id")
    private List<Long> logIds;

    private TaskSendMethod taskSendMethod;

    @Schema(description = "模板id")
    private Long thirdPartTemplateId;

    @Schema(description = "模板内容")
    private Map<String, Object> params;

    private Map<String, Object> _condition;

}
