package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.SurveyQuotaStatus;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.adminx.entity.survey.AdminxQuota;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class AdminxQuotaExtDto  extends BaseEntityDTO<AdminxQuota> {

    public AdminxQuotaExtDto() {
    }

    public AdminxQuotaExtDto(AdminxQuota entity) {
        super(entity);
    }


    @Schema(description = "问卷状态")
    @JsonView(ResourceViews.Basic.class)
    private SurveyStatus surveyStatus;

    @Schema(description = "问卷标题")
    @JsonView(ResourceViews.Basic.class)
    private String surveyTitle;

    @Schema(description = "是否开启cem配额")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableQuota;

    @Schema(description = "cem配额状态 0待计算 1计算中 2计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus cemQuotaStatus;

    @Schema(description = "是否开启adminx配额")
    @JsonView(ResourceViews.Basic.class)
    private Boolean adminxEnableQuota;

    @Schema(description = "adminx配额状态 0待计算 1计算中 2计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus adminxQuotaStatus;

}
