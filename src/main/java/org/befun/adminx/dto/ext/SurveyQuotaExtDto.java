package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuota;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class SurveyQuotaExtDto extends BaseEntityDTO<SurveyQuota> {

    public SurveyQuotaExtDto() {
    }

    public SurveyQuotaExtDto(SurveyQuota entity) {
        super(entity);
    }

    @Schema(description = "当前配额")
    @JsonView(ResourceViews.Basic.class)
    private Long current = 0L;

    @Schema(description = "问卷状态")
    @JsonView(ResourceViews.Basic.class)
    private SurveyStatus surveyStatus;

    @Schema(description = "问卷标题")
    @JsonView(ResourceViews.Basic.class)
    private String surveyTitle;

    @Schema(description = "adminx配额开关")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableQuota;

}
