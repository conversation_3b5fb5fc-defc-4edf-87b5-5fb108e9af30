package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.dto.user.UserStatisticsDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/6 5:04 下午
 */
@Getter
@Setter
public class CommunityUserExDto extends BaseEntityDTO<CommunityUser> {

    public CommunityUserExDto() {
    }

    public CommunityUserExDto(CommunityUser entity) {
        super(entity);
    }
    @Schema(description = "会员数量")
    @JsonView(ResourceViews.Detail.class)
    private Long countOfUsers = 0L;

    @Schema(description = "总奖励金额")
    @JsonView(ResourceViews.Detail.class)
    private Long sumOfReward = 0L;

    @Schema(description = "登录token")
    @JsonView(ResourceViews.Detail.class)
    private String token = null;

    @Schema(description = "统计数据")
    @JsonView(ResourceViews.Basic.class)
    private UserStatisticsDto statistics = null;

    @Schema(description = "省")
    @JsonView(ResourceViews.Basic.class)
    private String province = null;

    @Schema(description = "市")
    @JsonView(ResourceViews.Basic.class)
    private String city = null;

    @Schema(description = "区")
    @JsonView(ResourceViews.Basic.class)
    private String area = null;

    @Schema(description = "是否一年内修改")
    @JsonView(ResourceViews.Basic.class)
    private boolean educationModifiedOne;

    @Schema(description = "邀请链接")
    @JsonView(ResourceViews.Basic.class)
    private String inviteUrl;

    @Schema(description = "是否网格员")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isGrid;

    @Schema(description = "网格名称")
    @JsonView(ResourceViews.Basic.class)
    private String gridName;
}
