package org.befun.adminx.dto.ext;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.adminx.entity.DeliveryTaskDto;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 16:50
 */
@Getter
@Setter
public class SendTaskExDto extends DeliveryTaskDto {

    private SendTaskType sendType = SendTaskType.ALL;
    private Boolean enablePush = false;
    private Boolean enableInvite = false;
    private String blackList;
    private String whiteList;
    private List<SamplesDto> samples = new ArrayList<>();
}
