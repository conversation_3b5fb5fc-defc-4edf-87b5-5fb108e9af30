package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.adminx.entity.DeliveryTask;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/22 10:48
 */
@Getter
@Setter
public class DeliveryTaskExtDto extends BaseEntityDTO<DeliveryTask> {
    public DeliveryTaskExtDto() {
    }

    public DeliveryTaskExtDto(DeliveryTask entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    private SendTaskType sendType = SendTaskType.ALL;
    @JsonView(ResourceViews.Basic.class)
    private Boolean enablePush = false;
    @JsonView(ResourceViews.Basic.class)
    private String blackList = null;
    @JsonView(ResourceViews.Basic.class)
    private String whiteList = null;
    @JsonView(ResourceViews.Basic.class)
    private List<SamplesDto> samples = new ArrayList<>();
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableAdminxQuota = false;
}
