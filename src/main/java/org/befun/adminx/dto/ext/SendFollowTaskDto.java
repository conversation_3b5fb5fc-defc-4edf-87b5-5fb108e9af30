package org.befun.adminx.dto.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.task.SendSelectedDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.dto.wechat.WechatNotifyTemplateDto;
import org.befun.adminx.utils.DateFormatter;

import java.util.*;

@Getter
@Setter
public class SendFollowTaskDto{

    private String surveyId;

    private List<Long> cuidList = new ArrayList<>();

    private TaskSendMethod taskSendMethod;

    private Date sendTime;

    @Schema(description = "模板id")
    private Long thirdPartTemplateId;

    @Schema(description = "模板内容")
    private Map<String, Object> params;


    public SendSelectedDto mapToSendSelect(SendTaskExDto task, List<CommunityUserSearchDto> userList, TemplateInfoDto template, String baseLineSurveyId) {
        SendSelectedDto dto = new SendSelectedDto();
        dto.setTask(task);
        dto.setUserList(userList);
        dto.setType(SendType.SEND_SURVEY);
        dto.setBaseLineSurveyId(baseLineSurveyId);
        if (task != null) {
            dto.setSurveyId(Long.parseLong(task.getSid()));
            dto.setTaskId(task.getId());
        }
        if (sendTime == null ) {
            dto.setSendTime(DateFormatter.getStringTime(new Date()));
        } else {
            dto.setSendTime(DateFormatter.getStringTime(sendTime));
        }
        dto.setContentTemplate(params);
        dto.setTemplate(template);
        return dto;
    }

    public SendSelectedDto mapToSendSearch(SendTaskExDto task, List<CommunityUserSearchDto> userList, TemplateInfoDto template) {
        SendSelectedDto dto = new SendSelectedDto();
        dto.setTask(task);
        dto.setUserList(userList);
        dto.setType(SendType.SEND_SURVEY);
        if (task != null) {
            dto.setSurveyId(Long.parseLong(task.getSid()));
            dto.setTaskId(task.getId());
        }
        if (sendTime == null ) {
            dto.setSendTime(DateFormatter.getStringTime(new Date()));
        } else {
            dto.setSendTime(DateFormatter.getStringTime(sendTime));
        }

        Map<String,Object> params = new HashMap<>();
        params.put("first","");
        params.put("keyword1",task.getName());
        params.put("keyword2",DateFormatter.getStringDate(task.getStartTime()));
        params.put("keyword3",DateFormatter.getStringDate(task.getOverTime()));
        params.put("remark",String.format("答题获取%d元现金，名额有限速来天大哦~",(int)(task.getScore() / 100)));

        dto.setContentTemplate(params);
        dto.setTemplate(template);
        return dto;

    }


}
