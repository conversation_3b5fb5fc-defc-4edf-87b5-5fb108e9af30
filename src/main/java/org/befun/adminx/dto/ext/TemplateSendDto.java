package org.befun.adminx.dto.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.adminx.entity.DeliveryTaskDto;
import org.befun.task.BaseTaskDetailDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Getter
@Setter
public class TemplateSendDto {
    @Schema(description = "模板id",required = true)
    private String thirdPartTemplateId;
    @Schema(description = "模板内容")
    private Map<String, Object> params;
    @Schema(description = "投放设置")
    private SendTaskType sendType = SendTaskType.ALL;
    @Schema(description = "指定人群")
    private List<SamplesDto> samples = new ArrayList<>();
}
