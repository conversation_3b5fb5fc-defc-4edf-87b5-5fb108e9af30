package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.dto.channel.ChannelRefundInfo;
import org.befun.adminx.entity.SampleOrder;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/11 17:22
 */
@Setter
@Getter
public abstract class SampleOrderExtDto extends BaseEntityDTO<SampleOrder> {

    public SampleOrderExtDto() {
    }

    public SampleOrderExtDto(SampleOrder entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "问卷状态", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean surveyStatus = true;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "企业代码", example = "", jsonView = ResourceViews.Basic.class)
    private String orgCode;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "企业名称", example = "", jsonView = ResourceViews.Basic.class)
    private String orgName;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "手机号", example = "", jsonView = ResourceViews.Basic.class)
    private String mobile;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "邮箱", example = "", jsonView = ResourceViews.Basic.class)
    private String email;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "账号姓名", example = "", jsonView = ResourceViews.Basic.class)
    private String realName;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "渠道订单状态：未设置、待报价(none)、待付款(init)、已付款(success)、已退款(refund)", jsonView = ResourceViews.Basic.class)
    private String orderStatus;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "支付时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date payTime;

    @JsonView(ResourceViews.Basic.class)
    private Boolean needRefund;

}
