package org.befun.adminx.dto.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.dto.sample.SamplesDto;
import org.befun.task.BaseTaskDetailDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Getter
@Setter
public class TemplateSendTaskDto extends BaseTaskDetailDto {
    @Schema(description = "模板id")
    private String thirdPartTemplateId;
    @Schema(description = "模板内容")
    private Map<String, Object> params;
    @Schema(description = "投放设置")
    private SendTaskType sendType = SendTaskType.ALL;
    @Schema(description = "指定人群")
    private List<SamplesDto> samples = new ArrayList<>();
    @Schema(description = "问卷地址")
    private String surveyUrl;
    @Schema(description = "发送记录id")
    private Long sendRecordId;
    @Schema(description = "任务id")
    private Long taskId;
}
