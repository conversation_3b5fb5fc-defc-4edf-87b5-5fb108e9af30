package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.dto.survey.SimpleTemplateQuestionDto;
import org.befun.adminx.entity.survey.TemplateGroup;
import org.befun.adminx.entity.survey.TemplateSurveyQuestion;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class TemplateGroupExtDto  extends BaseEntityDTO<TemplateGroup> {

    public TemplateGroupExtDto() {
    }

    public TemplateGroupExtDto(TemplateGroup entity) {
        super(entity);
    }

    @Schema(description = "题库")
    @JsonView(ResourceViews.Basic.class)
    private List<SimpleTemplateQuestionDto> questions = new ArrayList<>();

}
