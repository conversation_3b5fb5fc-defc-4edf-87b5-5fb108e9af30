package org.befun.adminx.dto.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.entity.CustomerStatistics;
import org.befun.adminx.entity.JourneyMap;
import org.befun.adminx.entity.XmPlusUser;
import org.befun.adminx.entity.survey.Survey;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/11 15:07
 */
@Getter
@Setter
public class XmPlusUserExDto extends BaseEntityDTO<XmPlusUser> {
    public XmPlusUserExDto() {
    }

    public XmPlusUserExDto(XmPlusUser entity) {
        super(entity);
    }

    @Schema(description = "企业名称")
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Schema(description = "模板问卷")
    @JsonView(ResourceViews.Basic.class)
    private List<Survey> surveys;

    @Schema(description = "模板旅程")
    @JsonView(ResourceViews.Basic.class)
    private List<JourneyMap> journeyMaps;

    @DtoProperty(description = "企业统计数据", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private CustomerStatistics customerStatistics = null;

    @JsonView(ResourceViews.Basic.class)
    private Long smsBalance = 0l;

    @JsonView(ResourceViews.Basic.class)
    private Long aiPoint = 0l;

    @JsonView(ResourceViews.Basic.class)
    private List<String> appTypes = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    private String realMobile = "";

    @JsonView(ResourceViews.Basic.class)
    private String realEmail = "";

    @JsonView(ResourceViews.Basic.class)
    private boolean surveyContentAudit = true;
}
