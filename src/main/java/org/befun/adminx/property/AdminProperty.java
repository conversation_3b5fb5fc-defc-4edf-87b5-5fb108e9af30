package org.befun.adminx.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "befun.admin")
@Getter
@Setter
public class AdminProperty {

    private String surveyPlusDbName;
    private String surveyPlusUrl;
    private String xmPlusUrl;
}
