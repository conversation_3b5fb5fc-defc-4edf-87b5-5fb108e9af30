package org.befun.adminx.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "survey.quota.sync")
public class QuotaProperties {

    private int maxThreads = 8;
    private int sizePerPage = 100;
    private Duration progressExpire = Duration.ofHours(1);
}
