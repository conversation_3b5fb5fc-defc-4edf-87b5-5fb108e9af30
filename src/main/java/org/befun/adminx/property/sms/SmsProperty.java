package org.befun.adminx.property.sms;

import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.property.sms.SmsProviderProperty;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = Extensions.SMS_PREFIX)
@Getter
@Setter
public class SmsProperty {
    private boolean enable = true;
    private String vendor = "chuanglan";
    private boolean enableFeige = true;
    private boolean enableAliyun = false;
    private boolean enableSocket = false;
    private boolean enableActivemq = false;
    private boolean enableChuanglan = false;

    @NestedConfigurationProperty
    private List<SmsProviderProperty> providers = new ArrayList<>();
}