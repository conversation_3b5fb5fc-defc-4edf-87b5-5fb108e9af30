package org.befun.adminx.property.sms;

import lombok.Getter;
import lombok.Setter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SmsTemplateProperty {

    /* template name */
    private String name;            // verify-code,event-action-warning,event-action-cooperation,journey-indicator-warning,PLATFORM_FEEDBACK,PLATFORM_EVENT_ACTION,PLATFORM_RESEARCH
    private String id;              // 模板id, 第三方平台的模板id

    /* template content */
    private String content;         // 尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。
    private String parameters;      // [{"name": "customer.username", "type": "common", "title": "姓名"}]
    private String pattern;         // ^(.+)，您好！您收到一条(.+)预警事件：(.+)，请尽快登录体验家XM处理！$
    private String variables;       // nickname,level,event
    private String originTemplate;  // 第三方平台的模板的原始内容


    /* template signature */
    private String signature;       // 123456 如果没有配置，则使用provider.signature
    private String realSignature;   // 【瀚一数据】如果没有配置，则使用provider.realSignature


}