package org.befun.adminx.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.extension.property.WeChatCpProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "befun.extension.wechat-cp")
public class WechatCpCommunityProperties extends WeChatCpProperty {
    private Community community;
    private String unionIdCron;

    @Getter
    @Setter
    public static class Community {
        private String sexTagName;
        private String regionTagName;
        private String educationTagName;
        private String birthYearTagName;
        private Integer birthYearTagBefore;
        private Integer birthYearTagAfter;
    }
}
