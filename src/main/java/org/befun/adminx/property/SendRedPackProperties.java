package org.befun.adminx.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(value = "survey.lottery.red-pack")
public class SendRedPackProperties {

    private String sendName = "调研家社区";// 商户名称
    private int totalNum = 1;// 红包发放总人数
    private String wishing = "提现";// 红包祝福语
    private String actName = "现金提现";// 活动名称
    private String remark;// 备注
    private String sceneId;//场景id 非必填
    private String riskInfo;//活动信息 非必填


    //未领取退还红包时间 单位：分钟
    private int refundRedPack = 25 * 60;

    //单个红包最小金额默认1元、最大金额200元，单位分
    private int minMoney = 100;
    private int maxMoney = 20000;

    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId = "wx38eb";

    /**
     * 商家转账报备信息活动名称
     */
    private String activityName = "参与问卷答题，获取红包奖励";

    /**
     * 商家转账报备信息活动内容
     */
    private String activityContent = "参与问卷答题，获取红包奖励";

    private String transferBillPrefix = "transfer-bill";

    private String callBackUrl = "https://play.svix.com/view/e_Bm4Ho5aXFWF30WDsMgXbYjqiIlx/31GUkDfNq9MYM2bQBN6dlmaFRVq";
}

























