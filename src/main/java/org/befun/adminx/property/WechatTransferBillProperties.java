package org.befun.adminx.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(value = "befun.wechat-transfer")
public class WechatTransferBillProperties {
    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    private String transferSceneId = "1000";

    /**
     * 商家转账报备信息活动名称
     */
    private String activityName = "参与问卷答题，获取红包奖励";

    /**
     * 商家转账报备信息活动内容
     */
    private String activityContent = "参与问卷答题，获取红包奖励";
    private String transferRemark = "参与问卷答题，获取红包奖励";

    private String callBackUrl = "https://play.svix.com/view/e_Bm4Ho5aXFWF30WDsMgXbYjqiIlx/31GUkDfNq9MYM2bQBN6dlmaFRVq";


}

























