package org.befun.adminx.security;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.service.CommunityUserSessionService;
import org.befun.adminx.service.JwtService;
import org.befun.core.dto.UserDto;
import org.befun.core.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WechatAuthTokenFilter extends OncePerRequestFilter {
    /*公众号请求接口验证*/
    private static final String TOKEN = "Token";

    @Autowired
    private CommunityUserSessionService sessionService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader(TOKEN);
        if (!Strings.isNullOrEmpty(token)) {
            Boolean auth = sessionService.verifyToken(token);
            if(auth){
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(true, token);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }
        filterChain.doFilter(request, response);
    }
}
