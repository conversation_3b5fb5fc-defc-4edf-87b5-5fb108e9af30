package org.befun.adminx.security;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.user.SimpleUserInfoDto;
import org.befun.adminx.service.JwtService;
import org.befun.core.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AdminAuthTokenFilter extends OncePerRequestFilter {
    /*后台验证*/
    private static final String AdminToken = "AdminToken";

    @Autowired
    private JwtService jwtService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String adminToken = request.getHeader(AdminToken);
        if (!Strings.isNullOrEmpty(adminToken)) {
            SimpleUserInfoDto user = jwtService.decode(adminToken);
            if (user != null) {
                UserPrincipal principal = new UserPrincipal(user.toUserDto());
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, adminToken);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }
        filterChain.doFilter(request, response);
    }
}
