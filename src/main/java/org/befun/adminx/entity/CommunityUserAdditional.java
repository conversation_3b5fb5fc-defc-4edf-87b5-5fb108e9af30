package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.adminx.converter.EducationConverter;
import org.befun.adminx.converter.SexConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/13 14:13
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "community_user_additional")
@DtoClass(includeAllFields = true)
@AttributeOverride(name = "id", column = @Column(name = "cuid"))
public class CommunityUserAdditional extends BaseEntity {

    @Convert(converter = SexConverter.class)
    @Column(name = "sex")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "性别", example = "", jsonView = ResourceViews.Basic.class)
    private String gender;

    @Convert(converter = EducationConverter.class)
    @Column(name = "education")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "教育程度", example = "", jsonView = ResourceViews.Basic.class)
    private String education;

    @Column(name = "marital")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "婚姻状况", example = "", jsonView = ResourceViews.Basic.class)
    private Integer marital;

    @Column(name = "birthday")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DtoProperty(description = "出生年月日", example = "", jsonView = ResourceViews.Basic.class)
    private Date birthday;

    @Column(name = "education_modified")
    @Temporal(TemporalType.DATE)
    @JsonView(ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DtoProperty(description = "学历修改时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date educationModified;

//    @Convert(converter = PostalCodeConverter.class)
    @Column(name = "province_code")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "省", example = "", jsonView = ResourceViews.Basic.class)
    private String provinceCode;

//    @Convert(converter = PostalCodeConverter.class)
    @Column(name = "city_code")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "市", example = "", jsonView = ResourceViews.Basic.class)
    private String cityCode;

//    @Convert(converter = PostalCodeConverter.class)
    @Column(name = "area_code")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "区", example = "", jsonView = ResourceViews.Basic.class)
    private String areaCode;

    @Column(name = "latitude")
    @DtoProperty(description = "地理位置纬度", example = "", jsonView = ResourceViews.Detail.class)
    private Float latitude;

    @Column(name = "longitude")
    @DtoProperty(description = "地理位置经度", example = "", jsonView = ResourceViews.Detail.class)
    private Float longitude;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "省", example = "", jsonView = ResourceViews.Basic.class)
    private String province;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "市", example = "", jsonView = ResourceViews.Basic.class)
    private String city;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "区", example = "", jsonView = ResourceViews.Basic.class)
    private String area;

    @Column(name = "gps_modified")
    @Temporal(TemporalType.DATE)
    @JsonView(ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DtoProperty(description = "gps定位时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date gpsModified;

    public Integer educationConverter() {
        Integer educationCode = 0;
        switch (education){
            case "小学及以下":
                educationCode = 1;break;
            case "初中":
                educationCode = 2;break;
            case "高中/中专/技校/职高":
                educationCode = 3;break;
            case "大专":
                educationCode = 4;break;
            case "大学本科":
                educationCode = 5;break;
            case "硕士及以上":
                educationCode = 6;break;
            default:
        }
        return educationCode;
    }

    public String educationTextConverter() {
        String educationText = null;
        switch (education) {
            case "1":
                educationText = "小学及以下";
                break;
            case "2":
                educationText = "初中";
                break;
            case "3":
                educationText = "高中/中专/技校/职高";
                break;
            case "4":
                educationText = "大专";
                break;
            case "5":
                educationText = "大学本科";
                break;
            case "6":
                educationText = "硕士及以上";
                break;
            default:
        }
        return educationText;
    }

//    public String getProvince() {
//        return CityAndCodeUtil.getCityByCode(this.provinceCode);
//    }
//
//    public String getCity() {
//        return CityAndCodeUtil.getCityByCode(this.cityCode);
//    }
//
//    public String getArea() {
//        return CityAndCodeUtil.getCityByCode(this.areaCode);
//    }

}
