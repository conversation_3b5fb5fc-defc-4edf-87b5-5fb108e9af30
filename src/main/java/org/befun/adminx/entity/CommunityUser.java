package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import org.befun.adminx.constant.WechatSubscribeStatus;
import org.befun.adminx.converter.StringTimestampConverter;
import org.befun.adminx.dto.ext.CommunityUserExDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "community_users")
@DtoClass(includeAllFields = true, superClass = CommunityUserExDto.class)
@AttributeOverride(name = "id", column = @Column(name = "cuid"))
public class CommunityUser extends BaseEntity {

    @Column(name = "grid_id")
    @DtoProperty(description = "所属网格id", example = "", jsonView = ResourceViews.Basic.class)
    private String gridId;

//    @Column(name = "is_grid")
//    @DtoProperty(description = "是否网格员", example = "", jsonView = ResourceViews.Basic.class)
//    private Boolean isGrid = false;

    @Column(name = "join_grid_time")
    @DtoProperty(description = "加入网格时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date joinGridTime = null;

    @Column(name = "users_name")
    @DtoProperty(description = "用户名", example = "", jsonView = ResourceViews.Basic.class)
    private String userName = "";

    @Column(name = "nickname")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "微信昵称", example = "", jsonView = ResourceViews.Basic.class)
    private String nickName = "";

    @Column(name = "avatar")
    @DtoProperty(description = "头像", example = "", jsonView = ResourceViews.Detail.class)
    private String avatar;

    @Column(name = "level")
    @DtoProperty(description = "等级", example = "1", jsonView = ResourceViews.Basic.class)
    private Long level = 0l;

    @Column(name = "invite_id")
    @DtoProperty(description = "邀请人id", example = "1", jsonView = ResourceViews.Detail.class)
    private Long inviteId = 0l;

    @Column(name = "need_surveys")
    @DtoProperty(description = "升到下一级还需要填答的问卷数量", example = "", jsonView = ResourceViews.Detail.class)
    private Long needSurveys = 3l;

    @Column(name = "email")
    @DtoProperty(description = "email", example = "", jsonView = ResourceViews.Basic.class)
    private String email = "";

    @Column(name = "tel")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "mobile", example = "", jsonView = ResourceViews.Basic.class)
    private String mobile = "";

    @Column(name = "wechat_nickname")
    @DtoProperty(description = "wechatNickName", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatNickName = "";

    @Column(name = "openid")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "openId", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @Column(name = "user_score")
    @DtoProperty(description = "积分", example = "0", jsonView = ResourceViews.Basic.class)
    private Integer userScore = 0;

    @Column(name = "freeze_score")
    @DtoProperty(description = "冻结积分", example = "0", jsonView = ResourceViews.Basic.class)
    private Integer freezeScore = 0;

    @Column(name = "accumulative_score")
    @DtoProperty(description = "累计积分", example = "0", jsonView = ResourceViews.Basic.class)
    private Integer accumulativeScore = 0;

    @Column(name = "award_score")
    @DtoProperty(description = "升级的奖励积分", example = "0", jsonView = ResourceViews.Basic.class)
    private Integer awardScore = 0;

    @Column(name = "surveys_completed_count")
    @DtoProperty(description = "问卷的审核通过数量", example = "0", jsonView = ResourceViews.Basic.class)
    private Integer surveysCompletedCount = 0;

    @Column(name = "survey_count")
    @DtoProperty(description = "完成问卷的数量", example = "0", jsonView = ResourceViews.Basic.class)
    private Long surveyCount = 0l;

    @Column(name = "personal_info_completed")
    @DtoProperty(description = "个人资料填写完整，Y为完整，N为不完整", example = "N", jsonView = ResourceViews.Detail.class)
    private String personalInfoCompleted = "N";

    @Column(name = "account_info_completed")
    @DtoProperty(description = "账号信息填写完整，Y为完整，N为不完整", example = "N", jsonView = ResourceViews.Detail.class)
    private String accountInfoCompleted = "N";

    @Column(name = "password")
    @DtoProperty(description = "密码", example = "", jsonView = ResourceViews.Basic.class)
    private String passWord = "$2y$10$Gtgbho3ULhDZrsI9wzMHpO2ZOcd54uHea51RF/prW/2FQxmU2Cvca";

    @Column(name = "token")
    @DtoProperty(description = "登录令牌", example = "", jsonView = ResourceViews.Basic.class)
    private String token = "";

    @Column(name = "wechat_province")
    @DtoProperty(description = "省", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatProvince = "";

    @Column(name = "wechat_city")
    @DtoProperty(description = "城市", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatCity = "";

    @Column(name = "wechat_country")
    @DtoProperty(description = "国家", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatCountry = "";

    @Column(name = "wechat_headimgurl")
    @DtoProperty(description = "头像", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatHeadImgUrl = "";

    @Column(name = "wechat_privilege")
    @DtoProperty(description = "特权信息", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatPrivilege = "";

    @Column(name = "wechat_unionid")
    @DtoProperty(description = "unionid", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatUnionid = "";

    @Column(name = "wechat_subscribe")
    @DtoProperty(description = "用户是否订阅该公众号标识，值为0取关，1关注", example = "", jsonView = ResourceViews.Basic.class)
    private WechatSubscribeStatus wechatSubscribe = WechatSubscribeStatus.UN_SUBSCRIBE;

    @Convert(converter = StringTimestampConverter.class)
    @Column(name = "wechat_subscribe_time")
    @DtoProperty(description = "用户关注时间，时间戳", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatSubscribeTime = null;

    @Convert(converter = StringTimestampConverter.class)
    @Column(name = "wechat_unsubscribe_time")
    @DtoProperty(description = "用户取注时间，时间戳", example = "", jsonView = ResourceViews.Basic.class)
    private String wechatUnSubscribeTime = null;

    @Column(name = "number_of_invited")
    @DtoProperty(description = "已邀请人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer numberOfInvited = 0;

    @Column(name = "status")
    @DtoProperty(description = "状态，0为删除，1为正常，2为禁用", example = "", jsonView = ResourceViews.Basic.class)
    private Integer status = 1;

    @Column(name = "invite_award")
    @DtoProperty(description = "是否领取邀请奖励 1表示已经领取", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean inviteAward = false;

    @Column(name = "guide_info")
    @DtoProperty(description = "用户指导完成状态", example = "", jsonView = ResourceViews.Detail.class)
    private String guideInfo;

    @Column(name = "remark")
    @Size(max = 500, message = "文本长度超多限制")
    @DtoProperty(description = "网格员备注", example = "", jsonView = ResourceViews.Detail.class)
    private String remark;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cuid")
//    @NotFound(action = NotFoundAction.IGNORE)
    @DtoProperty(description = "社区用户个人资料", example = "0", jsonView = ResourceViews.Basic.class)
    private CommunityUserAdditional additional = null;

    /**
     * *初始化微信用户
     * @param cuid
     * @param inviteId
     * @param userInfo
     * @return
     */
    public CommunityUser initWechatUser(Long cuid, Long inviteId, WxOAuth2UserInfo userInfo, WechatSubscribeRecordDto subscribeRecordDto) {
        this.setId(cuid);
        this.setOpenId(userInfo.getOpenid());
        this.setNickName(userInfo.getNickname());
        this.setAvatar(userInfo.getHeadImgUrl());
        this.setInviteId(inviteId);
        if (subscribeRecordDto != null) {
            if (subscribeRecordDto.getType()) {//关注
                this.setWechatSubscribe(WechatSubscribeStatus.SUBSCRIBE);
                this.setWechatSubscribeTime(String.valueOf(subscribeRecordDto.getCreateTime().getTime() /1000));
            } else {//取关
                this.setWechatSubscribe(WechatSubscribeStatus.UN_SUBSCRIBE);
                this.setWechatUnSubscribeTime(String.valueOf(subscribeRecordDto.getCreateTime().getTime() /1000));
            }
        }
        return this;
    }

    public CommunityUser(Long cuid) {
        this.id = cuid;
    }
}


