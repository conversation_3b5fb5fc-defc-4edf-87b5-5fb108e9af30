package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * The class description
 *
 * <AUTHOR>
 */

@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_fingerprint")
@DtoClass()
public class SurveyFingerprint extends BaseEntity {
    private static final long serialVersionUID = 1529685098268257190L;

    @Schema(description = "问卷id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "s_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String surveyId;

    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "指纹", required = true)
    @Column(name = "fingerprint")
    private String fingerprint = null;

    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "城市", required = true)
    @Column(name = "city")
    private String city = null;

    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "时间戳", required = true)
    @Column(name = "ts")
    private Timestamp timestamp = null;
}
