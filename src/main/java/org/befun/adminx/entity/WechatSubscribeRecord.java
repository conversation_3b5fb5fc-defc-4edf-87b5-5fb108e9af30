package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@Table(name = "wechat_subscribe_record")
@DtoClass()
public class WechatSubscribeRecord extends BaseEntity {

    @DtoProperty(description = "cuid", example = "", jsonView = ResourceViews.Basic.class)
    private Long cuid;

    @Column(name = "openid")
    @DtoProperty(description = "微信openid", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @DtoProperty(description = "0取关，1关注", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean type;

    public WechatSubscribeRecord(Long cuid, String openid, Boolean type) {
        this.cuid = cuid;
        this.openId = openid;
        this.type = type;
    }
    public WechatSubscribeRecord(){}
}