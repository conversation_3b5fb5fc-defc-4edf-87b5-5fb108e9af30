package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@DtoClass(includeAllFields = true)
@Table(name = "permission", catalog = "cem_platform")
public class XmPermission extends BaseEntity {

    @Column(name = "role_id")
    private Long roleId;

    @Column(name = "module")
    private String module;

    @Column(name = "permission")
    private String permission;

}
