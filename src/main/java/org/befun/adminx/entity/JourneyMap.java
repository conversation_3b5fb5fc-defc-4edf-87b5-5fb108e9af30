package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:23
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "is_delete=0")
@DtoClass(includeAllFields = true)
@Table(name = "journey_map", catalog = "cem_platform")
public class JourneyMap extends BaseEntity {

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "survey title", example = "", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "user_id")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    private Long userId;

    @Column(name = "org_id")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    private Long orgId;
}
