package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:08
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "organization", catalog = "cem_platform")
@DtoClass(includeAllFields = true)
public class Organization extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id")
    @NotFound(action = NotFoundAction.IGNORE)
    @DtoProperty(ignore = true)
    @JsonIgnore
    private XmPlusUser xmpUser;

    @Column(name = "name")
    @DtoProperty(description = "企业名称", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Column(name = "code")
    @DtoProperty(description = "企业代码", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String code;

    @Column(name = "is_block")
    @DtoProperty(description = "企业状态：1 禁用 0 启用", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer isBlock;

    @Transient
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> version = new HashMap();

    @Column(name = "version")
    @JsonIgnore
    private String strVersion;

    @Column(name = "optional_limit")
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = HashMapConverter.class)
    private HashMap<String, Object> optionalLimit = new HashMap();

//    @Column(name = "app_types")
//    @JsonView(ResourceViews.Basic.class)
//    @DtoProperty(description = "体验家和调研家权限", example = "", jsonView = ResourceViews.Basic.class)
//    @Convert(converter = ListConverter.class)
//    private List<String> appTypes = List.of();
    @Column(name = "app_types")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "体验家和调研家权限", example = "", jsonView = ResourceViews.Basic.class)
    private String appTypes;

    @Temporal(TemporalType.DATE)
    @Column(name = "available_date_begin")
    @DtoProperty(description = "开始时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @JsonView(ResourceViews.Basic.class)
    private Date availableDateBegin;

    @Temporal(TemporalType.DATE)
    @Column(name = "available_date_end")
    @DtoProperty(description = "截止时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @JsonView(ResourceViews.Basic.class)
    private Date availableDateEnd;

    @Column(name = "alias_name")
    @DtoProperty(description = "客户别名", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String aliasName;

    @Column(name = "remark")
    @DtoProperty(description = "备注说明", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String remark;

    public Map<String, Object> getVersion() {
        return JsonHelper.toMap(strVersion);
    }

    public void setVersion(Map<String, Object> version) {
        setStrVersion(JsonHelper.toJson(version));
    }

    public HashMap<String, Object> getOptionalLimit() {
        if (!optionalLimit.containsKey("logo_change_limit") && getVersion()!= null) {
            optionalLimit.put("logo_change_limit", "profession".equals(getVersion().getOrDefault("cem_version", "free")));
        }
        if (!optionalLimit.containsKey("bi_data_field_edit") && getVersion()!= null) {
            optionalLimit.put("bi_data_field_edit", "profession".equals(getVersion().getOrDefault("cem_version", "free")));
        }
        if (!optionalLimit.containsKey("sys_manage_api_edit") && getVersion()!= null) {
            optionalLimit.put("sys_manage_api_edit", "profession".equals(getVersion().getOrDefault("cem_version", "free")));
        }
        if (!optionalLimit.containsKey("multi_language_limit") && getVersion() != null) {
            optionalLimit.put("multi_language_limit", "profession".equals(getVersion().getOrDefault("cem_version", "free")));
        }
        return optionalLimit;
    }
}


