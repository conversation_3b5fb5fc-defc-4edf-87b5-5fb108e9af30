package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "resource")
@DtoClass()
public class Resource extends BaseEntity {
    @Column(name = "category", columnDefinition = "varchar")
    @DtoProperty(description = "category", example = "", jsonView = ResourceViews.Basic.class)
    private String category;

    @Column(name = "name", columnDefinition = "varchar")
    @DtoProperty(description = "name", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "enabled", columnDefinition = "bit")
    @DtoProperty(description = "enabled", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enabled = true;
}
