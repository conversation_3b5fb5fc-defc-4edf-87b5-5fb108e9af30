package org.befun.adminx.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.dto.grid.ReadCommunityGridDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@DtoClass(includeAllFields = true)
@Table(name = "community_grid")
public class CommunityGrid extends BaseEntity {

    @Column(name = "grid_id")
    @Schema(description = "网格id：上传者自行定义")
    @JsonView({ResourceViews.Basic.class})
    private String gridId;

    @Column(name = "cuid")
    @Schema(description = "网格cuid")
    @JsonView({ResourceViews.Basic.class})
    private Long cuid;

    @Column(name = "grid_name")
    @Schema(description = "网格名称")
    @JsonView({ResourceViews.Basic.class})
    private String gridName;

    @Column(name = "grid_inspector_Name")
    @Schema(description = "网格员名称")
    @JsonView({ResourceViews.Basic.class})
    private String gridInspectorName;

    @Column(name = "contract")
    @Schema(description = "网格id：上传者自行定义")
    @JsonView({ResourceViews.Basic.class})
    private String contract;

}
