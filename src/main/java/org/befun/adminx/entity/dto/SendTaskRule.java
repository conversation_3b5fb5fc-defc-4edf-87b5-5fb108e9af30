package org.befun.adminx.entity.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.AuditRuleType;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonView(ResourceViews.Basic.class)
public class SendTaskRule {
    private String name;
    private String expression;
    private Integer score;
    private AuditRuleType ruleType;
}
