package org.befun.adminx.entity.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/11 17:25
 */
@Getter
@Setter
@JsonView(ResourceViews.Basic.class)
public class UserStatistics {
    private String date;
    private Long cuid;
    private Integer total;
    private Integer completed;
    private Integer quotaFull;
    private Integer earlyCompleted;
    private Integer auditPass;
    private Integer auditFail;
    private Integer completedRate;
    private Integer passRate;
}
