package org.befun.adminx.entity.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/20 18:04
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Additional {

    @JsonView(ResourceViews.Basic.class)
    private Long cuid;

    @JsonView(ResourceViews.Basic.class)
    private Integer gender;

    @JsonView(ResourceViews.Basic.class)
    private String province;

    @JsonView(ResourceViews.Basic.class)
    private String city;

    @JsonView(ResourceViews.Basic.class)
    private String area;

    @JsonView(ResourceViews.Basic.class)
    private Integer education;

    @JsonView(ResourceViews.Basic.class)
    private Date birthday;


}
