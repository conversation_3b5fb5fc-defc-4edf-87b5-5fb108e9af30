package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.OrderStatus;
import org.befun.adminx.constant.PayType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_order",catalog = "cem_platform")
@EntityScopeStrategy
@DtoClass
public class OrganizationOrder extends EnterpriseEntity {

    @Schema(description = "订单来源：红包id,短信充值记录id")
    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "title")
    private String title;

    @Enumerated(EnumType.STRING)
    @Column(name = "pay_type")
    private PayType payType;

    @Column(name = "amount")
    private Integer amount;

    @Column(name = "amount_wallet")
    private Integer amountWallet;

    @Column(name = "amount_recharge")
    private Integer amountRecharge;

    @Column(name = "recharge_id")
    private Long rechargeId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OrderStatus status = OrderStatus.init;

    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;
}