package org.befun.adminx.entity;

import lombok.*;
import org.befun.adminx.constant.ScoreType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/26 15:37
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "invite_award_record")
@DtoClass()
public class InviteAwardRecord extends BaseEntity {

    @Column(name = "cuid")
    @DtoProperty(description = "用户uid", example = "", jsonView = ResourceViews.Basic.class)
    private Long cuid;

    @Column(name = "invite_id")
    @DtoProperty(description = "邀请人id", example = "1", jsonView = ResourceViews.Basic.class)
    private Long inviteId;

    @DtoProperty(description = "积分", example = "", jsonView = ResourceViews.Basic.class)
    private Integer score;

    @Column(name = "score_type")
    @DtoProperty(description = "类型，R回答问卷奖励，U等级升级奖励，P完善个人资料奖励，A完善个人账号信息奖励，M积分兑换码兑换，F冻结积分，F-P冻结积分审核通过，F-N冻结积分审核不通过", example = "R", jsonView = ResourceViews.Basic.class)
    private ScoreType scoreType;

    @Column(name = "task_id", columnDefinition = "bigint not null")
    @DtoProperty(description = "任务id", example = "", jsonView = ResourceViews.Basic.class)
    private Long taskId;

    @Column(name = "expire_time")
    @DtoProperty(description = "过期时间", example = "", jsonView = ResourceViews.Basic.class)
    public Date expireTime;

    @Column(name = "status", columnDefinition = "bit default 0")
    @DtoProperty(description = "奖励是否已被领取", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean status = false;

    public InviteAwardRecord(Long cuid, Long inviteId, Integer score, ScoreType scoreType, Long taskId, Date expireTime) {
        this.cuid = cuid;
        this.inviteId = inviteId;
        this.score = score;
        this.scoreType = scoreType;
        this.taskId = taskId;
        this.expireTime = expireTime;
    }

}
