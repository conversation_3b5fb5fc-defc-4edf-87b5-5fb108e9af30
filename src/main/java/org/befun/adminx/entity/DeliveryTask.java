package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;
import lombok.*;
import org.befun.adminx.constant.DeliveryTaskStatus;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.converter.ListListStringConverter;
import org.befun.adminx.dto.ext.DeliveryTaskExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "delivery_task")
@DtoClass(includeAllFields = true, superClass = DeliveryTaskExtDto.class)
public class DeliveryTask extends BaseEntity {
    /* 问卷ID */
//    @OneToOne
//    @JoinColumn(name = "s_id", columnDefinition = "bigint not null")
//    @DtoProperty(description = "survey", example = "", jsonView = ResourceViews.Basic.class, type = SurveyDto.class)
//    private Survey survey;

    @Column(name = "s_id", columnDefinition = "varchar")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private String sid;

    @Column(name = "name", columnDefinition = "varchar")
    @DtoProperty(description = "任务名", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "title", columnDefinition = "varchar")
    @DtoProperty(description = "问卷别名", example = "", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "total", columnDefinition = "int default 0")
    @DtoProperty(description = "总人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer total = 0;

    @Column(name = "audit_count", columnDefinition = "int default 0")
    @DtoProperty(description = "审核通过人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer auditCount = 0;

    @Column(name = "audit_fail_count", columnDefinition = "int default 0")
    @DtoProperty(description = "审核不通过人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer auditFailCount = 0;

    @Column(name = "audit_wait_count", columnDefinition = "int default 0")
    @DtoProperty(description = "待审核", example = "", jsonView = ResourceViews.Basic.class)
    private Integer auditWaitCount = 0;

    @Column(name = "score", columnDefinition = "int default 0")
    @DtoProperty(description = "score", example = "", jsonView = ResourceViews.Basic.class)
    private Integer score = 0;

    @Column(name = "status", columnDefinition = "int default 0")
    @DtoProperty(description = "status", example = "", jsonView = ResourceViews.Basic.class)
    private DeliveryTaskStatus status = DeliveryTaskStatus.WAIT_SEND;

    @Column(name = "end_time", columnDefinition = "TIMESTAMP")
    @DtoProperty(description = "结束时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date endTime;

    @Column(name = "channel_id", columnDefinition = "bigint default 0")
    @DtoProperty(description = "渠道id", example = "", jsonView = ResourceViews.Basic.class)
    private Long channelId = 0L;

    @Column(name = "reply_count", columnDefinition = "int default 0")
    @DtoProperty(description = "填答人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer replyCount = 0;

    @Column(name = "type", columnDefinition = "int default 0")
    @DtoProperty(description = "问卷类型", example = "", jsonView = ResourceViews.Basic.class)
    private SurveyType type = SurveyType.XM_PLUS;

    @Column(name = "survey_status", columnDefinition = "int default 1")
    @DtoProperty(description = "问卷状态", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean surveyStatus = true;

    @Temporal(TemporalType.DATE)
    @Column(name = "start_time")
    @DtoProperty(description = "问卷调查开始时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date startTime;

    @Temporal(TemporalType.DATE)
    @Column(name = "over_time")
    @DtoProperty(description = "问卷调查截止时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date overTime;

    @Column(name = "survey_url")
    @DtoProperty(description = "问卷地址", example = "", jsonView = ResourceViews.Basic.class)
    private String surveyUrl;

    @Column(name = "short_url")
    @DtoProperty(description = "问卷短链接", example = "", jsonView = ResourceViews.Basic.class)
    private String shortUrl;

    @Column(name = "enable_audit", columnDefinition = "bit default 0")
    @DtoProperty(description = "开启审核", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableAudit = false;

    @Column(name = "enable_invite", columnDefinition = "bit default 0")
    @DtoProperty(description = "开启邀请积分奖励", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableInvite = false;

    @Column(name = "invite_score", columnDefinition = "int default 0")
    @DtoProperty(description = "邀请积分", example = "", jsonView = ResourceViews.Basic.class)
    private Integer inviteScore = 0;

    @Column(name = "limit_invite_people", columnDefinition = "bit default 0")
    @DtoProperty(description = "开启邀请限制人数", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean limitInvitePeople = false;

    @Column(name = "limit_invite_people_num", columnDefinition = "int default 0")
    @DtoProperty(description = "邀请人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer limitInvitePeopleNum = 3;

    @Column(name = "task_type")
    @DtoProperty(description = "任务类型", example = "", jsonView = ResourceViews.Basic.class)
    private TaskType taskType = TaskType.DELIVERY;

    @Column(name = "open_supervisor",columnDefinition = "bit default 0")
    @DtoProperty(description = "是否开启督导", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean openSupervisor = false;

    @Column(name = "enable_notify", columnDefinition = "bit default 0")
    @DtoProperty(description = "开启回收提醒", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableNotify = false;

    @Column(name = "enable_electronic_fence", columnDefinition = "bit default 0")
    @DtoProperty(description = "开启电子围栏", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableElectronicFence = false;

    @Column(name = "limit_locations", columnDefinition = "varchar")
    @DtoProperty(description = "地区", example = "", jsonView = ResourceViews.Basic.class)
    @Convert(converter = ListListStringConverter.class)
    private List<List<String>> limitLocations;

    public Integer getAuditCount() {
        return auditCount == null ? 0 : auditCount;
    }
}