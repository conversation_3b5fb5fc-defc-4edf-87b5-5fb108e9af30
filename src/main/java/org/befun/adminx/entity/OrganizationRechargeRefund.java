package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_recharge_refund", catalog = "cem_platform")
@EntityScopeStrategy
@DtoClass
public class OrganizationRechargeRefund extends EnterpriseEntity {

    @Schema(description = "企业退款单号")
    @Column(name = "refund_no")
    private String refundNo;

    @Schema(description = "支付订单id")
    @Column(name = "recharge_id")
    private Long rechargeId;

    @Schema(description = "企业订单id")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "退款金额")
    @Column(name = "amount")
    private Integer amount;

    @Column(name = "deleted")
    private Integer deleted = 0;

}