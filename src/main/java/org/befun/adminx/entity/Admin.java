package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@DtoClass()
@Table(name = "admin")
public class Admin extends BaseEntity {
    @Column(name = "user_name")
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    private String userName;

    @Column(name = "password")
    @JsonIgnore
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Detail.class)
    private String password;

    @Column(name = "roles")
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    @Convert(converter = StringListConverter.class)
    private List<String> roles = new ArrayList<>();
}
