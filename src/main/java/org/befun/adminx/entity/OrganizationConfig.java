package org.befun.adminx.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "organization_config", catalog = "cem_platform")
@DtoClass
public class OrganizationConfig extends BaseEntity {

    @Column(name = "org_id")
    public Long orgId;

    @Column(name = "type")
    private String type;

    @Column(name = "config")
    private String config;

}