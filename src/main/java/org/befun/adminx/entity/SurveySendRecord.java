package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.*;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SendStatus;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
//@EntityQueryable({"name","account"})
@Table(name = "survey_send_record")
@DtoClass(includeAllFields = true)
public class SurveySendRecord extends BaseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "任务id", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "task_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class, description = "任务id")
    private Long taskId = 0l;

    @Schema(description = "问卷id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "s_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String surveyId;

    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "客户端id", required = true)
    @Column(name = "client_id")
    private String clientId = null;

    @Schema(description = "接收人姓名", required = true)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String name = "";

    @Schema(description = "接收人账号(微信openid或者手机号)", required = true)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String account;

    @Schema(description = "推送内容", required = true)
    @JsonView(ResourceViews.Detail.class)
    private String content;

    @Schema(description = "问卷地址", required = true)
    @Column(name = "send_url")
    private String sendUrl;

    @Schema(description = "发送次数", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "send_count")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Integer sendCount = 0;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷发送时间")
    @Column(name = "send_time")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Date sendTime = null;

    @Column(name = "send_status")
    @Enumerated
    @Schema(description = "发送状态(0:待发送,1:发送成功,2:发送失败)")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private SendStatus sendStatus = SendStatus.UN_SEND;

    @Schema(description = "回收状态(0:未完成,1:已完成)")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Boolean status = false;

    @Enumerated
    @JsonIgnore
    @Schema(description = "通知类型 0问卷接受通知,1积分变动通知")
    private SendType type;

    /*追访*/
    @Column(name = "cuid")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class, description = "cuid")
    private Long cuid;

    @Column(name = "task_type")
    @Enumerated
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private TaskType taskType = TaskType.DELIVERY;

    @Schema(description = "最后一次发送方式")
    @Column(name = "send_method")
    @Enumerated
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private TaskSendMethod sendMethod;

    @Schema(description = "微信发送次数")
    @Column(name = "send_wechat_count")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Integer sendWechatCount = 0;

    @Schema(description = "短信发送次数")
    @Column(name = "send_message_count")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Integer sendMessageCount = 0;

    @Schema(description = "最后一次填答状态")
    @Column(name = "submit_status")
    @Enumerated
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private SubmitStatus submitStatus = SubmitStatus.NOT_VISIT;

    @Schema(description = "最后一次发送关注状态")
    @Column(name = "subscribe_status")
    @Enumerated
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private WechatSubscribeStatus subscribeStatus;

    @Schema(description = "手机号绑定状态（false:未绑定，true：绑定")
    @Column(name = "phone_status")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Boolean phoneStatus;

    @Schema(description = "最后一次失败原因")
    @Column(name = "fail_msg")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String failMsg;

    @Schema(description = "基线问卷id", required = true)
    @Column(name = "base_line_sid")
    @JsonView(ResourceViews.Basic.class)
    private String baseLineSurveyId;

    @Schema(description = "基线答卷id", required = true)
    @Column(name = "base_line_rid")
    @JsonView(ResourceViews.Basic.class)
    private String baseLineResponseId;

    @Schema(description = "答卷审核状态")
    @Column(name = "response_status")
    @Enumerated
    private ResponseStatus responseStatus;

    @Schema(description = "积分奖励", required = true)
    @Column(name = "points")
    @JsonView(ResourceViews.Basic.class)
    private Integer points;



    public SurveySendRecord convertSubscribeStatus(Integer subscribeStatus) {
        switch (subscribeStatus) {
            case 1:
                this.subscribeStatus = WechatSubscribeStatus.SUBSCRIBE;break;
            case 2:
                this.subscribeStatus = WechatSubscribeStatus.UN_SUBSCRIBE;break;
            case 0:
            default:
                this.subscribeStatus = WechatSubscribeStatus.CANCEL_SUBSCRIBE;
        }
        return this;
    }
}
