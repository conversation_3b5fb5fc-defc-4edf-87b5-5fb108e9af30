package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.SendStatus;
import org.befun.adminx.constant.SendTaskType;
import org.befun.adminx.constant.TaskSendType;
import org.befun.adminx.constant.TaskType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 09:52
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "send_record")
@DtoClass()
public class SendRecord extends BaseEntity {

    @Column(name = "task_id", columnDefinition = "long")
    @DtoProperty(description = "任务id", example = "", jsonView = ResourceViews.Basic.class)
    private Long taskId;

    @Column(name = "s_id", columnDefinition = "varchar")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private String sid;

    @Column(name = "enable_push", columnDefinition = "boolean")
    @DtoProperty(description = "是否公众号模板推送", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enablePush = false;

    @Column(name = "rules", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "问卷推送规则", example = "", jsonView = ResourceViews.Basic.class)
    private String rules = "[]";

    @Column(name = "latest", columnDefinition = "boolean")
    @DtoProperty(description = "是否最新的task推送", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean latest = true;

    @Column(name = "send_type", columnDefinition = "boolean")
    @DtoProperty(description = "样本推送类型(0:全部,1:指定样本)", example = "", jsonView = ResourceViews.Basic.class)
    private SendTaskType sendType;

    @Column(name = "black_list", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "黑名单", example = "", jsonView = ResourceViews.Basic.class)
    private String blackList;

    @Column(name = "white_list", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "白名单", example = "", jsonView = ResourceViews.Basic.class)
    private String whiteList;

    @Column(name = "operator", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "操作人id", example = "", jsonView = ResourceViews.Basic.class)
    private Long operator;

    @Column(name = "template_id", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "推送的微信模板", example = "", jsonView = ResourceViews.Basic.class)
    private String templateId;

    @Column(name = "complete_count", columnDefinition = "int")
    @DtoProperty(description = "推送微信模板完成人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer completeCount = 0;

    @Column(name = "total_count", columnDefinition = "int")
    @DtoProperty(description = "推送微信模板总人数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer totalCount;

    @Column(name = "send_status", columnDefinition = "boolean")
    @DtoProperty(description = "推送状态：推送失败，推送中，推送成功，", example = "", jsonView = ResourceViews.Basic.class)
    @Enumerated(value = EnumType.STRING)
    private SendStatus sendStatus;

    @Column(name = "message", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "记录推送成功、失败相关信息", example = "", jsonView = ResourceViews.Basic.class)
    private String message;

    @Column(name = "task_send_type", columnDefinition = "boolean")
    @DtoProperty(description = "推送任务类型:默认任务，模板推送", example = "", jsonView = ResourceViews.Basic.class)
    @Enumerated(value = EnumType.STRING)
    private TaskSendType taskSendType;

    @Transient
    @Schema(description = "模板名称")
    private String templateName;

    @Transient
    @Schema(description = "操作人账号名")
    private String operatorName;


}

























