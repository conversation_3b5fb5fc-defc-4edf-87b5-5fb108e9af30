package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.CemWorkerBase;
import org.befun.adminx.dto.task.TaskType;

import javax.persistence.*;

@Entity
@Setter
@Getter
@Table(name = "worker_task")
@NoArgsConstructor
public class CemTask extends CemWorkerBase {

    @Schema(description = "任务类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private TaskType type;

    public CemTask(TaskType type) {
        this.type = type;
    }
}
