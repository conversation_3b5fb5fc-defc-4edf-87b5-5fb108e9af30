package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_survey_question", catalog = "cem_platform")
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class TemplateSurveyQuestion extends BaseQuestion {

    @Schema(description = "组织id")
    @Column(name = "org_id")
    private Long orgId;

    @Schema(description = "用户id")
    @Column(name = "user_id")
    private Long userId;

    @Schema(description = "模板库id")
    @Column(name = "group_id")
    private Long groupId;

    @DtoProperty(jsonView = ResourceViews.Basic.class, description = "可否编辑 0：不能 1：可以")
    private Boolean editable = true;

    @OneToMany(mappedBy = "question", cascade = {CascadeType.ALL})
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = TemplateSurveyQuestionItemDto.class)
    private List<TemplateSurveyQuestionItem> items = new ArrayList<>();

    @OneToMany(mappedBy = "question", cascade = {CascadeType.ALL})
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = TemplateSurveyQuestionColumnDto.class)
    private List<TemplateSurveyQuestionColumn> columns = new ArrayList<>();

}
