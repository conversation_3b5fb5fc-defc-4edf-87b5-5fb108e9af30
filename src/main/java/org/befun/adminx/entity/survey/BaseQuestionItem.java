package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.SequenceAware;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Size;

@Getter
@Setter
@MappedSuperclass
public class BaseQuestionItem extends BaseEntity implements SequenceAware {
    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private Integer sequence = 0;

    @Size(max = 50, message = "选项值长度超多限制")
    @Schema(description = "选项值", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String value;

    @Size(max = 2000, message = "选项文本长度超多限制")
    @Schema(description = "选项显示文本", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String text;

    @Size(max = 200, message = "SEL长度超多限制")
    @Column(name = "visible_if")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "显示条件 SEL表达式", example = "q1 > 3")
    private String visibleIf;

    @Column(name = "exclude_other")
    @Schema(description = "是否排除其他", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean excludeOther = false;

    @Size(max = 500, message = "超多限制")
    @Schema(description = "选项配置项", example = "")
    @JsonView(ResourceViews.Basic.class)
    private String configure = "";

    @Schema(description = "选项分值", example = "")
    @JsonView(ResourceViews.Basic.class)
    private Integer score = 0;

    @Column(name = "enable_text_input")
    @Schema(description = "是否支持文本输入", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableTextInput = false;

    @Column(name = "is_fix")
    @Schema(description = "是否选项固定", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isFix = false;
}
