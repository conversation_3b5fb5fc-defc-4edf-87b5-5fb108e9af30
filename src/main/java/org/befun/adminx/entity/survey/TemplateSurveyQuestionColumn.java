package org.befun.adminx.entity.survey;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_survey_question_column", catalog = "cem_platform")
@DtoClass(includeAllFields = true)
public class TemplateSurveyQuestionColumn extends BaseQuestionColumn {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "q_id")
    @DtoProperty(ignore = true)
    private TemplateSurveyQuestion question;

}
