package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.constant.survey.ChannelType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted=0")
@Table(name = "survey_channel", catalog = "cem_platform")
@DtoClass()
public class SurveyChannel extends BaseEntity {
    @DtoProperty(description = "survey id", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "s_id")
    private Long sid;

    @DtoProperty(description = "channel name", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "status")
    @Enumerated
    @DtoProperty(description = "渠道状态 0:未设置，1:回收中，2:暂停，3:已完成，4:关闭", example = "", jsonView = ResourceViews.Basic.class)
    private ChannelStatus status = ChannelStatus.UNSET;

    @Column(name = "type")
    @Enumerated
    @DtoProperty(description = "渠道类型 0通用类型,1wechat短链接,2短信,3微信服务号,4页面嵌入,5调研家社区,6场景互动", example = "", jsonView = ResourceViews.Basic.class)
    private ChannelType type = ChannelType.COMMON;

    @Column(name = "enable_wechat_limit",columnDefinition = "bit(1) default 0")
    @DtoProperty(description = "是否开启只能在微信填答限制", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableWechatLimit = false;

    @Column(name = "enable_wechat_reply_only", columnDefinition = "bit(1) default 0")
    @DtoProperty(description = "是否开启每个微信号只填答一次限制", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableWechatReplyOnly = false;

    @Column(name = "configure", length = 65535)
    @Schema(description = "渠道配置信息，json格式")
    @JsonView(ResourceViews.Detail.class)
    private String configure = "";

    @Column(name = "reject_message", columnDefinition = "varchar")
    @DtoProperty(description = "订单驳回消息", jsonView = ResourceViews.Basic.class)
    @Length(max = 500)
    private String rejectMessage;

    @Schema(name = "订单报价方式：standard, manual")
    @Column(name = "order_pay_type")
    @DtoProperty(description = "订单报价方式：standard, manual", jsonView = ResourceViews.Basic.class)
    private String orderPayType;

    @Column(name = "order_id")
    @DtoProperty(description = "订单id", jsonView = ResourceViews.Basic.class)
    private Long orderId;

    @Column(name = "order_amount")
    @DtoProperty(description = "订单金额", jsonView = ResourceViews.Basic.class)
    private Integer orderAmount;

    //  {
    //      "type": "full",
    //      "unitPrice": 1,
    //      "quantity": 101,
    //      "recycle": 100,
    //      "serviceRate": 0.06,
    //      "refundAmount": 100
    //  }
    @Column(name = "order_refund")
    private String orderRefund;

}


