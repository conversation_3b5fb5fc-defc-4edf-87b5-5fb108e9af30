package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.audit.AuditResultDownLoadDto;
import org.befun.adminx.dto.audit.AuditResultJsonDto;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.*;

@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_response", catalog = "cem_platform")
@DtoClass()
public class SurveyResponse extends BaseEntity {

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "s_id")
//    @DtoProperty(ignore = true)
//    private Survey survey;

    @Schema(description = "问卷ID")
    @Column(name = "s_id")
    private Long surveyId;

    @Schema(description = "客户中心客户ID")
    @Column(name = "c_id")
    private Long customerId;

    @Size(max = 100, message = "外部用户ID长度超多限制")
    @Schema(description = "外部用户ID")
    @Column(name = "euid")
    private String externalUserId;

    @Size(max = 100, message = "客户端长度超多限制")
    @Schema(description = "客户端ID")
    @Column(name = "client_id")
    private String clientId;

    @Schema(description = "场景ID")
    @Column(name = "scene_id")
    private Long sceneId;

    @Schema(description = "部门ID")
    @Column(name = "department_id")
    private Long departmentId;

    @Schema(description = "部门名称")
    @Column(name = "department_name")
    private String departmentName;

    @Schema(description = "客户名称")
    @Column(name = "customer_name")
    private String customerName;

    @Schema(description = "客户性别")
    @Column(name = "customer_gender")
    private String customerGender;

    @Schema(description = "外部组织编号")
    @Column(name = "department_code")
    private String departmentCode;

    @Schema(description = "外部企业ID")
    @Column(name = "external_company_id")
    private String externalCompanyId;

    @Schema(description = "默认参数")
    @Column(name = "default_pa")
    private String defaultPa;

    @Schema(description = "默认参数")
    @Column(name = "default_pb")
    private String defaultPb;

    @Schema(description = "默认参数")
    @Column(name = "default_pc")
    private String defaultPc;

    @Schema(description = "是否结束")
    @Column(name = "is_completed")
    private Boolean isCompleted = false;

    private String ip;
    @JsonView(ResourceViews.Basic.class)
    private String country = "未知";;
    @JsonView(ResourceViews.Basic.class)
    private String province = "未知";;
    @JsonView(ResourceViews.Basic.class)
    private String city = "未知";

    private String device;
    private String os;
    private String browser;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "duration_seconds")
    @Schema(description = "答题持续时间(秒单位)")
    private Integer durationSeconds;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "total_score")
    @Schema(description = "总得分")
    private Integer totalScore = 0;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "track_id")
    @Schema(description = "跟踪id")
    private String trackId;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题结束时间")
    @Column(name = "finish_time")
    private Date finishTime;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "parameters")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> parameters = new HashMap<>();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "addition_data")
    @Schema(description = "附加数据")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> additionData = new HashMap<>();

    @OneToMany(mappedBy = "responseId", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
    private Collection<SurveyResponseCell> cells;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "status")
    @Enumerated
    private ResponseStatus status = ResponseStatus.INIT;

    @Column(name = "connector_method")
    @Enumerated
    @Schema(description = "收集方法", example = "URL")
    private SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;

    @Schema(description = "渠道ID")
    @Column(name = "channel_id")
    private Long channelId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "sequence")
    @Schema(description = "答卷编号")
    private Long sequence;

    @Schema(description = "微信openid")
    private String openid;

    @Schema(description = "审核状态")
    @Column(name = "audit_result")
    private String auditResult = null;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信接收人名称")
    private String name;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信接收人手机号")
    private String phone;

    @Column(name = "similarity_check")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题质控 0不通过 1通过")
    private Boolean similarityCheck = true;

    @Column(name = "similarity_percent")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "相似程度")
    private Float similarityPercent;

    @Column(name = "similarity_no")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "相似组序号")
    private Long similarityNo;


    public AuditResultDownLoadDto getAuditResult(String auditResult) {
        AuditResultDownLoadDto downLoadDto = new AuditResultDownLoadDto();
        AuditResultJsonDto dto = JsonHelper.toObject(auditResult, AuditResultJsonDto.class);
        if(dto == null) return null;
        if(dto.getType() != null) {
            downLoadDto.setAuditType(dto.getType().getText());
        } else {
            //TODO 兼容以前审核方式
            if(dto.getAutoAudit() == null) downLoadDto.setAuditType("半自动");
            else downLoadDto.setAuditType(dto.getAutoAudit() ? "自动":"手动");
        }
        downLoadDto.setAuditScore(dto.getScore().toString());
        downLoadDto.setPoints(dto.getPoints());
        downLoadDto.setFailRule(Objects.toString(dto.getFailRuleName(), ""));
        return downLoadDto;
    }
}
