package org.befun.adminx.entity.survey;

import com.google.common.base.Strings;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.dto.survey.SurveyFileUploadDto;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_response_cell", catalog = "cem_platform")
@DtoClass(includeAllFields = true)
public class SurveyResponseCell extends BaseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Column(name = "s_id")
    private Long surveyId;

    @Column(name = "q_id")
    private Long questionId;

    @DtoProperty(ignore = true)
    @Column(name = "r_id")
    private Long responseId;

    @Enumerated
    private QuestionType type;

    @Column(name = "i_val")
    private Integer intValue;

    @Column(name = "d_val")
    private Double doubleValue;

    @Column(name = "s_val")
    private String strValue;

    @Column(name = "j_val")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> jsonValue;

    @Column(name = "comment_val")
    private String commentValue;

    @Column(name = "t_val")
    private Long dateValue;

    @Column(name = "tags")
    private String tags;

    @Column(name = "cell_score")
    private Integer cellScore;

    public SurveyResponseCell(Long surveyId, Long responseId, Long questionId, QuestionType questionType, Object value) {
        this.surveyId = surveyId;
        this.questionId = questionId;
        this.responseId = responseId;
        setType(questionType);
        setValue(value);
    }

    public SurveyResponseCell(Survey survey, SurveyQuestion question, SurveyResponse response, Object value) {
        Assert.notNull(question, "invalid question");
        this.surveyId = survey.getId();
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());

        setValue(question, value);
    }


    public SurveyResponseCell(Long surveyId, Long responseId, Long questionId, QuestionType questionType) {
        this.surveyId = surveyId;
        this.questionId = questionId;
        this.responseId = responseId;
        setType(questionType);
    }

    public SurveyResponseCell(Long surveyId, SurveyQuestion question, SurveyResponse response) {
        Assert.notNull(question, "invalid question");

        this.surveyId = surveyId;
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());
    }

    public SurveyResponseCell(Survey survey, SurveyQuestion question, SurveyResponse response) {
        Assert.notNull(question, "invalid question");

        this.surveyId = survey.getId();
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());
    }

    /**
     * 根据不同题型，返回不同数值
     *
     * @return
     */
    public Object getValue() {
        switch (getType()) {
            case SINGLE_CHOICE:
            case COMBOBOX:
            case TEXT:
            case MARK:
            case MOBILE:
            case EMAIL:
            case EVALUATION:
            case EXPERIMENT:
            case SCORE_EVALUATION:
                return getStrValue();
            case MATRIX:
            case SCORE:
            case NPS:
                return getIntValue();
            case NUMBER:
                return getDoubleValue();
            case AREA:
            case DROP_DOWN:
            case MULTIPLE_CHOICES:
            case ORGANIZE:
                List<Object> arrayList = new ArrayList<>();
                if (!Strings.isNullOrEmpty(getStrValue())) {
                    Collections.addAll(arrayList, getStrValue().split(";"));
                }
                return arrayList;
            case MATRIX_SCORE:
            case MATRIX_CHOICE:
            case MATRIX_SLIDER:
            case RANKING:
            case LOCATION:
            case BLANK:
            case MULTIPLE_BLANK:
                return getJsonValue();
            case DATE:
                return getDateValue();
            case FILE:
            case SIGNATURE:
                return JsonHelper.toList(getStrValue(), SurveyFileUploadDto.class);
            default:
                break;
        }
        return null;
    }

    /**
     * 根据不同题型，设置不同数值
     *
     * @param question
     * @param value
     */
    public void setValue(SurveyQuestion question, Object value) {
        setType(question.getType());
        setValue(value);
    }

    public void setValue(Object value) {
        switch (getType()) {
            case SINGLE_CHOICE:
            case COMBOBOX:
            case EVALUATION:
            case TEXT:
            case MARK:
            case MOBILE:
            case EMAIL:
            case EXPERIMENT:
            case SCORE_EVALUATION:
                this.setStrValue(value.toString());
                break;
            case NUMBER:
                this.setDoubleValue(Double.valueOf(value.toString()));
                break;
            case MATRIX:
            case SCORE:
            case NPS:
                this.setIntValue((int) value);
                break;
            case AREA:
            case MULTIPLE_CHOICES:
            case MATRIX_SCORE:
            case MATRIX_CHOICE:
            case DROP_DOWN:
            case MATRIX_SLIDER:
            case ORGANIZE:
            case RANKING:
            case LOCATION:
            case BLANK:
            case MULTIPLE_BLANK:
                if (value instanceof String[]) {
                    String[] saVal = (String[]) value;
                    this.setStrValue(Arrays.stream(saVal).collect(Collectors.joining(";")));
                } else if (value instanceof ArrayList) {
                    this.setStrValue(((List<String>) value).stream().collect(Collectors.joining(";")));
                } else if (value instanceof Map) {
                    this.setJsonValue((Map<String, Object>) value);
                } else {
                    throw new RuntimeException("invalid multiple items");
                }
                break;
            case DATE:
                this.setDateValue(Long.parseLong(value.toString()));
                break;
            case FILE:
            case SIGNATURE:
                this.setStrValue(JsonHelper.toJson(value));
            default:
                break;
        }
    }

    /**
     * 联合实验题答题数据
     */
    @Transient
    private Map<String/*item*/, Map<String/*attr*/, String/*value*/>> experimentValue;
}
