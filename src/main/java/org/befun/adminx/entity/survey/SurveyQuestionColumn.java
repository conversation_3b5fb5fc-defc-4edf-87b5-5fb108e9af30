package org.befun.adminx.entity.survey;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.RootAware;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_question_column", catalog = "cem_platform")
@Slf4j
@DtoClass(includeAllFields = true)
public class SurveyQuestionColumn extends BaseQuestionColumn implements RootAware<Survey> {
    private static final long serialVersionUID = 6529685098267757690L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "q_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private SurveyQuestion question;

    @Override
    @JsonIgnore
    public Survey getRoot() {
        if (question == null) {
            return null;
        }
        return question.getSurvey();
    }

}
