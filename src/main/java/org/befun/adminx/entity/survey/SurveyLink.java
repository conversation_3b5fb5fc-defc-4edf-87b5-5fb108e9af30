package org.befun.adminx.entity.survey;


import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "link", catalog = "cem_platform")
@DtoClass()
public class SurveyLink extends BaseEntity {
    @Column(name = "url")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "原始地址", example = "", jsonView = ResourceViews.Basic.class)
    private String url;

    @Column(name = "survey_id")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private Long surveyId;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "问卷客户端前缀", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "survey_client_prefix")
    private String surveyClientPrefix;

    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "参数", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "params")
    private String params;
}
