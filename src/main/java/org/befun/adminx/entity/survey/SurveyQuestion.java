package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.RootAware;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_question", catalog = "cem_platform")
@Slf4j
@DtoClass(includeAllFields = true)
@NamedEntityGraph(name = "QuestionItems.Graph", attributeNodes = {@NamedAttributeNode("items")})
public class SurveyQuestion extends BaseQuestion implements RootAware<Survey> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    private Survey survey;

    @OneToMany(mappedBy = "question", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name = "sequence", insertable = false, updatable = false)
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = SurveyQuestionItemDto.class)
    private List<SurveyQuestionItem> items = new ArrayList<>();

    @OneToMany(mappedBy = "question", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name = "sequence", insertable = false, updatable = false)
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = SurveyQuestionColumnDto.class)
    private List<SurveyQuestionColumn> columns = new ArrayList<>();

    @Transient
    @DtoProperty(ignore = true)
    private boolean[] removeFlag = new boolean[]{false, false};

    @Override
    @JsonIgnore
    public Survey getRoot() {
        return survey;
    }


    public List<SurveyQuestionItem> getItems() {
        if (!removeFlag[0]) {
            items.removeIf(Objects::isNull);
            removeFlag[0] = true;
        }
        return items;
    }

    public List<SurveyQuestionColumn> getColumns() {
        if (!removeFlag[1]) {
            columns.removeIf(Objects::isNull);
            removeFlag[1] = true;
        }
        return columns;
    }
}
