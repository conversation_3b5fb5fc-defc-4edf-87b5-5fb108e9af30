package org.befun.adminx.entity.survey;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_completed_record")
@DtoClass()
public class SurveyCompletedRecord extends BaseEntity {

    @Column(name = "s_id", columnDefinition = "varchar")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private String sid;

    @Column(name = "response_id", columnDefinition = "bigint")
    @DtoProperty(description = "response id", example = "", jsonView = ResourceViews.Basic.class)
    private Long responseId;

    @Column(name = "open_id", columnDefinition = "varchar")
    @DtoProperty(description = "user openId", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @Column(name = "cuid", columnDefinition = "varchar")
    @DtoProperty(description = "user id", example = "", jsonView = ResourceViews.Basic.class)
    private Long cuid;

    @Column(name = "grid_id", columnDefinition = "varchar")
    @DtoProperty(description = "通过哪个网格id填答", example = "", jsonView = ResourceViews.Basic.class)
    private String gridId;

    @Column(name = "task_id", columnDefinition = "varchar")
    @DtoProperty(description = "任务id", example = "", jsonView = ResourceViews.Basic.class)
    private String taskId;

    @Column(name = "invite_id", columnDefinition = "varchar")
    @DtoProperty(description = "邀请人id", example = "", jsonView = ResourceViews.Basic.class)
    private Long inviteId;

}


