package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.QuotaChannelType;
import org.befun.adminx.dto.ext.AdminxQuotaExtDto;
import org.befun.adminx.dto.ext.SurveyQuotaExtDto;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "adminx_quota")
@Slf4j
@DtoClass(includeAllFields = true,superClass = AdminxQuotaExtDto.class)
public class AdminxQuota extends BaseEntity {

    @Column(name = "s_id")
    @Schema(description = "问卷id", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Long sid;

    @Size(max = 255, message = "名称超出255长度")
    @Schema(description = "问卷标题", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_title")
    private String surveyTitle = "";

}














