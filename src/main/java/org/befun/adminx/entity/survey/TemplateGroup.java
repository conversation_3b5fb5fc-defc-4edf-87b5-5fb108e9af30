package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.survey.TemplateType;
import org.befun.adminx.dto.ext.TemplateGroupExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "template_group", catalog = "cem_platform")
//@AllArgsConstructor
@NoArgsConstructor
@DtoClass(includeAllFields = true, superClass = TemplateGroupExtDto.class)
@EntityScopeStrategy
public class TemplateGroup extends EnterpriseEntity {

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private int sequence = 0;

    @Size(max = 200, message = "组名长度超过限制")
    @Schema(description = "组名")
    @JsonView(ResourceViews.Basic.class)
    private String title = "";

    @Schema(description = "模板类型0：survey 1：question")
    @JsonView(ResourceViews.Basic.class)
    private TemplateType type = TemplateType.SURVEY;

    public TemplateGroup(int sequence, String title, TemplateType type) {
        this.sequence = sequence;
        this.title = title;
        this.type = type;
    }

}
