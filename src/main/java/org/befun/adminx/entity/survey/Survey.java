package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.SurveyQuotaStatus;
import org.befun.adminx.constant.survey.SurveyStatus;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey", catalog = "cem_platform")
@Where(clause = "deleted=0")
@DtoClass()
public class Survey extends BaseEntity {
    /* 问卷名称 */
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "问卷别名：编辑端列表显示", example = "", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "real_title")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "问卷标题：答题端显示", example = "", jsonView = ResourceViews.Basic.class)
    private String realTitle;

    @Column(name = "user_id")
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    private Long userId;

    @Column(name = "org_id")
    @DtoProperty(description = "", example = "", jsonView = ResourceViews.Basic.class)
    private Long orgId;

    @Column(name = "status")
    @DtoProperty(description = "问卷状态", example = "", jsonView = ResourceViews.Basic.class)
    private Integer status;

    @OneToMany(mappedBy = "survey", orphanRemoval = true, fetch = FetchType.EAGER)
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyQuestionDto.class)
    private List<SurveyQuestion> questions = new ArrayList<>();

    @Column(name = "enable_quota", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启配额")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableQuota = false;

    @Column(name = "enable_adminx_quota", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启配额")
    @JsonView(ResourceViews.Detail.class)
    @DtoProperty(description = "是否开启配额",  example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableAdminxQuota = true;

    @Column(name = "quota_status")
    @Enumerated
    @Schema(description = "问卷状态 0初始状态 1配额计算中 2配额计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus quotaStatus = SurveyQuotaStatus.CALCULATE_INIT;

    @Column(name = "adminx_quota_status")
    @Enumerated
    @Schema(description = "问卷状态 0初始状态 1配额计算中 2配额计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus adminxQuotaStatus = SurveyQuotaStatus.CALCULATE_INIT;

}


