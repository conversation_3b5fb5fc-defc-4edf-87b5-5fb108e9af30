package org.befun.adminx.entity.survey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/26 16:00
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_behavior_record", catalog = "cem_platform")
@DtoClass(includeAllFields = true)
public class SurveyBehaviorRecord  extends BaseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Column(name = "r_id")
    @Schema(description = "答卷id", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Long responseId;

    @Column(name = "page")
    @Schema(description = "问卷页码序号", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Integer page = 0;

    @Column(name = "s_id")
    @Schema(description = "问卷id", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Long surveyId;

    @Column(name = "q_codes")
    @Size(max = 200, message = "qCodes超出200长度")
    @Schema(description = "每页的question code", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String qCodes = null;

    @Column(name = "duration")
    @Schema(description = "每页停留的时间（毫秒）", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Integer duration = 0;

    @Column(name = "return_times")
    @Schema(description = "返回次数", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Integer returnTimes = 0;
}
