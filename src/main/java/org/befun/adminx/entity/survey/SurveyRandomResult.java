package org.befun.adminx.entity.survey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.survey.QuestionRandomType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/2/2 13:43:49
 * @desc 题组、题目随机结果表
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_random_result", catalog = "cem_platform")
@DtoClass()
public class SurveyRandomResult extends BaseEntity {

    @Column(name = "survey_id")
    private Long surveyId;

    @Column(name = "response_id")
    private Long responseId;

    @Column(name = "question_id")
    private Long questionId;

    @Enumerated
    @Schema(description = "问题类型：题目、题组")
    private QuestionRandomType type;

    @Column(name = "is_show")
    @Schema(description = "该问题是否显示，显示表示随机到")
    private Boolean isShow;

}
