package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.adminx.constant.AutoAuditType;
import org.befun.adminx.constant.backup.BackupLogType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;

import javax.persistence.*;
import java.util.Date;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 11:17
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "backup_log")
@DtoClass()
public class BackupLog extends BaseEntity {
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "backup_id")
    @DtoProperty(ignore = true)
    private BackupAccount account;

    @Column(name = "file", columnDefinition = "varchar")
    @DtoProperty(description = "文件", example = "", jsonView = ResourceViews.Basic.class)
    private String file;

    @Column(name = "success", columnDefinition = "bit")
    @DtoProperty(description = "是否成功", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean success;

    @Column(name = "message", columnDefinition = "varchar")
    @DtoProperty(description = "消息", example = "", jsonView = ResourceViews.Basic.class)
    private String message;

    @Column(name = "type", columnDefinition = "int")
    @DtoProperty(description = "类型", example = "", jsonView = ResourceViews.Basic.class)
    private BackupLogType type = BackupLogType.BACKUP;
}
