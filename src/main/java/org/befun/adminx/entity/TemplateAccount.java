package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 11:17
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_account")
@DtoClass()
public class TemplateAccount extends BaseEntity {


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "org_id")
    @DtoProperty(description = "企业信息", example = "", jsonView = ResourceViews.Basic.class)
    private Organization organization = null;

    @Column(name = "name", columnDefinition = "varchar")
    @DtoProperty(description = "名称", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "is_default", columnDefinition = "bit")
    @DtoProperty(description = "是否默认", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean isDefault = false;
}
