package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.converter.StringTimestampConverter;
import org.befun.adminx.dto.ext.XmPlusUserExDto;
import org.befun.core.dto.UserDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:08
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user", catalog = "cem_platform")
@NamedEntityGraph(name = "xmpUser.all", attributeNodes = {@NamedAttributeNode("organization")})
@DtoClass(includeAllFields = true, superClass = XmPlusUserExDto.class)
public class XmPlusUser extends BaseEntity {

//    @Column(name = "org_id")
//    @DtoProperty(description = "组织ID", example = "", jsonView = ResourceViews.Basic.class)
//    private Long orgId;

    @Column(name = "role_id")
    @DtoProperty(description = "角色ID", example = "", jsonView = ResourceViews.Basic.class)
    private String roleId;

    @Column(name = "password")
    @DtoProperty(description = "角色ID", example = "", ignore = true)
    @JsonIgnore
    private String password;

    @Column(name = "username")
    @DtoProperty(description = "用户名", example = "", jsonView = ResourceViews.Basic.class)
    private String userName;

    @Column(name = "truename")
    @DtoProperty(description = "真实姓名", example = "", jsonView = ResourceViews.Basic.class)
    private String trueName;

    @Column(name = "email")
    @DtoProperty(description = "email", example = "", jsonView = ResourceViews.Basic.class)
    private String email;

    @Column(name = "mobile")
    @DtoProperty(description = "mobile", example = "", jsonView = ResourceViews.Basic.class)
    private String mobile;

    @Column(name = "is_admin")
    @DtoProperty(description = "系统管理员 0-不是 1-是", example = "", jsonView = ResourceViews.Basic.class)
    private Integer isAdmin;

    @Column(name = "status")
    @DtoProperty(description = "用户状态 0-启用(兼容旧版本) 1-启用 2-禁用 3-待激活", example = "", jsonView = ResourceViews.Basic.class)
    private Integer status = 1;

    @Convert(converter = StringTimestampConverter.class)
    @Column(name = "latest_login")
    @DtoProperty(description = "最后登录时间，时间戳", example = "", jsonView = ResourceViews.Basic.class)
    private String latestLogin = null;

    @Column(name = "ip")
    @DtoProperty(description = "ip", example = "", jsonView = ResourceViews.Basic.class)
    private String ip;

    @Column(name = "province")
    @DtoProperty(description = "province", example = "", jsonView = ResourceViews.Basic.class)
    private String province;

    @Column(name = "city")
    @DtoProperty(description = "city", example = "", jsonView = ResourceViews.Basic.class)
    private String city;

    @Column(name = "available_systems")
    @DtoProperty(description = "可使用的系统", example = "", jsonView = ResourceViews.Basic.class)
    private String availableSystems;

    @Column(name = "survey_count")
    @DtoProperty(description = "问卷总数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer surveyCount = 0;

    @Column(name = "response_count")
    @DtoProperty(description = "问卷回收总数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer responseCount = 0;

    @Column(name = "event_count")
    @DtoProperty(description = "预警总数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer eventCount = 0;

    @Column(name = "response_count_last_month")
    @DtoProperty(description = "本月所有问卷回收量", example = "", jsonView = ResourceViews.Basic.class)
    private Integer responseCountLastMonth = 0;

    @Column(name = "response_total", columnDefinition = "int")
    @DtoProperty(description = "问卷投放总数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer responseTotal = 0;

    @Column(name = "response_total_last_month", columnDefinition = "int")
    @DtoProperty(description = "问卷本月投放总数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer responseTotalLastMonth = 0;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "org_id")
    @DtoProperty(description = "企业信息", example = "", jsonView = ResourceViews.Basic.class)
    private Organization organization = null;
}


