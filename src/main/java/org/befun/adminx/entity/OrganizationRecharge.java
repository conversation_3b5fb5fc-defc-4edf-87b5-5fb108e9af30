package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.RechargeType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@Where(clause = "deleted=0")
@Table(name = "organization_recharge",catalog = "cem_platform")
@EntityScopeStrategy
@DtoClass
public class OrganizationRecharge extends EnterpriseEntity {

    @Column(name = "type")
    private String type;

    @Schema(description = "企业支付单号")
    @Column(name = "pay_no")
    private String payNo;

    @Schema(description = "第三方支付单号")
    @Column(name = "thirtparty_pay_no")
    private String thirtpartyPayNo;

    @Schema(description = "充值名称（商品名称）")
    @Column(name = "title")
    private String title;

    @Schema(description = "企业订单id，如果是企业订单发起的充值则有此参数")
    @Column(name = "order_id")
    private Long orderId;

    @Schema(description = "充值金额")
    @Column(name = "recharge_amount")
    private Integer rechargeAmount;

    @Schema(description = "服务费金额")
    @Column(name = "service_amount")
    private Integer serviceAmount;

    @Schema(description = "合计金额")
    @Column(name = "total_amount")
    private Integer totalAmount;

    @Schema(description = "服务费率")
    @Column(name = "service_rate")
    private Float serviceRate;


    @Schema(description = "第三方支付状态")
    @Column(name = "thirtparty_status")
    private String thirtpartyStatus;

    @Column(name = "mock_recharge")
    private Integer mockRecharge = 0;

    @Schema(description = "过期时间")
    @Column(name = "expire_time")
    private Date expireTime;

    @Schema(description = "支付时间")
    @Column(name = "pay_time")
    private Date payTime;

    @Schema(description = "原始响应内容")
    @Column(name = "thirtpartyResponse")
    private String thirtpartyResponse;

    @Schema(description = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;

    @Transient
    @Schema(hidden = true,description = "微信支付商品名称不能包含敏感词，这里使用别名")
    private String titleAlias;
}