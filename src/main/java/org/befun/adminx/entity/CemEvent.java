package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.CemWorkerBase;
import org.befun.adminx.dto.event.EventType;

import javax.persistence.*;

@Entity
@Setter
@Getter
@Table(name = "worker_event",catalog = "cem_platform")
@NoArgsConstructor
public class CemEvent extends CemWorkerBase {
    @Schema(description = "事件类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private EventType type;

    public CemEvent(EventType type) {
        this.type = type;
    }
}
