package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "wework_seller_config", catalog = "cem_platform")
//@AllArgsConstructor
@NoArgsConstructor
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class WeworkSellerConfig extends BaseEntity {

    @DtoProperty(description = "坐席", jsonView = ResourceViews.Basic.class)
    @Column(name = "users")
    private String users;

    @DtoProperty(description = "名称", jsonView = ResourceViews.Basic.class)
    @Column(name = "name")
    private String name;

    @DtoProperty(description = "标签ID", jsonView = ResourceViews.Basic.class)
    @Column(name = "tag_id")
    private String tagId;

    @DtoProperty(description = "版本延长天数", jsonView = ResourceViews.Basic.class)
    @Column(name = "extend_days")
    private Integer extendDays;

    @DtoProperty(description = "是否开启", jsonView = ResourceViews.Basic.class)
    @Schema(name = "enable", description = "是否开启")
    private Boolean enable = false;

    @DtoProperty(description = "欢迎语模版", jsonView = ResourceViews.Basic.class)
    @Column(name = "template")
    private String template;

}
