package org.befun.adminx.entity;

import lombok.*;
import org.befun.adminx.converter.StringTimestampConverter;
import org.befun.adminx.dto.ext.XmPlusUserExDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:08
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "is_admin=1")
@Table(name = "user", catalog = "survey_pro")
@NamedEntityGraph(name = "spUser.all", attributeNodes = {@NamedAttributeNode("organization")})
@DtoClass()
public class SPlusUser extends BaseEntity {

//    @Column(name = "org_id")
//    @DtoProperty(description = "组织ID", example = "", jsonView = ResourceViews.Basic.class)
//    private Long orgId;

    @Column(name = "role_id")
    @DtoProperty(description = "角色ID", example = "", jsonView = ResourceViews.Basic.class)
    private String roleId;

    @Column(name = "username")
    @DtoProperty(description = "用户名", example = "", jsonView = ResourceViews.Basic.class)
    private String userName;

    @Column(name = "truename")
    @DtoProperty(description = "真实姓名", example = "", jsonView = ResourceViews.Basic.class)
    private String trueName;

    @Column(name = "email")
    @DtoProperty(description = "email", example = "", jsonView = ResourceViews.Basic.class)
    private String email;

    @Column(name = "mobile")
    @DtoProperty(description = "mobile", example = "", jsonView = ResourceViews.Basic.class)
    private String mobile;

    @Column(name = "is_admin")
    @DtoProperty(description = "系统管理员 0-不是 1-是", example = "", jsonView = ResourceViews.Basic.class)
    private Integer isAdmin;

    @Column(name = "status")
    @DtoProperty(description = "1-启用 2-禁用 3-待激活", example = "", jsonView = ResourceViews.Basic.class)
    private Integer status = 1;

    @Convert(converter = StringTimestampConverter.class)
    @Column(name = "latest_login")
    @DtoProperty(description = "最后登录时间，时间戳", example = "", jsonView = ResourceViews.Basic.class)
    private String latestLogin = null;

    @Column(name = "ip")
    @DtoProperty(description = "ip", example = "", jsonView = ResourceViews.Basic.class)
    private String ip;

    @Column(name = "province")
    @DtoProperty(description = "province", example = "", jsonView = ResourceViews.Basic.class)
    private String province;

    @Column(name = "city")
    @DtoProperty(description = "city", example = "", jsonView = ResourceViews.Basic.class)
    private String city;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "org_id")
    @DtoProperty(description = "企业信息", example = "", jsonView = ResourceViews.Basic.class)
    private SpOrganization organization = null;

}


