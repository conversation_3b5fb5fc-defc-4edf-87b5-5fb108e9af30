package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/13 17:22
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "reply_message")
@DtoClass()
public class ReplyMessage extends BaseEntity {

    @Column(name = "keyword", columnDefinition = "varchar(50)")
    @DtoProperty(description = "关键字", example = "", jsonView = ResourceViews.Basic.class)
    private String keyword;

    @Column(name = "content", columnDefinition = "varchar(100)")
    @DtoProperty(description = "回复内容", example = "", jsonView = ResourceViews.Basic.class)
    private String content;


}
