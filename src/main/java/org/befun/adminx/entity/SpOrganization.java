package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.adminx.converter.VersionConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:08
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "organization", catalog = "survey_pro")
@DtoClass(includeAllFields = true)
public class SpOrganization extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private SPlusUser spUser;

    @Column(name = "name")
    @DtoProperty(description = "企业名称", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Column(name = "code")
    @DtoProperty(description = "企业代码", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String code;

    @Convert(converter = VersionConverter.class)
    @Column(name = "version")
    @DtoProperty(description = "版本", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String version;

    @Temporal(TemporalType.DATE)
    @Column(name = "available_date_begin")
    @DtoProperty(description = "开始时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @JsonView(ResourceViews.Basic.class)
    private Date availableDateBegin;

    @Temporal(TemporalType.DATE)
    @Column(name = "available_date_end")
    @DtoProperty(description = "截止时间", example = "", jsonView = ResourceViews.Basic.class)
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @JsonView(ResourceViews.Basic.class)
    private Date availableDateEnd;
}


