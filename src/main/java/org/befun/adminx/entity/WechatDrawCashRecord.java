package org.befun.adminx.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.adminx.constant.BillStates;
import org.befun.adminx.constant.WechatDrawCashType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/3/10 14:23
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@Table(name = "wechat_draw_cash_record")
@DtoClass()
public class WechatDrawCashRecord extends BaseEntity {

    @Column(name = "openid")
    @DtoProperty(description = "微信openid", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @DtoProperty(description = "0现金红包，1提现到余额", example = "", jsonView = ResourceViews.Basic.class)
    private WechatDrawCashType type;

    @DtoProperty(description = "金额 单位分", example = "", jsonView = ResourceViews.Basic.class)
    private int amount;

    @Column(name = "mch_billno")
    @DtoProperty(description = "微信订单号", example = "", jsonView = ResourceViews.Basic.class)
    private String mchBillNo;

    @Column(name = "return_code")
    @DtoProperty(description = "返回状态码", example = "", jsonView = ResourceViews.Basic.class)
    private String returnCode;

    @Column(name = "return_msg")
    @DtoProperty(description = "返回信息", example = "", jsonView = ResourceViews.Basic.class)
    private String returnMsg;

    @Column(name = "billno")
    @DtoProperty(description = "订单号", example = "", jsonView = ResourceViews.Basic.class)
    private String billNo;

    @Column(name = "package_info")
    @DtoProperty(description = "领取页面的package信息", example = "", jsonView = ResourceViews.Basic.class)
    private String packageInfo;

    @Schema(description = "提现记录状态")
    @Column(name = "states")
    @Enumerated(EnumType.STRING)
    private BillStates states = BillStates.ACCEPTED;

    public WechatDrawCashRecord(String openId, WechatDrawCashType type, int amount, String mchBillNo,
            String returnCode, String returnMsg, String billNo, String packageInfo, BillStates states) {
        this.openId     = openId;
        this.type       = type;
        this.amount     = amount;
        this.mchBillNo  = mchBillNo;
        this.returnCode = returnCode;
        this.returnMsg  = returnMsg;
        this.billNo = billNo;
        this.packageInfo = packageInfo;
        this.states = states;
    }
    public WechatDrawCashRecord(){}

}
