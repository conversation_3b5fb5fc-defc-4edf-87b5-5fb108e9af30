package org.befun.adminx.entity;

import lombok.*;
import org.befun.adminx.constant.AuditScriptLanguage;
import org.befun.adminx.constant.AutoAuditType;
import org.befun.adminx.entity.dto.AuditRule;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.hibernate.types.annotation.NestedColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "audit")
@DtoClass()
public class Audit extends BaseEntity {
    @Column(name = "s_id", columnDefinition = "varchar")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private String sid;

    @Column(name = "name", columnDefinition = "varchar")
    @DtoProperty(description = "审核名称", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "audit_type", columnDefinition = "int")
    @DtoProperty(description = "自动审核类型", example = "", jsonView = ResourceViews.Basic.class)
    private AutoAuditType auditType = AutoAuditType.SCORE_RULE;

    @Column(name = "code", columnDefinition = "varchar(5000)")
    @DtoProperty(description = "审核代码", example = "")
    private String code;

    @Column(name = "language", columnDefinition = "int default 0")
    @DtoProperty(description = "脚本语言", example = "", jsonView = ResourceViews.Basic.class)
    private AuditScriptLanguage language = AuditScriptLanguage.R;

    /* 审核规则 */
    @NestedColumn(AuditRule.class)
    @Type(type = "org.befun.core.hibernate.types.NestedEntityList")
    @Column(name = "rules", columnDefinition = "varchar(1000)")
    @DtoProperty(description = "审核规则", example = "", jsonView = ResourceViews.Basic.class)
    private List<AuditRule> rules = new ArrayList<>();

    @Column(name = "threshold", columnDefinition = "int default 0")
    @DtoProperty(description = "阈值", example = "", jsonView = ResourceViews.Basic.class)
    private Integer threshold = 0;

    @Column(name = "enable_similarity_check")
    @DtoProperty(description = "是否开启相似度检查", example = "true", jsonView = ResourceViews.Basic.class)
    private Boolean enableSimilarityCheck = false;

    @Column(name = "similarity_percent")
    @DtoProperty(description = "相似度系数 百分比", example = "80", jsonView = ResourceViews.Basic.class)
    private Integer similarityPercent = 0;

    @Column(name = "similarity_count", columnDefinition = "int default 0")
    @DtoProperty(description = "相似条数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer similarityCount = 0;

    @Column(name = "response_percent")
    @DtoProperty(description = "答卷权重", example = "80", jsonView = ResourceViews.Basic.class)
    private Integer responsePercent = 0;

    @Column(name = "additional_percent")
    @DtoProperty(description = "资料权重", example = "80", jsonView = ResourceViews.Basic.class)
    private Integer additionalPercent = 0;

}