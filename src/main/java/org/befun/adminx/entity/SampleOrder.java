package org.befun.adminx.entity;

import lombok.*;
import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.dto.ext.SampleOrderExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/10 11:39
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "sample_order")
@DtoClass(includeAllFields = true, superClass = SampleOrderExtDto.class)
public class SampleOrder extends BaseEntity {

    @Column(name = "s_id", columnDefinition = "long")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private Long surveyId;

    @Column(name = "org_id", columnDefinition = "long")
    @DtoProperty(description = "组织id", example = "", jsonView = ResourceViews.Basic.class)
    private Long orgId;

    @Column(name = "survey_title", columnDefinition = "varchar")
    @DtoProperty(description = "问卷别名：编辑端列表显示", example = "", jsonView = ResourceViews.Basic.class)
    private String surveyTitle;

    @Column(name = "survey_real_title", columnDefinition = "varchar")
    @DtoProperty(description = "问卷标题：答题端显示", example = "", jsonView = ResourceViews.Basic.class)
    private String surveyRealTitle;

    @Column(name = "channel_id", columnDefinition = "long")
    @DtoProperty(description = "渠道id", example = "", jsonView = ResourceViews.Basic.class)
    private Long channelId;

    @Column(name = "channel_name", columnDefinition = "varchar")
    @DtoProperty(description = "渠道名称", example = "", jsonView = ResourceViews.Basic.class)
    private String channelName;

    @Column(name = "channel_status", columnDefinition = "varchar")
    @DtoProperty(description = "渠道状态", example = "", jsonView = ResourceViews.Basic.class)
    private ChannelStatus channelStatus;

    @Column(name = "quantity", columnDefinition = "int")
    @DtoProperty(description = "样本数量", example = "", jsonView = ResourceViews.Basic.class)
    private Integer quantity = 0;

    @Column(name = "recycle", columnDefinition = "int")
    @DtoProperty(description = "回收数量", example = "", jsonView = ResourceViews.Basic.class)
    private Integer recycle = 0;

    @Column(name = "count_question", columnDefinition = "int")
    @DtoProperty(description = "问卷题目数量", example = "", jsonView = ResourceViews.Basic.class)
    private Integer countQuestion = 0;

    // 需要保存小数部分，
    @Deprecated(since = "1.10.9")
    @Column(name = "unit_price", columnDefinition = "int")
    @DtoProperty(description = "样本单价", example = "", jsonView = ResourceViews.Basic.class)
    private Integer unitPrice = 0;

    @Column(name = "unit_price2", columnDefinition = "int")
    @DtoProperty(description = "样本单价", example = "", jsonView = ResourceViews.Basic.class)
    private BigDecimal unitPrice2;

    @Column(name = "total_price", columnDefinition = "varchar")
    @DtoProperty(description = "订单总价", example = "", jsonView = ResourceViews.Basic.class)
    private String totalPrice;

    @Column(name = "refund_price", columnDefinition = "varchar")
    @DtoProperty(description = "退款金额", example = "", jsonView = ResourceViews.Basic.class)
    private String refundPrice;

    @Column(name = "contacts", columnDefinition = "varchar")
    @DtoProperty(description = "跟进人", example = "", jsonView = ResourceViews.Basic.class)
    private String contacts;

    @Column(name = "is_contacted", columnDefinition = "boolean")
    @DtoProperty(description = "是否联系", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean isContacted = false;

    @Column(name = "gender_match", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-性别", example = "", jsonView = ResourceViews.Basic.class)
    private String genderMatch;

    @Column(name = "age_match", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-年龄", example = "", jsonView = ResourceViews.Basic.class)
    private String ageMatch;

    @Column(name = "education_match", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-学历", example = "", jsonView = ResourceViews.Basic.class)
    private String educationMatch;

    @Column(name = "location_match", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-地区", example = "", jsonView = ResourceViews.Basic.class)
    private String locationMatch;

    @Column(name = "other", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-其他", example = "", jsonView = ResourceViews.Basic.class)
    private String other;

    @Column(name = "detail", columnDefinition = "varchar")
    @DtoProperty(description = "样本特征-待报价内容", example = "", jsonView = ResourceViews.Basic.class)
    private String detail;

    @DtoProperty(description = "支付时间", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "pay_time")
    private Date payTime;

    @DtoProperty(description = "支付方式", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "pay_type")
    private String payType;

    @DtoProperty(description = "支付订单号", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "pay_no")
    private String payNo;

    @DtoProperty(description = "支付退单号", example = "", jsonView = ResourceViews.Basic.class)
    @Column(name = "pay_refund_no")
    private String payRefundNo;

    @Transient
    @Length(max = 500)
    private String rejectMessage;
}
