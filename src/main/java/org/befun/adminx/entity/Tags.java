package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/1 11:17
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "UPDATE tags SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@Table(name = "tags")
@DtoClass()
public class Tags extends BaseEntity {

    @Column(name = "name", columnDefinition = "varchar")
    @DtoProperty(description = "标签名", example = "", jsonView = ResourceViews.Basic.class)
    private String name;
}
