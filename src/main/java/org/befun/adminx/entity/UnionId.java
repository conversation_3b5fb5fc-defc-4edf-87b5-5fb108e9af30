package org.befun.adminx.entity;

import lombok.*;
import org.befun.adminx.constant.backup.BackupLogType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;


@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "union_id")
@DtoClass()
public class UnionId extends BaseEntity {
    @Column(name = "pending_id_expire_time")
    public Date pendingIdExpireTime;

    @Column(name = "union_id")
    public String unionId;

    @Column(name = "openid")
    public String openId;

    @Column(name = "pending_id")
    public String pendingId;

    @Column(name="external_userid")
    public String externalUserId;

    @Column(name="pass")
    public Boolean pass = false;

    @Column(name="userid")
    public String userId;

    public UnionId(String unionId, String openId, String externalUserId, String pendingId){
        this.unionId = unionId;
        this.openId = openId;
        this.externalUserId = externalUserId;
        this.pendingId = pendingId;
    }
}
