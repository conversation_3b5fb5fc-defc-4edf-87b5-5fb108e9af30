package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "address")
@DtoClass()
@AttributeOverride(name = "id", column = @Column(name = "id"))
public class Address extends BaseEntity {

    @Column(name = "name")
    @DtoProperty(description = "省/城市名称", example = "", jsonView = ResourceViews.Basic.class)
    private String name = "";

    @Column(name = "level")
    @DtoProperty(description = "级别(0:省,1:市,2:区)", example = "", jsonView = ResourceViews.Basic.class)
    private Integer level;

    @Column(name = "parent_id")
    @DtoProperty(description = "pid", example = "", jsonView = ResourceViews.Basic.class)
    private Long parentId;
}


