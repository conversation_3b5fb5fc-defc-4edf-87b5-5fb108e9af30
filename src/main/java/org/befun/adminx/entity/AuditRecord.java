package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.befun.adminx.constant.AuditType;
import org.befun.adminx.constant.TaskType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "audit_record")
@DtoClass()
public class AuditRecord extends BaseEntity {
    /* 任务 */
    @Column(name = "task_id", columnDefinition = "bigint not null")
    @JsonIgnore
    @DtoProperty(description = "任务id", example = "", jsonView = ResourceViews.Basic.class, access = JsonProperty.Access.WRITE_ONLY, type = DeliveryTaskDto.class)
    private Long taskId;

    /* 答题ID */
    @DtoProperty(description = "答卷id", example = "", jsonView = ResourceViews.Basic.class)
    private Long responseId;

    /* 用户ID */
    @DtoProperty(description = "微信openid", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @Column(columnDefinition = "bit default 0")
    @DtoProperty(description = "is passed", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean isPassed = false;

    @Column(columnDefinition = "bit default 0")
    @DtoProperty(description = "审核方式", example = "", jsonView = ResourceViews.Basic.class)
    private AuditType type = AuditType.MANUAL;

    @DtoProperty(description = "用户cuid", example = "", jsonView = ResourceViews.Basic.class)
    private Long cuid;

    @DtoProperty(description = "任务名称", example = "", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "s_id")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private Long sid;

    @DtoProperty(description = "评分卡得分", example = "", jsonView = ResourceViews.Basic.class)
    private Integer score;

    @Column(name = "fail_rule_name")
    @DtoProperty(description = "审核失败的规则名", example = "", jsonView = ResourceViews.Basic.class)
    private String failRuleName;

    @DtoProperty(description = "审核失败日志", example = "", jsonView = ResourceViews.Basic.class)
    private String log;

    @DtoProperty(description = "审核规则json", example = "", jsonView = ResourceViews.Basic.class)
    private String items;

    @Column(name = "task_type")
    @Enumerated
    @DtoProperty(description = "任务类型", example = "", jsonView = ResourceViews.Basic.class)
    private TaskType taskType = TaskType.DELIVERY;

    public AuditRecord(Long responseId, String openId, Long cuid, DeliveryTaskDto taskDto) {
        this.isPassed = true;
        this.responseId = responseId;
        this.openId = openId;
        this.taskId = taskDto.getId();
        this.cuid = cuid;
        this.title = taskDto.getTitle();
        this.sid = Long.parseLong(taskDto.getSid());
        this.taskType = taskDto.getTaskType();
    }

    public AuditRecord(Long responseId, String openId, Long cuid, DeliveryTask task) {
        this.isPassed = true;
        this.responseId = responseId;
        this.openId = openId;
        this.taskId = task.getId();
        this.cuid = cuid;
        this.title = task.getTitle();
        this.sid = Long.parseLong(task.getSid());
        this.taskType = task.getTaskType();
    }

}