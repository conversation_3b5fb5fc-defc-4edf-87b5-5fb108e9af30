package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/4 16:31
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_statistics")
@DtoClass()
public class SurveyStatistics extends BaseEntity {

    @Column(name = "date", columnDefinition = "varchar")
    @DtoProperty(description = "日期", example = "2022-08-04", jsonView = ResourceViews.Basic.class)
    private String date;

    @Column(name = "cuid", columnDefinition = "int")
    @DtoProperty(description = "cuid", example = "", jsonView = ResourceViews.Basic.class)
    private Long cuid;

    @Column(name = "total", columnDefinition = "int")
    @DtoProperty(description = "问卷打开次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer total = 0;

    @Column(name = "completed", columnDefinition = "int")
    @DtoProperty(description = "问卷完成次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer completed = 0;

    @Column(name = "quota_full", columnDefinition = "int")
    @DtoProperty(description = "配额结束次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer quotaFull = 0;

    @Column(name = "early_completed", columnDefinition = "int")
    @DtoProperty(description = "提前结束次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer earlyCompleted = 0;

    @Column(name = "audit_pass", columnDefinition = "int")
    @DtoProperty(description = "问卷通过次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer auditPass = 0;

    @Column(name = "audit_fail", columnDefinition = "int")
    @DtoProperty(description = "问卷不通过次数", example = "", jsonView = ResourceViews.Basic.class)
    private Integer auditFail = 0;

    @Column(name = "completed_rate", columnDefinition = "int")
    @DtoProperty(description = "问卷完成率", example = "", jsonView = ResourceViews.Basic.class)
    private Integer completedRate = 0;

    @Column(name = "pass_rate", columnDefinition = "int")
    @DtoProperty(description = "问卷通过率", example = "", jsonView = ResourceViews.Basic.class)
    private Integer passRate = 0;

    public SurveyStatistics attributeSummary(Integer total, Integer completed, Integer quotaFull, Integer earlyCompleted, Integer auditPass, Integer auditFail) {
        this.total += total;
        this.completed += completed;
        this.quotaFull += quotaFull;
        this.earlyCompleted += earlyCompleted;
        this.auditPass += auditPass;
        this.auditFail += auditFail;
        this.completedRate = this.total == 0 ? 0: Integer.valueOf((this.completed / this.total) * 100);
        this.passRate = this.completed == 0 ? 0: Integer.valueOf((this.auditPass / this.completed) * 100);
        return this;
    }

    public SurveyStatistics attributeSummary() {
        this.completedRate = this.total == 0 ? 0: Integer.valueOf((this.completed / this.total) * 100);
        this.passRate = this.completed == 0 ? 0: Integer.valueOf((this.auditPass / this.completed) * 100);
        return this;
    }
}
