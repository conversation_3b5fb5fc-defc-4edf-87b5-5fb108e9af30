package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.adminx.constant.survey.SurveyAuditSource;
import org.befun.adminx.constant.survey.SurveyAuditStatus;
import org.befun.adminx.dto.surveyaudit.SurveyAuditRecordExtDto;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_audit_record")
@DtoClass(includeAllFields = true, superClass = SurveyAuditRecordExtDto.class)
public class SurveyAuditRecord extends BaseEntity {

    @Schema(description = "企业id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "org_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Long orgId;

    @Schema(description = "问卷id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Long surveyId;

    @Enumerated(EnumType.STRING)
    @Schema(description = "类型")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "source")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private SurveyAuditSource source;

    @Schema(description = "来源id: contentAudit, contentAuditId; report, responseId")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "source_id")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Long sourceId;

    @Schema(description = "审核原因")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "reason")
    private String reason = null;

    @Schema(description = "审核内容")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "content")
    private String content = null;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "提交时间")
    @Column(name = "request_time")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Date requestTime = null;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "审核时间")
    @Column(name = "audit_time")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private Date auditTime = null;

    @Enumerated(EnumType.STRING)
    @Schema(description = "审核状态")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "status")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private SurveyAuditStatus status = SurveyAuditStatus.init;

    @Schema(description = "备注")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "remark")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String remark;

}
