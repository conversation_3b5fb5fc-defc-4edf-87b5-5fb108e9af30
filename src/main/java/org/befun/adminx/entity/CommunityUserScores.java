package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "community_user_scores")
@DtoClass()
public class CommunityUserScores extends BaseEntity {

//    @Column(name = "cuid")
//    @DtoProperty(description = "用户uid", example = "", jsonView = ResourceViews.Basic.class)
//    private Long cuid;

    @Column(name = "title")
    @DtoProperty(description = "积分变动", example = "", jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "score_change")
    @DtoProperty(description = "积分增、减值", example = "", jsonView = ResourceViews.Basic.class)
    private Long scoreChange;

    @Column(name = "score")
    @DtoProperty(description = "剩余积分值", example = "", jsonView = ResourceViews.Basic.class)
    private Long score;

    @Column(name = "type")
    @DtoProperty(description = "类型，R回答问卷奖励，U等级升级奖励，P完善个人资料奖励，A完善个人账号信息奖励，M积分兑换码兑换，F冻结积分，F-P冻结积分审核通过，F-N冻结积分审核不通过", example = "R", jsonView = ResourceViews.Basic.class)
    private String type = "R";

    @Column(name = "s_id")
    @DtoProperty(description = "问卷id", example = "", jsonView = ResourceViews.Basic.class)
    private String surveyId;

    @Column(name = "survey_title")
    @DtoProperty(description = "问卷标题", example = "", jsonView = ResourceViews.Basic.class)
    private String surveyTitle;

    @Column(name = "message")
    @DtoProperty(description = "操作信息", example = "", jsonView = ResourceViews.Basic.class)
    private String message;

    @Column(name = "operator")
    @DtoProperty(description = "提现操作人", example = "", jsonView = ResourceViews.Basic.class)
    private String operator;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cuid", nullable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @DtoProperty(description = "cuid", example = "", jsonView = ResourceViews.Basic.class)
    private CommunityUser user;

    @Column(name = "share_id")
    @DtoProperty(description = "分享用户uid", example = "", jsonView = ResourceViews.Basic.class)
    private Long shareId;
}


