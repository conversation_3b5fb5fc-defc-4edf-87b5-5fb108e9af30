package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/9 16:31
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "customer_statistics", catalog = "adminx")
@DtoClass(includeAllFields = true)
@AttributeOverride(name = "id", column = @Column(name = "org_id"))
public class CustomerStatistics extends BaseEntity {

    @Column(name = "survey_quantity", columnDefinition = "int")
    @DtoProperty(description = "问卷总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer surveyQuantity = 0;

    @Column(name = "survey_response_quantity", columnDefinition = "int")
    @DtoProperty(description = "问卷回收总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer surveyResponseQuantity = 0;

    @Column(name = "journey_map_quantity", columnDefinition = "int")
    @DtoProperty(description = "旅程总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer journeyMapQuantity = 0;

    @Column(name = "event_quantity", columnDefinition = "int")
    @DtoProperty(description = "预警总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer eventQuantity = 0;

    @Column(name = "bi_dashboard_quantity", columnDefinition = "int")
    @DtoProperty(description = "BI看板总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer biDashBoardQuantity = 0;

    @Column(name = "customer_quantity", columnDefinition = "int")
    @DtoProperty(description = "客户总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer customerQuantity = 0;

    @Column(name = "account_quantity", columnDefinition = "int")
    @DtoProperty(description = "账号总数", example = "", jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private Integer accountQuantity = 0;

}
