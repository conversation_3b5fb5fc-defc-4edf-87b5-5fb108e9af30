package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "role", catalog = "cem_platform")
@Where(clause = "platform='cem'")
public class XmRole extends EnterpriseEntity {

    @DtoProperty(description = "角色名称", queryable = true, jsonView = ResourceViews.Basic.class)
    private String name;

    @DtoProperty(description = "角色描述", queryable = true, jsonView = ResourceViews.Basic.class)
    private String description;

    @DtoProperty(description = "角色平台", jsonView = ResourceViews.Basic.class)
    private String platform;

    @DtoProperty(description = "是否可以编辑：0 不可以 1 可以编辑删除 2 只能编辑", jsonView = ResourceViews.Basic.class)
    private Integer editable = 0;

    @DtoProperty(description = "角色类型：1 超级管理员(不能编辑删除) 3 成员(只能编辑) 4 其他(可以编辑删除)", jsonView = ResourceViews.Basic.class)
    private Integer type;

    private Integer created;
    private Integer updated;


}
