package org.befun.adminx.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 11:17
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "feature")
@DtoClass()
public class Feature extends BaseEntity {
    @Column(name = "name", columnDefinition = "varchar(100)")
    @DtoProperty(description = "代码", example = "", jsonView = ResourceViews.Basic.class)
    private String name = "";

    @Column(name = "title", columnDefinition = "varchar(100)")
    @DtoProperty(description = "名称", example = "", jsonView = ResourceViews.Basic.class)
    private String title = "";

    @Column(name = "enabled", columnDefinition = "bit")
    @DtoProperty(description = "是否开启", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enabled = false;

    @Column(name = "award_percent")
    @DtoProperty(description = "奖励系数", example = "", jsonView = ResourceViews.Basic.class)
    private Double awardPercent = 0.1;

}













