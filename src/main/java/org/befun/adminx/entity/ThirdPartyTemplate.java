package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.core.converter.MapListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/5/28 13:41
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "thirdparty_template")
@DtoClass()
public class ThirdPartyTemplate extends BaseEntity {

    @Column(name = "name", columnDefinition = "varchar")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "模版名称", example = "", jsonView = ResourceViews.Basic.class)
    private String name;

    @Column(name = "description", columnDefinition = "varchar")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "模版描述", example = "", jsonView = ResourceViews.Basic.class)
    private String description;

    @Column(name = "example", columnDefinition = "varchar")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "模版示例", example = "", jsonView = ResourceViews.Basic.class)
    private String example;

    @Convert(converter = MapListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "模版示例", example = "", jsonView = ResourceViews.Basic.class)
    private List<Map<String, Object>> parameters = new ArrayList<>();

    @Column(name = "open_id")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "第三方模版id", example = "", jsonView = ResourceViews.Basic.class)
    private String openId;

    @Column(name = "signature_id")
    @JsonView(ResourceViews.Detail.class)
    @DtoProperty(description = "模版签名id", example = "", jsonView = ResourceViews.Detail.class)
    private String signatureId;

    @Column(name = "send_method")
    @Enumerated
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(description = "发送方式", example = "",queryable = true, jsonView = ResourceViews.Basic.class)
    private TaskSendMethod sendMethod;

    @Schema(description = "模板状态：0 待审核 1 审核通过")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Detail.class)
    private Boolean status = false;

    @Enumerated
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "通知类型 0问卷接受通知,1积分变动通知")
    private SendType type = SendType.SEND_SURVEY;
}
