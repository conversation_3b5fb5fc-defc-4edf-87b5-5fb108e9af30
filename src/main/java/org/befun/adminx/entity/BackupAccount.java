package org.befun.adminx.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;
import java.util.*;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 11:17
 */
@Getter
@Setter
@Entity
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "backup_account")
@DtoClass()
public class BackupAccount extends BaseEntity {

    @Column(name = "enabled", columnDefinition = "bit")
    @DtoProperty(description = "是否开启", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enabled = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "org_id")
    @DtoProperty(description = "企业信息", example = "", jsonView = ResourceViews.Basic.class)
    private Organization organization = null;

    @Column(name = "file", columnDefinition = "varchar")
    @DtoProperty(description = "文件", example = "", jsonView = ResourceViews.Basic.class)
    private String file;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView({ResourceViews.Basic.class})
    @Column(
            name = "last_backup_time",
            updatable = true,
            columnDefinition = "TIMESTAMP"
    )
    @DtoProperty(description = "最近备份时间", example = "", jsonView = ResourceViews.Basic.class)
    private Date lastBackupTime;

//    @OneToMany(fetch = FetchType.LAZY)
//    @DtoProperty(ignore = true)
//    private List<BackupLog> logs;

    @OneToMany(mappedBy = "account", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(ignore = true)
    private List<BackupLog> logs;
}
