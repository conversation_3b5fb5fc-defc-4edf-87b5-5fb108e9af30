package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionItem;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SurveyQuestionItemRepository extends ResourceRepository<SurveyQuestionItem, Long> {
    void deleteAllByQuestion(SurveyQuestion question);

    @Query(nativeQuery = true,
            value = "select id,q_id,text,value from survey_question_item where q_id in (?1)")
    List<Map<String, Object>> findAllSimpleQuestionItem(List<Long> ids);

    void deleteByIdIn(List<Long> ids);

    List<SurveyQuestionItem> findAllByQuestionOrderBySequence(SurveyQuestion question);
}
