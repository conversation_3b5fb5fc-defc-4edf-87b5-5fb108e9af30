package org.befun.adminx.repository;

import org.befun.adminx.entity.Organization;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/11 16:00
 */
@Repository
public interface OrganizationRepository extends ResourceRepository<Organization, Long> {
    Optional<Organization> findOneById(Long orgId);
    List<Organization> findByAvailableDateEndBefore(Date availableDateEnd);

}
