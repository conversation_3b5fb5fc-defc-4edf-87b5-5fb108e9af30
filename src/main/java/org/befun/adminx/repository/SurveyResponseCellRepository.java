package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SurveyResponseCellRepository extends ResourceRepository<BaseEntity, Number> {

    List<SurveyResponseCell> findAllBySurveyIdAndResponseId(Long surveyId, Long responseId);

    List<SurveyResponseCell> findAllBySurveyIdAndResponseIdIn(Long surveyId, List<Long> responseIds);

    List<SurveyResponseCell> findBySurveyIdAndResponseIdBetween(Long surveyId, Long responseIdStart, Long responseIdEnd);
}
