package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.SurveyCompletedRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface SurveyCompletedRecordRepository extends ResourceRepository<SurveyCompletedRecord, Long> {
    Optional<SurveyCompletedRecord> findFirstBySidAndOpenId(String surveyId, String openId);

    List<SurveyCompletedRecord> findAllByOpenIdAndSidIn(String openId, List<String> sids);

    Optional<SurveyCompletedRecord> findFirstBySidAndResponseIdOrderByIdDesc(String surveyId, Long responseId);

    long countByOpenId(String openId);

    List<SurveyCompletedRecord> findBySidAndGridId(String sid,String gridId);

    SurveyCompletedRecord findFirstBySidAndCuid(String sid,Long cuid);

    int countBySidAndGridId(String sid,String gridId);

    @Query(nativeQuery = true,value = "select response_id from survey_completed_record where open_id =?1")
    List<Long> findResponseIdsByOpenId(String openId);

    @Transactional
    void deleteBySidAndOpenId(String surveyId,String openId);

    @Transactional
    void deleteBySidAndResponseId(String surveyId,Long responseId);

    @Query(nativeQuery = true,
            value = "SELECT count(scr.cuid) FROM survey_completed_record scr LEFT JOIN audit_record ar ON scr.task_id = ar.task_id AND scr.response_id = ar.response_id AND scr.s_id = ar.s_id AND ar.is_passed = 1 WHERE scr.cuid != ?2 AND scr.task_id = ?1 AND scr.invite_id = ?2 ;")
    int countByTaskIdAndInviteId(String taskId, Long inviteId);
}
