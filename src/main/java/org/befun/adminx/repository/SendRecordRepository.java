package org.befun.adminx.repository;

import org.befun.adminx.constant.TaskSendType;
import org.befun.adminx.entity.SendRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 09:59
 */
@Repository
public interface SendRecordRepository extends ResourceRepository<SendRecord, Long> {
    @Modifying
    @Query("update SendRecord set latest = 0 WHERE taskId = ?1")
    void updateLatestTaskSend(Long taskId);

    List<SendRecord> findByTaskIdInAndLatestIsTrue(Collection<Long> taskIds);

    Optional<SendRecord> findFirstByTaskIdAndLatestIsTrueOrderByIdDesc(Long taskId);

    List<SendRecord> findByTaskIdAndTaskSendTypeOrderByCreateTimeDesc(Long taskId, TaskSendType taskSendType);

}
