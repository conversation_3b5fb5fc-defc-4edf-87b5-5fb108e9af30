package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.Survey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface SurveyRepository extends ResourceRepository<Survey, Long> {
    Optional<List<Survey>> findAllByOrgIdAndAndUserId(Long orgId, Long userId);
}
