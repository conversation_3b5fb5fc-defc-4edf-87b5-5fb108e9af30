package org.befun.adminx.repository;

import org.befun.adminx.constant.SubmitStatus;
import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface SurveySendRecordRepository extends ResourceRepository<SurveySendRecord, Long> {
    Optional<SurveySendRecord> findById(Long id);

    Optional<SurveySendRecord> findFirstByTaskId(Long taskId);

    Optional<SurveySendRecord> findFirstByTaskIdAndCuid(Long taskId,Long cuid);

    Optional<SurveySendRecord> findFirstByTaskIdAndCuidOrderByIdDesc(Long taskId, Long cuid);

    List<SurveySendRecord> findByIdInAndTaskIdAndStatusIsFalse(Collection<Long> ids, Long taskId);

    List<SurveySendRecord> findByIdInAndTaskId(Collection<Long> ids, Long taskId);

    List<SurveySendRecord> findByTaskId(Long taskId);

    List<SurveySendRecord> findByCuidAndTaskIdIn(Long cuid,List<Long> taskIds);

    @Transactional
    @Modifying
    @Query("update SurveySendRecord s set s.submitStatus = ?1, s.responseStatus = ?2 where s.taskId = ?3 and s.clientId = ?4")
    int updateSubmitStatusByTaskIdAndClientId(SubmitStatus submitStatus, ResponseStatus responseStatus, Long taskId, String clientId);
    @Transactional
    @Modifying
    @Query("update SurveySendRecord s set s.submitStatus = ?1, s.responseStatus = ?2 where s.taskId = ?3 and s.cuid = ?4")
    void updateSubmitStatusByTaskIdAndCuid(SubmitStatus submitStatus, ResponseStatus responseStatus, Long taskId, Long cuid);

    @Transactional
    @Modifying
    @Query("update SurveySendRecord s set s.submitStatus = ?1, s.responseStatus = ?2, s.status = ?5 where s.taskId = ?3 and s.cuid = ?4")
    void updateSubmitStatusByTaskIdAndCuid(SubmitStatus submitStatus, ResponseStatus responseStatus, Long taskId, Long cuid,Boolean completeStatus);
}
