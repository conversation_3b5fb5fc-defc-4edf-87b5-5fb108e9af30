package org.befun.adminx.repository;

import org.befun.adminx.constant.QuotaChannelType;
import org.befun.adminx.entity.survey.SurveyQuota;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SurveyQuotaRepository extends ResourceRepository<SurveyQuota, Long> {

    List<SurveyQuota> findBySidAndChannelType(Long sid, QuotaChannelType type);


    List<SurveyQuota> findBySid(Long sid);

}
