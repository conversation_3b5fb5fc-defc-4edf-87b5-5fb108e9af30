package org.befun.adminx.repository;

import org.befun.adminx.constant.survey.ChannelType;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface SurveyChannelRepository extends ResourceRepository<SurveyChannel, Long> {
    List<SurveyChannel> findAllBySidAndType(Long sid, ChannelType type);
    List<SurveyChannel> findAllBySid(Long surveyId);
}
