package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.SurveyBehaviorRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/26 16:09
 */
@Repository
public interface SurveyBehaviorRecordRepository extends ResourceRepository<SurveyBehaviorRecord,Long> {
    Optional<SurveyBehaviorRecord> findByResponseIdAndPage(Long responseId, Integer page);

    List<SurveyBehaviorRecord> findByResponseId(Long responseId);

    List<SurveyBehaviorRecord> findByResponseIdInOrderByPage(Collection<Long> responseIds);

    List<SurveyBehaviorRecord> findAllBySurveyId(Long sid);

    SurveyBehaviorRecord findFirstBySurveyId(Long surveyId);

    List<SurveyBehaviorRecord> findBySurveyIdAndResponseIdBetween(Long surveyId, Long responseIdStart, Long responseIdEnd);

    List<SurveyBehaviorRecord> findBySurveyIdAndResponseIdIn(Long surveyId, Collection<Long> responseIds);

}
