package org.befun.adminx.repository;

import org.befun.adminx.constant.survey.QuestionRandomType;
import org.befun.adminx.entity.survey.SurveyRandomResult;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface SurveyRandomResultRepository extends ResourceRepository<SurveyRandomResult, Long> {

    List<SurveyRandomResult> findBySurveyId(Long sid);

    Optional<SurveyRandomResult> findFirstBySurveyId(Long surveyId);

    boolean existsBySurveyIdAndType(Long surveyId, QuestionRandomType type);

    List<SurveyRandomResult> findBySurveyIdAndResponseIdBetween(Long surveyId, Long responseIdStart, Long responseIdEnd);

    List<SurveyRandomResult> findBySurveyIdAndResponseIdIn(Long surveyId, Collection<Long> responseIds);
}
