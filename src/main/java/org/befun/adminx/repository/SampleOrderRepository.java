package org.befun.adminx.repository;

import org.befun.adminx.constant.survey.ChannelStatus;
import org.befun.adminx.entity.SampleOrder;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/10 11:51
 */
@Repository
public interface SampleOrderRepository extends ResourceRepository<SampleOrder, Long> {
    @Transactional
    @Modifying
    @Query("update SampleOrder s set s.channelStatus = ?1 where s.surveyId = ?2 and s.channelId = ?3")
    void updateChannelStatusBySurveyIdAndChannelId(ChannelStatus channelStatus, Long surveyId, Long channelId);

    SampleOrder findFirstBySurveyIdAndChannelId(Long surveyId, Long channelId);

    @Transactional
    @Modifying
    @Query("update SampleOrder s set s.recycle = ?1 where s.surveyId = ?2 and s.channelId = ?3")
    int updateRecycleBySurveyIdAndChannelId(Integer recycle, Long surveyId, Long channelId);
}
