package org.befun.adminx.repository;

import org.befun.adminx.constant.DeliveryTaskStatus;
import org.befun.adminx.constant.TaskType;
import org.befun.adminx.entity.DeliveryTask;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface DeliveryTaskRepository extends ResourceRepository<DeliveryTask, Long> {
    List<DeliveryTask> findOneBySidAndTaskTypeAndStatusIn(String surveyId, TaskType taskType,List<DeliveryTaskStatus> statusList);
    Optional<DeliveryTask> findFirstBySid(String surveyId, Sort sort);

    List<DeliveryTask> findByTaskTypeAndStatus(TaskType taskType, DeliveryTaskStatus status);

    List<DeliveryTask> findByOpenSupervisorIsTrue();

    DeliveryTask findFirstBySidAndChannelId(String sid, Long channelId);

}
