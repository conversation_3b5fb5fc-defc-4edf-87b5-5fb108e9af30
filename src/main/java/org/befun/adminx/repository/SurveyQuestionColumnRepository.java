package org.befun.adminx.repository;

import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.adminx.entity.survey.SurveyQuestionColumn;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyQuestionColumnRepository extends ResourceRepository<SurveyQuestionColumn, Long> {

    List<SurveyQuestionColumn> findAllByQuestionOrderBySequence(SurveyQuestion question);

}
