package org.befun.adminx.repository.template;


import org.befun.adminx.constant.survey.TemplateType;
import org.befun.adminx.entity.survey.TemplateGroup;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TemplateGroupRepository extends ResourceRepository<TemplateGroup, Long> {

    List<TemplateGroup> findByType(TemplateType type, Sort sort);

    long countByType(TemplateType type);

}
