package org.befun.adminx.repository.template;

import org.befun.adminx.dto.survey.SimpleTemplateQuestionDto;
import org.befun.adminx.entity.survey.TemplateGroup;
import org.befun.adminx.entity.survey.TemplateSurveyQuestion;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TemplateSurveyQuestionRepository extends ResourceRepository<TemplateSurveyQuestion, Long> {

    List<TemplateSurveyQuestion> findAllByOrgId(Long orgId);

    List<SimpleTemplateQuestionDto> findAllByGroupId(Long groupId);
}
