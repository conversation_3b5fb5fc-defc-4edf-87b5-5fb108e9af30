package org.befun.adminx.repository;

import org.befun.adminx.dto.community.SimpleCommunityUserDto;
import org.befun.adminx.dto.grid.GridMemberDetailDto;
import org.befun.adminx.dto.grid.SimpleGridMemberDto;
import org.befun.adminx.dto.sample.SimpleUserDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.QueryHint;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface CommunityUserRepository extends ResourceRepository<CommunityUser, Long> {
    @QueryHints(@QueryHint(name = "org.hibernate.cacheable", value = "false"))
    @Override
    Optional<CommunityUser> findById(Long cuid);

    @QueryHints(@QueryHint(name = "org.hibernate.cacheable", value = "false"))
    Optional<CommunityUser> findByOpenId(String openId);

    Optional<CommunityUser> findFirstByOrderByIdDesc();

    List<CommunityUser> findByOpenIdIsIn(Collection<String> openIds);

    Optional<CommunityUser> findFirstByUserNameOrderByIdDesc(String userName);

    List<SimpleCommunityUserDto> findByOpenIdIsInOrderByIdAsc(Collection<String> openIds);

    List<CommunityUser> findByIdIn(Collection<Long> ids);

    Page<CommunityUser> findByIdInAndGridId(Collection<Long> ids,String gridId,Pageable pageable);

    List<SimpleUserDto> findByInviteIdOrderByIdDesc(Long inviteId);

    Optional<SimpleUserDto> findFirstByMobile(String mobile);

    List<SimpleUserDto> findByGridId(String girdId);

    Page<SimpleGridMemberDto> findAllByGridId(String girdId,Pageable pageable);

    @Transactional
    @Modifying
    @Query("update CommunityUser c set c.status = ?1 where c.id in ?2")
    int updateStatusByIdIn(Integer status, Collection<Long> ids);
}
