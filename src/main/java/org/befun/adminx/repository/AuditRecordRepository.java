package org.befun.adminx.repository;

import org.befun.adminx.entity.AuditRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface AuditRecordRepository extends ResourceRepository<AuditRecord, Long> {
    Optional<AuditRecord> findAllByTaskIdAndResponseId(Long taskId, Long responseId);

    List<AuditRecord> findBySidAndIsPassed(Long sid,Boolean isPass);
}
