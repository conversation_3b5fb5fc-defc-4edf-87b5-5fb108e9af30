package org.befun.adminx.repository;

import org.befun.adminx.constant.survey.ResponseStatus;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.survey.SimpleResponse;
import org.befun.adminx.dto.survey.SurveyResponseStatisticsDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.befun.core.repository.ResourceRepository;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/5/11 16:47
 */
@Repository
public interface SurveyResponseRepository extends ResourceRepository<SurveyResponse, Long> {

    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(Long surveyId, SurveyCollectorMethod method, List<ResponseStatus> downloadStatusList);
    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(Long surveyId);

    List<SimpleResponse> findSimpleBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(Long surveyId, SurveyCollectorMethod method, List<ResponseStatus> downloadStatusList);

    List<SimpleResponse> findSimpledBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(Long surveyId);

    List<SimpleResponse> findSimpledBySurveyIdAndStatusAndCollectorMethodAndIsCompletedIsTrueOrderByCreateTimeAsc(Long surveyId, ResponseStatus status,SurveyCollectorMethod collectorMethod);


    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(Long surveyId, SurveyCollectorMethod method, List<ResponseStatus> downloadStatusList,  Pageable pageable);
    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(Long surveyId, Pageable pageable);

    int  countDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatusInOrderByCreateTimeAsc(Long surveyId, SurveyCollectorMethod method, List<ResponseStatus> downloadStatusList);
    int  countDistinctClientIdBySurveyIdAndIsCompletedIsTrueOrderByCreateTimeAsc(Long surveyId);

    Optional<SurveyResponse> findFirstBySurveyIdAndIsCompletedIsTrueAndStatusAndCollectorMethod(Long surveyId, ResponseStatus status, SurveyCollectorMethod method, Sort sort);
    Optional<SurveyResponse> findFirstBySurveyIdAndIsCompletedIsTrueAndStatus(Long surveyId, ResponseStatus status, Sort sort);

    @Query("select s.openid,s.surveyId,count(s) as total,\n" +
            "sum(case when s.status = 0 then 1 else 0 end) as init,\n" +
            "sum(case when s.status = 1 then 1 else 0 end) as final_submit,\n" +
            "sum(case when s.status = 2 then 1 else 0 end) as early_completed,\n" +
            "sum(case when s.status = 3 then 1 else 0 end) as invalid,\n" +
            "sum(case when s.status = 4 then 1 else 0 end) as deleted,\n" +
            "sum(case when s.status = 5 then 1 else 0 end) as wait_audit,\n" +
            "sum(case when s.status = 6 then 1 else 0 end) as audit_fail,\n" +
            "sum(case when s.status = 7 then 1 else 0 end) as quota_full,\n" +
            "sum(case when s.status = 8 then 1 else 0 end) as audit_error from SurveyResponse s where s.collectorMethod = 5 and s.openid <> '' and s.createTime >= ?1 and s.createTime <= ?2 \n" +
            "group by s.openid,s.surveyId")
    SurveyResponseStatisticsDto countCompleted(@NonNull Date startTime, @NonNull Date endTime);


    // download list
    List<SurveyResponse> findBySurveyIdAndStatusOrderByIdAsc(Long surveyId, ResponseStatus status);

    List<SurveyResponse> findBySurveyIdAndStatusAndIdGreaterThan(Long surveyId, ResponseStatus status, Long id, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndIdIn(Long surveyId, Collection<Long> ids);

    long countBySurveyIdAndStatus(Long surveyId, ResponseStatus status);

    long countBySurveyIdAndStatusInAndCollectorMethod(Long surveyId, List<ResponseStatus> status, SurveyCollectorMethod method);

    List<SimpleResponse> findBySurveyIdAndIsCompletedIsTrueAndCollectorMethodAndStatus(Long surveyId, SurveyCollectorMethod collectorMethod, ResponseStatus status);

    List<SurveyResponse> findBySurveyIdAndOpenidIsInAndStatus(Long surveyId, Collection<String> openIds, ResponseStatus status);

    List<SurveyResponse> findBySurveyIdAndStatus(Long surveyId, ResponseStatus status, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndStatusInAndCollectorMethod(Long surveyId, List<ResponseStatus> status, SurveyCollectorMethod method, Pageable pageable);


}
