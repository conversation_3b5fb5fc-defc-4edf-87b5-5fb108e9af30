package org.befun.adminx.repository;

import org.befun.adminx.entity.Admin;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface AdminRepository extends ResourceRepository<Admin, Long> {
    Optional<Admin> findFirstByUserName(String userName);
    Optional<Admin> findFirstByOrderByIdDesc();
}
