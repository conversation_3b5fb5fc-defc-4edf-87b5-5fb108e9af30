package org.befun.adminx.repository;

import java.util.List;
import java.util.Optional;
import org.befun.adminx.constant.BillStates;
import org.befun.adminx.constant.WechatDrawCashType;
import org.befun.adminx.entity.WechatDrawCashRecord;
import org.befun.core.repository.ResourceRepository;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/3/10 14:49
 */
public interface WechatDrawCashRecordRepository extends ResourceRepository<WechatDrawCashRecord, Long>{
    Optional<WechatDrawCashRecord> findFirstByOpenIdAndTypeAndStatesNotIn(String openId, WechatDrawCashType type, List<BillStates> states);
    Optional<WechatDrawCashRecord> findFirstByOpenIdAndBillNo(String openId, String billNo);
}
