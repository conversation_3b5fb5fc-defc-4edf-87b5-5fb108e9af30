package org.befun.adminx.repository;

import org.befun.adminx.entity.Address;
import org.befun.adminx.entity.UnionId;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface UnionIdRepository extends ResourceRepository<UnionId, Long> {

    UnionId findTopOneByExternalUserId(String externalUserId);

    UnionId findTopOneByUnionId(String externalUserId);

    List<UnionId> findAllByPass(Boolean pass);
}
