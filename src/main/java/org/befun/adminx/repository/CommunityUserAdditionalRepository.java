package org.befun.adminx.repository;

import org.befun.adminx.entity.CommunityUserAdditional;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface CommunityUserAdditionalRepository extends ResourceRepository<CommunityUserAdditional, Long> {
    List<CommunityUserAdditional> findByOrderByIdAsc(Pageable pageable);
}
