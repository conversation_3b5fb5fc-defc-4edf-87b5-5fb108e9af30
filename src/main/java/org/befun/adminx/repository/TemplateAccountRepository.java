package org.befun.adminx.repository;

import org.befun.adminx.entity.TemplateAccount;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 09:59
 */
@Repository
public interface TemplateAccountRepository extends ResourceRepository<TemplateAccount, Long> {
    Optional<TemplateAccount> findFirstByIsDefaultTrue();
}
