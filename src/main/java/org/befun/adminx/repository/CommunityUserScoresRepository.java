package org.befun.adminx.repository;

import org.befun.adminx.entity.CommunityUserScores;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * The interface description
 *
 * <AUTHOR>
 */
@Repository
public interface CommunityUserScoresRepository extends ResourceRepository<CommunityUserScores, Long> {

    @Query(" select sum(scoreChange) from CommunityUserScores where score > 0 and type in ('R','U','P','A')")
    Long sumUserScore();

    // 加合指定cuid和type的积分
    @Query(nativeQuery = true, value = " select IFNULL(sum(score_change),0) from community_user_scores where cuid = ?1 and type =?2 ")
    Long sumScoreByCuidAndType(Long cuid, String type);
}
