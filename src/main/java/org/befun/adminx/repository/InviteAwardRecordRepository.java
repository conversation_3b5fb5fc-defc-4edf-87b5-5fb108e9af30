package org.befun.adminx.repository;

import org.befun.adminx.entity.InviteAwardRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/26 16:02
 */
@Repository
public interface InviteAwardRecordRepository extends ResourceRepository<InviteAwardRecord,Long>{
    Optional<InviteAwardRecord> findFirstByCuid(Long cuid);

}
