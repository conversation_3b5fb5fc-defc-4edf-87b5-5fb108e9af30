package org.befun.adminx.repository;

import org.befun.adminx.entity.SurveyStatistics;
import org.springframework.stereotype.Repository;
import org.befun.core.repository.ResourceRepository;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/4 16:20
 */
@Repository
public interface SurveyStatisticsRepository extends ResourceRepository<SurveyStatistics, Long>{
    Optional<SurveyStatistics> findByDateAndCuid(String date, Long cuid);
    Optional<SurveyStatistics> findByCuid(Long cuid);

}
