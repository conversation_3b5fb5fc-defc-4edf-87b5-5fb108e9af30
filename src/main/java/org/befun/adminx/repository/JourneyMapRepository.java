package org.befun.adminx.repository;

import org.befun.adminx.entity.JourneyMap;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 18:28
 */
@Repository
public interface JourneyMapRepository extends ResourceRepository<JourneyMap, Long> {
    Optional<List<JourneyMap>> findAllByOrgIdAndAndUserId(Long orgId, Long userId);
}
