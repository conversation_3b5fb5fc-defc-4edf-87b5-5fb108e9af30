package org.befun.adminx.repository;

import org.befun.adminx.entity.WechatSubscribeRecord;
import org.befun.core.repository.ResourceRepository;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/2/16 14:37
 */
public interface WechatSubscribeRecordRepository extends ResourceRepository<WechatSubscribeRecord, Long> {
    Optional<WechatSubscribeRecord> findFirstByOpenIdOrderByIdDesc(String openId);
}
