package org.befun.adminx.repository;

import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyQuestion;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface SurveyQuestionRepository extends ResourceRepository<SurveyQuestion, Long> {
    @EntityGraph(value = "QuestionItems.Graph", type = EntityGraph.EntityGraphType.FETCH)
    List<SurveyQuestion> findAllBySurvey(Survey survey);

    List<SurveyQuestion> findAllBySurveyAndSequenceGreaterThanEqual(Survey survey, int sequence);

    Optional<SurveyQuestion> findBySurveyAndName(Survey survey, String name);



    @Modifying
    @Query(nativeQuery = true,
            value = "update `survey_question` set `sequence` = `sequence`+?1 WHERE `s_id` = ?2 AND `sequence` >= ?3")
    void increaseSequenceBySurveyIdAndSequence(int step, Long surveyId, Number sequence);

    @Query(nativeQuery = true,
            value = "select id,s_id,title,type from survey_question where type in (6,11) AND s_id in (?1)")
    List<Map<String,Object>> findAllSimpleQuestion(List<Long> ids);
}
