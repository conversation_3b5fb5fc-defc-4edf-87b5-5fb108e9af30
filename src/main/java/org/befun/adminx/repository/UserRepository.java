package org.befun.adminx.repository;

import org.befun.adminx.entity.XmPlusUser;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 17:08
 */
@Repository
public interface UserRepository extends ResourceRepository<XmPlusUser, Long> {
    Optional<XmPlusUser> findByMobile(String mobile);

    @Transactional
    @Modifying
    @Query("update XmPlusUser x set x.availableSystems = ?1 where x.organization.id = ?2")
    void updateAvailableSystemsByOrganization_Id(String availableSystems, Long id);

    Optional<XmPlusUser> findFirstByOrganization_IdAndStatusOrderByLatestLoginDesc(Long id, Integer status);

    Optional<XmPlusUser> findFirstByOrganization_IdAndIsAdminOrderByIdDesc(Long id, Integer isAdmin);

    List<XmPlusUser> findByIsAdminAndStatus(Integer isAdmin, Integer status);

}
