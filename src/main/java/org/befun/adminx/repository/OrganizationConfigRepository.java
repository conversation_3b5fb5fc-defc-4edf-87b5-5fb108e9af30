package org.befun.adminx.repository;

import org.befun.adminx.entity.OrganizationConfig;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface OrganizationConfigRepository extends ResourceRepository<OrganizationConfig, Long> {

    OrganizationConfig findFirstByOrgIdAndType(Long orgId, String type);
}
