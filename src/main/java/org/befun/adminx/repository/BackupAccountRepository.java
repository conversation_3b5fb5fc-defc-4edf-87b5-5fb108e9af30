package org.befun.adminx.repository;

import org.befun.adminx.entity.BackupAccount;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;
import java.util.*;

/**
 * The class description
 *
 * @Author: winston
 * @Date: 2023/10/10 09:59
 */
@Repository
public interface BackupAccountRepository extends ResourceRepository<BackupAccount, Long> {
    List<BackupAccount> findAllByEnabled(Boolean enabled);
}
