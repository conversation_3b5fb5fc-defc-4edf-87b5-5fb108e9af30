package org.befun.adminx.repository;

import org.befun.adminx.constant.SendType;
import org.befun.adminx.constant.TaskSendMethod;
import org.befun.adminx.entity.ThirdPartyTemplate;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/5/28 14:47
 */
@Repository
public interface ThirdPartyTemplateRepository extends ResourceRepository<ThirdPartyTemplate,Long>{
    ThirdPartyTemplate findFirstBySendMethodAndTypeAndStatusIsTrueOrderByIdDesc(TaskSendMethod sendMethod, SendType type);

    @Transactional
    @Modifying
    @Query("delete from ThirdPartyTemplate t where t.sendMethod = ?1")
    void deleteBySendMethod(TaskSendMethod sendMethod);

    ThirdPartyTemplate findFirstByOpenId(String templateId);

}
