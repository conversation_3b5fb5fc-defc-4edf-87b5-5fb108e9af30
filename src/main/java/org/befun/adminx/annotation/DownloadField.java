package org.befun.adminx.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DownloadField {
    // 表头展示的文字
    String display() default "";

    //展示英文列明
    String displayCode() default "";

    boolean show() default true;
}
