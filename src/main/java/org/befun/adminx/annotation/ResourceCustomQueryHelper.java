package org.befun.adminx.annotation;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ResourceCustomQueryHelper {

    public static <E, P extends ResourceCustomQueryDto> PageRequest pageRequest(P dto) {
        ResourceEntityQueryDto<E> queryDto = dto.transform();
        Sort sort = queryDto.getSorts();
        if (sort.isUnsorted()) {
            sort = Sort.by("id").descending();
        }
        return PageRequest.of(queryDto.getPage() - 1, queryDto.getLimit(), sort);
    }

    public static <E, P extends ResourceCustomQueryDto> Specification<E> specification(P dto, Map<Class<?>, String> subQueryRootProeprtyMap) {
        Map<Class<?>, SubQueryContext> subQueryContextMap = new HashMap<>();
        return (root, query, criteriaBuilder) -> {
            List<Predicate> where = new ArrayList<>();
            for (Field field : dto.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(dto);
                } catch (IllegalAccessException e) {
                    log.error("parseQueryField error", e);
                }
                if (value != null) {
                    if (!rootQuery(root, criteriaBuilder, field, value, where)) {
                        subQuery(root, query, criteriaBuilder, field, subQueryContextMap, subQueryRootProeprtyMap, value);
                    }
                }
            }
            if (!subQueryContextMap.isEmpty()) {
                subQueryContextMap.forEach((k, subQueryContext) -> {
                    where.add(criteriaBuilder.exists(subQueryContext.buildSubQuery()));
                });
            }
            return where.isEmpty() ? null : criteriaBuilder.and(where.toArray(new Predicate[0]));
        };
    }

    private static void subQuery(Root<?> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, Field field, Map<Class<?>, SubQueryContext> subQueryContextMap, Map<Class<?>, String> subQueryRootProeprtyMap, Object value) {
        ResourceCustomSubQueryParam subQueryParam = field.getAnnotation(ResourceCustomSubQueryParam.class);
        if (subQueryParam != null) {
            SubQueryContext context = SubQueryContext.buildSubQueryContext(
                    query,
                    root,
                    criteriaBuilder,
                    subQueryParam.subClass(),
                    subQueryContextMap,
                    subQueryRootProeprtyMap);
            if (context != null) {
                String name = subQueryParam.subProperty();
                if (StringUtils.isEmpty(name)) {
                    name = field.getName();
                }
                ResourceQueryCriteria criteria = new ResourceQueryCriteria(name, value, subQueryParam.operator());
                context.subWhere.add(criteria.toPredicate(context.subRoot, criteriaBuilder));
            }
        }
    }

    private static boolean rootQuery(Root<?> root, CriteriaBuilder criteriaBuilder, Field field, Object value, List<Predicate> where) {
        ResourceCustomQueryParam queryParam = field.getAnnotation(ResourceCustomQueryParam.class);
        if (queryParam != null) {
            String name = queryParam.property();
            if (StringUtils.isEmpty(name)) {
                name = field.getName();
            }
            ResourceQueryCriteria criteria = new ResourceQueryCriteria(name, value, queryParam.operator());
            where.add(criteria.toPredicate(root, criteriaBuilder));
            return true;
        }
        return false;
    }

    public static class SubQueryContext {
        Class<?> subClass;
        Subquery<?> subquery;
        Root<?> subRoot;
        String rootProperty;
        List<Predicate> subWhere = new ArrayList<>();

        public SubQueryContext(Class<?> subClass, Subquery<?> subquery, Root<?> subRoot, String rootProperty) {
            this.subClass = subClass;
            this.subquery = subquery;
            this.subRoot = subRoot;
            this.rootProperty = rootProperty;
        }

        public Subquery<?> buildSubQuery() {
            subquery.where(subWhere.toArray(new Predicate[0]));
            return subquery;
        }

        public static SubQueryContext buildSubQueryContext(CriteriaQuery<?> query, Root<?> root, CriteriaBuilder builder, Class<?> subClass, Map<Class<?>, SubQueryContext> subQueryContextMap, Map<Class<?>, String> subQueryExistProeprtyMap) {
            SubQueryContext context = subQueryContextMap.get(subClass);
            if (context != null) {
                return context;
            }
            String property = subQueryExistProeprtyMap.get(subClass);
            if (property == null) {
                return null;
            }
            Subquery<?> subquery = query.subquery(subClass);
            Root<?> subRoot = subquery.from(subClass);
            subquery.select(subRoot.get("id"));
            context = new SubQueryContext(subClass, subquery, subRoot, property);
            subQueryContextMap.put(subClass, context);
            context.subWhere.add(builder.equal(subRoot.get("id"), root.get(property)));
            return context;
        }
    }
}
