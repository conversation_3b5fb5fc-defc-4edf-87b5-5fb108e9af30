package org.befun.adminx.annotation;

import org.befun.core.constant.QueryOperator;
import org.befun.core.entity.BaseEntity;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ResourceCustomQueryParam {

    String property() default "";// 默认和参数同名

    QueryOperator operator() default QueryOperator.EQUAL;
}
