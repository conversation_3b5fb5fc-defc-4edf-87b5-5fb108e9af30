package org.befun.adminx.auditor;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.AuditScriptLanguage;
import org.befun.adminx.dto.audit.AuditContext;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.entity.AuditDto;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStreamWriter;

/**
 * 基于脚本的审核模型
 *
 * <AUTHOR>
 */
@Component("SCRIPT")
@Slf4j
public class ScriptAuditor implements SurveyAuditor {
    /**
     * polyglot context (TBD, limit access later)
     */
    private ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private Context polyglot = Context.newBuilder().out(outputStream).allowAllAccess(true).build();

    @Override
    public AuditResultDto run(AuditDto audit, AuditContext context) {
        AuditScriptLanguage language = audit.getLanguage();
        Value binding = polyglot.getBindings(language.getLangId());
        context.getContext().entrySet().forEach(entry -> {
            binding.putMember(entry.getKey(), entry.getValue());
        });
        Value result = polyglot.eval(language.getLangId(), audit.getCode());
        AuditResultDto resultDto = new AuditResultDto().toBuilder()
                .pass(result.asBoolean())
                .log(outputStream.toString())
                .build();
        outputStream.reset();
        return resultDto;
    }

}
