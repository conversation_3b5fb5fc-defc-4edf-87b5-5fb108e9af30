package org.befun.adminx.auditor;

import cn.hanyi.expression.expression.ExpressionEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.befun.adminx.constant.AuditRuleType;
import org.befun.adminx.dto.audit.AuditContext;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.dto.audit.AuditResultItemDto;
import org.befun.adminx.entity.AuditDto;
import org.befun.adminx.entity.dto.AuditRule;
import org.befun.core.exception.BadRequestException;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于评分卡的审核模型
 * - Hard
 * - Soft
 *
 * <AUTHOR>
 */
@Component("SCORE_RULE")
@Slf4j
public class ScoreRuleAuditor implements SurveyAuditor {
    @Override
    public AuditResultDto run(AuditDto audit, AuditContext context) {
        AuditResultDto.AuditResultDtoBuilder resultBuilder = new AuditResultDto().toBuilder();

        List<AuditResultItemDto> items = new ArrayList<>();
        List<AuditRule> rules = audit.getRules();
        Integer score = 0;

        List<AuditRule> hardRules = rules.stream()
                .filter(r -> r.getRuleType() == AuditRuleType.HARD_CHECK)
                .collect(Collectors.toList());
        List<AuditRule> softRules = rules.stream()
                .filter(r -> r.getRuleType() == AuditRuleType.SOFT_CHECK)
                .collect(Collectors.toList());

        Boolean shouldContinue = true;
        // go hard check firstly
        for (AuditRule rule : hardRules) {
            Boolean result = evaluateRule(context, rule);
            items.add(AuditResultItemDto.builder()
                    .ruleName(rule.getName())
                    .pass(result)
                    .build());
            if (!result) {
                shouldContinue = false;
                resultBuilder.failRuleName(rule.getName());
                break;
            }
        }

        // go soft check to calculate score
        if (shouldContinue) {
            for (AuditRule rule : softRules) {
                Boolean result = evaluateRule(context, rule);
                items.add(AuditResultItemDto.builder()
                        .score(result ? rule.getScore() : 0)
                        .ruleName(rule.getName())
                        .pass(result)
                        .build());
                if (result) {
                    score += rule.getScore();
                }
            }
        }

        resultBuilder.pass(shouldContinue && score >= audit.getThreshold());
        resultBuilder.items(items);
        resultBuilder.score(score);
        return resultBuilder.build();
    }

    /**
     *
     * @param context
     * @param rule
     * @return
     */
    private Boolean evaluateRule(AuditContext context, AuditRule rule) {
        String expr = rule.getExpression();
        ExpressionEvaluator evaluator = new ExpressionEvaluator(expr, false);
        try {
            Object result = evaluator.evaluate(context.getContext());
            if (result instanceof Boolean && BooleanUtils.isTrue((Boolean) result)) {
                return true;
            }
        } catch (Exception ex) {
            log.error("failed to audit rule expression {} on response: {} error: {}", rule.getExpression(), context.getResponseId(), ex);
            throw new BadRequestException(String.format("逻辑错误 规则: %s 错误: %s", rule.getName(), ex.getMessage()));
        }
        return false;
    }
}
