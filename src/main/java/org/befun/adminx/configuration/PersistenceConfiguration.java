package org.befun.adminx.configuration;

import org.befun.adminx.dto.sample.SimpleLoginResponseDto;
import org.befun.adminx.security.AdminAuthTokenFilter;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration
@EntityScan({"org.befun.adminx.entity", "org.befun.core.entity", "org.befun.extension.entity", "org.befun.task.entity"})
@EnableJpaRepositories(basePackages = {"org.befun.core.repo", "org.befun.adminx.repository", "org.befun.extension.repository", "org.befun.task.repository"},  repositoryBaseClass = BaseRepositoryImpl.class)
public class PersistenceConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public AdminAuthTokenFilter simpleTokenFilter() {
        return new AdminAuthTokenFilter();
    }

    @Bean
    public RedisTemplate<String, SimpleLoginResponseDto> tokenRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, SimpleLoginResponseDto> template = new RedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
}
