package org.befun.adminx.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.constant.DownLoadType;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.ResetResponseDto;
import org.befun.adminx.dto.SurveyProcessDto;
import org.befun.adminx.entity.DeliveryTask;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.survey.SurveyQuota;
import org.befun.adminx.entity.survey.SurveyQuotaDto;
import org.befun.adminx.repository.SurveyQuotaRepository;
import org.befun.adminx.service.QuotaService;
import org.befun.adminx.service.download.ResponseDownloadHelper;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/1 11:30
 */
@ResourceController(
        entityClass = SurveyQuota.class,
        repositoryClass = SurveyQuotaRepository.class,
        dtoClass = SurveyQuotaDto.class,
        serviceClass = QuotaService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/quota/quotas")
@Tag(name = "配额")
public class QuotasController extends BaseController<QuotaService> {

        @Autowired
        private ResponseDownloadHelper responseDownloadHelper;

        @PostMapping("/resetQuotaFullResponse")
        @Operation(summary = "重置配额已满的答卷")
        public ResourceResponseDto<Boolean> resetQuotaFullResponse(@RequestBody @Validated ResetResponseDto resetResponseDto) {
                return new ResourceResponseDto(service.resetQuotaFullResponse(resetResponseDto));
        }


}
