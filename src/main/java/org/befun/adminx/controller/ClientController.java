package org.befun.adminx.controller;

import org.befun.adminx.entity.survey.SurveyTrackingDataDto;
import org.befun.adminx.service.TrackingService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/8 11:44
 */
@RestController
@RequestMapping("/client")
public class ClientController {

    @Autowired
    private TrackingService trackingService;

    @GetMapping("/ip")
    public ResourceResponseDto<SurveyTrackingDataDto> getLocationByIp(@RequestParam String ip) {
        return new ResourceResponseDto<>(trackingService.parseRequest(ip));
    }

}
