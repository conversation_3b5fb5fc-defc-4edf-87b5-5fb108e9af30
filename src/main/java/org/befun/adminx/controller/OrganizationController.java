package org.befun.adminx.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.user.RegisterInfoDto;
import org.befun.adminx.dto.user.SendTemplateDto;
import org.befun.adminx.dto.user.UserInfoDto;
import org.befun.adminx.entity.JourneyMapDto;
import org.befun.adminx.entity.OrganizationDto;
import org.befun.adminx.entity.XmPlusUser;
import org.befun.adminx.entity.XmPlusUserDto;
import org.befun.adminx.entity.survey.SurveyDto;
import org.befun.adminx.repository.UserRepository;
import org.befun.adminx.service.OrganizationService;
import org.befun.adminx.service.UserService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Optional;


/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 19:26
 */
@ResourceController(
        entityClass = XmPlusUser.class,
        repositoryClass = UserRepository.class,
        dtoClass = XmPlusUserDto.class,
        serviceClass = UserService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/mgnt/organizations")
public class OrganizationController extends BaseController<OrganizationService> {
}
