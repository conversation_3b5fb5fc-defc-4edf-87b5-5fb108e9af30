package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.dto.audit.AuditEvaluateRequestDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.entity.AuditRecord;
import org.befun.adminx.entity.AuditRecordDto;
import org.befun.adminx.repository.AuditRecordRepository;
import org.befun.adminx.service.AuditRecordService;
import org.befun.adminx.service.AuditService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = AuditRecord.class,
        repositoryClass = AuditRecordRepository.class,
        dtoClass = AuditRecordDto.class,
        serviceClass = AuditRecordService.class,
        excludeActions = {
                ResourceMethod.COUNT
        }
)
@RestController
@RequestMapping("/community/audit-record")
@Tag(name = "审核记录")
public class AuditRecordController extends BaseController<AuditService> {

        @Autowired
        private AuditRecordService auditRecordService;

        @GetMapping("/pass")
        @Operation(summary = "审核通过名单", description = "审核通过名单")
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public ResourceListResponseDto getAllPassAudit(Long sid){
                return new ResourceListResponseDto(auditRecordService.getAllPassAudit(sid));
        }
}
