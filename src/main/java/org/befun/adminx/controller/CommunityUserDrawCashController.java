package org.befun.adminx.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.dto.community.DrawCashDto;
import org.befun.adminx.dto.community.DrawCashResult;
import org.befun.adminx.exception.WechatCallBackException;
import org.befun.adminx.service.CommunityUserDrawCashService;
import org.befun.adminx.service.wechat.WechatPayService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.transfer.TransferBillsNotifyResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "社区用户提现")
@RequestMapping("/community/users/draw-cash")
@Slf4j
public class CommunityUserDrawCashController {
    @Autowired
    private CommunityUserDrawCashService drawCashService;

    @Autowired
    private WechatPayService wechatPayService;

    @PostMapping("")
    @Operation(
            summary = "提现",
            description = "现金提现"
    )
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<DrawCashResult> drawCash(HttpServletRequest request, @RequestBody DrawCashDto dto) {
        return new BaseResponseDto<>(drawCashService.drawCashNew(request,dto));
    }


    @PostMapping("/cancel")
    @Operation(
            summary = "提现",
            description = "撤销提现"
    )
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<DrawCashResult> drawCashCancel(@RequestBody DrawCashDto dto) {
        return new BaseResponseDto<>(drawCashService.drawCashCancel(dto));
    }

    @PostMapping("/callback")
    @Operation(
            summary = "提现",
            description = "提现回调"
    )
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<Boolean> drawCashCallBack(HttpServletRequest request) {
        try {
            TransferBillsNotifyResult result = wechatPayService.transferBillCallBack(request);
            log.info("-------用户提现微信回调通知------：{}", JsonHelper.toJson(result));
            drawCashService.handleCallbackResult(result);
        } catch (BadRequestException exception) {
            log.error("微信提现回调内部处理失败，不返回给微信端");
        }
        return new BaseResponseDto<>(true);
    }

}
