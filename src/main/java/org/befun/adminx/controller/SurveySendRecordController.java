package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.SurveySendRecord;
import org.befun.adminx.entity.SurveySendRecordDto;
import org.befun.adminx.repository.SurveySendRecordRepository;
import org.befun.adminx.service.SurveySendRecordService;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;



/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = SurveySendRecord.class,
        repositoryClass = SurveySendRecordRepository.class,
        dtoClass = SurveySendRecordDto.class,
        serviceClass = SurveySendRecordService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/survey-send-record")
@PreAuthorize("isAuthenticated()")
@Tag(name = "问卷发送记录")
public class SurveySendRecordController extends BaseController<SurveySendRecordService> {
}






















