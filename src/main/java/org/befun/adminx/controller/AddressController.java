package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.Address;
import org.befun.adminx.entity.AddressDto;
import org.befun.adminx.repository.AddressRepository;
import org.befun.adminx.service.AddressService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/14 16:18
 */
@ResourceController(
        entityClass = Address.class,
        repositoryClass = AddressRepository.class,
        dtoClass = AddressDto.class,
        serviceClass = AddressService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        docTag = "管理后台-省市区",
        docCrud = "省市区",
        permission = "isAuthenticated()"
)

@Tag(name = "省市区")
@RestController
@RequestMapping("/community/address")
public class AddressController extends BaseController<AddressService> {
}
