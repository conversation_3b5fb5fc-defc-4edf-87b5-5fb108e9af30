package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.survey.SurveyResponseFetchRequestDto;
import org.befun.adminx.dto.survey.AbstractSurvey;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyDto;
import org.befun.adminx.repository.SurveyRepository;
import org.befun.adminx.service.HybridSurveyService;
import org.befun.adminx.service.SurveyService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.*;


/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = Survey.class,
        repositoryClass = SurveyRepository.class,
        dtoClass = SurveyDto.class,
        serviceClass = SurveyService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/community/surveys")
//@PreAuthorize("isAuthenticated()")
@Tag(name = "问卷")
public class SurveyController extends BaseController<SurveyService> {

        @Autowired
        private HybridSurveyService hybridSurveyService;

        @SneakyThrows
        @ResourceInstanceAction(
                action = "fetchResponse",
                path = "fetch-response",
                method = RequestMethod.POST
        )
        public ResourceResponseDto<Map> fetchResponse(SurveyDto survey, SurveyResponseFetchRequestDto fetchRequestDto) {
                Map<String, Object> context = service.getResponseContext(survey, fetchRequestDto);
                return new ResourceResponseDto<>(context);
        }

        @GetMapping("/hybrid/{type}/{id}")
        public BaseResponseDto<AbstractSurvey> getHybridSurvey(@PathVariable SurveyType type, @PathVariable String id) {
                AbstractSurvey survey = hybridSurveyService.getSurvey(type, id);
                return new BaseResponseDto<>(survey);
        }

        @GetMapping("/get-cuid-by-survey/{id}")
        public ResourceResponseDto<SurveySimpleDto> getCuidBySurveyId(@PathVariable Long id) {
                return new ResourceResponseDto<>(service.getSimpleSurveyBySurveyId(id));
        }
}
