package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import org.befun.adminx.entity.Resource;
import org.befun.adminx.entity.ResourceDto;
import org.befun.adminx.repository.ResourceRepository;
import org.befun.adminx.service.ResourceService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * <AUTHOR>
 */
@org.befun.core.rest.annotation.ResourceController(
        entityClass = Resource.class,
        repositoryClass = ResourceRepository.class,
        dtoClass = ResourceDto.class,
        serviceClass = ResourceService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/resources")
@PreAuthorize("isAuthenticated()")
public class ResourceController extends BaseController<ResourceService> {
}
