package org.befun.adminx.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.constant.AppVersion;
import org.befun.adminx.constant.XmVersions;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.user.XmVersionUpdateDto;
import org.befun.adminx.entity.XmPermission;
import org.befun.adminx.entity.XmPermissionDto;
import org.befun.adminx.repository.XmPermissionRepository;
import org.befun.adminx.service.UserService;
import org.befun.adminx.service.XmPermissionService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@ResourceController(
        entityClass = XmPermission.class,
        repositoryClass = XmPermissionRepository.class,
        dtoClass = XmPermissionDto.class,
        serviceClass = XmPermissionService.class,
        excludeActions = {
                ResourceMethod.FIND_ALL,
                ResourceMethod.FIND_ONE,
                ResourceMethod.CREATE,
                ResourceMethod.UPDATE_ONE,
                ResourceMethod.BATCH_UPDATE,
                ResourceMethod.DELETE_ONE,
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/xm-versions")
@Tag(name = "体验家版本")
public class XmVersionController extends BaseController<XmPermissionService> {

    @Autowired
    private UserService userService;

    @GetMapping("")
    @Operation(
            summary = "获取APP版本",
            description = "获取体验家当前所有版本"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<List> getCemVersions() {
        return new ResourceResponseDto(service.getCemVersions());
    }

    @PostMapping("/update-version")
    @Operation(
            summary = "更改APP版本",
            description = "更改调研家当前版本"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<AppVersion> updateCemVersions(@RequestBody XmVersionUpdateDto dto) {
        userService.updateCemPermission(dto);
        return new ResourceResponseDto();
    }

    @PostMapping("/batch-update")
    @Operation(
            summary = "批量处理脚本",
            description = "批量处理老调研家账号权限，迁移到新调研家"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<AuditResponseDto> batchUpdateSurveyPlusVersion() {
        userService.batchUpdateSurveyPlusVersion();
        return new ResourceResponseDto();
    }
}
