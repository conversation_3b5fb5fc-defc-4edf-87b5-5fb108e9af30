package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.befun.adminx.dto.SurveyProcessDto;
import org.befun.adminx.entity.survey.AdminxQuota;
import org.befun.adminx.entity.survey.AdminxQuotaDto;
import org.befun.adminx.entity.survey.SurveyQuota;
import org.befun.adminx.entity.survey.SurveyQuotaDto;
import org.befun.adminx.repository.AdminxQuotaRepository;
import org.befun.adminx.repository.SurveyQuotaRepository;
import org.befun.adminx.service.AdminxQuotaService;
import org.befun.adminx.service.QuotaService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.*;

@ResourceController(
        entityClass = AdminxQuota.class,
        repositoryClass = AdminxQuotaRepository.class,
        dtoClass = AdminxQuotaDto.class,
        serviceClass = AdminxQuotaService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/adminxQuota/quotas")
public class AdminxQuotasController extends BaseController<AdminxQuotaService> {


        @PutMapping("/{id}/publish")
        @Operation(summary = "启用问卷")
        public ResourceResponseDto<AdminxQuotaDto> publish(@PathVariable long id) {
                return new ResourceResponseDto<>(service.publish(id));
        }

        @PutMapping("/{id}/stop")
        @Operation(summary = "暂停问卷")
        public ResourceResponseDto<Boolean> stop(@PathVariable long id) {
                return new ResourceResponseDto<>(service.stop(id));
        }

        @PostMapping("/{id}/sync")
        @Operation(summary = "配额计算")
        public ResourceResponseDto<Boolean> quotaSync(@PathVariable long id) {
                return new ResourceResponseDto<>(service.quotaSync(id));
        }

}
