package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import org.befun.adminx.constant.DownLoadType;
import org.befun.adminx.constant.SurveyType;
import org.befun.adminx.dto.audit.AuditRequestItemDto;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.community.CheckTaskDto;
import org.befun.adminx.dto.ext.ResendDto;
import org.befun.adminx.dto.ext.SendFollowTaskDto;
import org.befun.adminx.dto.ext.SendTaskExDto;
import org.befun.adminx.dto.ext.TemplateSendDto;
import org.befun.adminx.dto.task.TaskFenceDto;
import org.befun.adminx.entity.DeliveryTask;
import org.befun.adminx.entity.DeliveryTaskDto;
import org.befun.adminx.entity.SendRecord;
import org.befun.adminx.entity.SurveySendRecordDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyQuotaDto;
import org.befun.adminx.repository.DeliveryTaskRepository;
import org.befun.adminx.service.*;
import org.befun.adminx.service.download.ResponseDownloadHelper;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = DeliveryTask.class,
        repositoryClass = DeliveryTaskRepository.class,
        dtoClass = DeliveryTaskDto.class,
        serviceClass = DeliveryService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/community/tasks")
@PreAuthorize("isAuthenticated()")
@Tag(name = "任务")
public class DeliveryTaskController extends BaseController<DeliveryService> {

        @Autowired
        private FileService fileService;

        @Autowired
        private QuotaService quotaService;

        @Autowired
        private SendRecordService sendRecordService;

        @Autowired
        private ResponseDownloadHelper responseDownloadHelper;

        @Autowired
        private SurveySendRecordService surveySendRecordService;

        @SneakyThrows
        @ResourceInstanceAction(
                action = "auditPass",
                path = "audit-pass",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> auditPass(DeliveryTask task, @RequestBody AuditRequestItemDto dto) {
                String[] responseIds = fileService.parseContent(dto);
                AuditResponseDto response;
                DeliveryTaskDto taskDto = service.mapToDto(task);
                if(dto.getScoreChange() != null && dto.getScoreChange() >0) {
                        taskDto.setScore(dto.getScoreChange());
                }
                if(dto.getAutoAudit()) {
                        response = service.auditPassByAutoAudit(taskDto, responseIds);
                }else {
                        response = service.auditPassByContent(taskDto, responseIds);
                }
                return new BaseResponseDto<>(response);
        }

        @SneakyThrows
        @ResourceInstanceAction(
                action = "auditFail",
                path = "audit-fail",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> auditFail(DeliveryTask task, @RequestBody AuditRequestItemDto dto) {
                String[] responseIds = fileService.parseContent(dto);
                AuditResponseDto response = service.auditFailByContent(service.mapToDto(task), responseIds);
                return new BaseResponseDto<>(response);
        }

        @ResourceInstanceAction(
                action = "send",
                path = "send",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> send(DeliveryTask task, @RequestBody SendTaskExDto taskChange) {
                return new BaseResponseDto<>(service.sendDeliveryTask(task,taskChange));
        }

        @GetMapping("/{id}/invite/{cuid}")
        public BaseResponseDto<String> buildInviteShareLink(
                @PathVariable(value = "id") Long taskId,
                @PathVariable(value = "cuid") Long cuid
        ) {
                return new BaseResponseDto<>(service.buildInviteShareLink(taskId, cuid));
        }

        @ResourceInstanceAction(
                action = "sendExpected",
                path = "send-expected",
                method = RequestMethod.POST
        )
        public BaseResponseDto<Integer> sendExpected(DeliveryTask task, @RequestBody SendTaskExDto taskChange) {
                return new BaseResponseDto<>(service.sendTaskExpected(task,taskChange));
        }

        @PostMapping("/{id}/templateSend")
        @Operation(summary = "发送模板消息")
        public BaseResponseDto<Boolean> templateSend(@NotNull @PathVariable Long id, @RequestBody TemplateSendDto taskChange) {
                return new BaseResponseDto<>(service.templateSend(id,taskChange));
        }

        @GetMapping("/{taskId}/template-send-record")
        @Operation(summary = "模板消息发送记录")
        public ResourceListResponseDto<SendRecord> templateSendRecord(@PathVariable Long taskId){
                List<SendRecord> sendRecord = sendRecordService.templateSendRecord(taskId);
                return new ResourceListResponseDto<>(sendRecord);
        }

        @ResourceInstanceAction(
                action = "resend",
                path = "resend",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> resend(DeliveryTask task, @RequestBody ResendDto resendDto) {
                return new BaseResponseDto<>(service.resendDeliveryTask(task,resendDto));
        }

        @ResourceInstanceAction(
                action = "sendFollowTask",
                path = "send-follow-task",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> followSend(DeliveryTask task, @RequestBody SendFollowTaskDto taskChange) {
                return new BaseResponseDto<>(service.sendFollowTask(task,taskChange));
        }

        @ResourceInstanceAction(
                action = "pause",
                path = "pause",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> pause(DeliveryTask task) {
                return new BaseResponseDto<>(service.pauseDeliveryTask(task));
        }

        @ResourceInstanceAction(
                action = "restart",
                path = "restart",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> restart(DeliveryTask task) {
                return new BaseResponseDto<>(service.restartDeliveryTask(task));
        }

        @ResourceInstanceAction(
                action = "stop",
                path = "stop",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> stop(DeliveryTask task) {
                return new BaseResponseDto<>(service.stopDeliveryTask(task));
        }

        @ResourceInstanceAction(
                action = "toggleAudit",
                path = "toggle-audit",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> toggleAudit(DeliveryTask task) {
                service.toggleAudit(task);
                return new BaseResponseDto<>();
        }

        @ResourceInstanceAction(
                action = "toggleNotify",
                path = "toggle-notify",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> toggleNotify(DeliveryTask task) {
                service.toggleNotify(task);
                return new BaseResponseDto<>();
        }

        @PostMapping("get-survey-list")
        @Operation(
                summary = "getSurveyList"
        )
        @JsonView(ResourceViews.Basic.class)
        public BaseResponseDto<DeliveryTaskDto> getSurveyList(
                @ResourceQueryPredicate ResourceEntityQueryDto<DeliveryTaskDto> params, @RequestBody Map<String,Object> body) {
                return new BaseResponseDto(service.getSurveyList(params,body));
        }

        @GetMapping("/{taskId}/download")
        @JsonView(ResourceViews.Detail.class)
        @Operation(summary = "下载答题数据")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<Survey> download(@PathVariable Long taskId, HttpServletResponse response) throws IOException {
                DeliveryTask task = service.require(taskId);
//                if(task.getType() == SurveyType.XM_PLUS) downloadService.download(response, Long.parseLong(task.getSid()), DownLoadType.SURVEY_PLUS);
                if(task.getType() == SurveyType.XM_PLUS) responseDownloadHelper.downloadToResponse(response, Long.parseLong(task.getSid()), DownLoadType.SURVEY_PLUS, task.getTaskType(), taskId);
                return null;
        }

        @GetMapping("/{taskId}/download-all")
        @JsonView(ResourceViews.Detail.class)
        @Operation(summary = "下载全量答题数据")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<Survey> downloadAll(@PathVariable Long taskId, HttpServletResponse response) throws IOException {
                DeliveryTask task = service.require(taskId);
//                if(task.getType() == SurveyType.XM_PLUS) downloadService.download(response, Long.parseLong(task.getSid()), DownLoadType.ALL);
                if(task.getType() == SurveyType.XM_PLUS) responseDownloadHelper.downloadToResponse(response, Long.parseLong(task.getSid()), DownLoadType.ALL, task.getTaskType(), taskId);
                return null;
        }

        @GetMapping("/{taskId}/download-quota-full")
        @JsonView(ResourceViews.Detail.class)
        @Operation(summary = "下载配额已满答题数据")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<Survey> downloadQuotaFull(@PathVariable Long taskId, HttpServletResponse response) throws IOException {
                DeliveryTask task = service.require(taskId);
                if(task.getType() == SurveyType.XM_PLUS) responseDownloadHelper.downloadToResponse(response, Long.parseLong(task.getSid()), DownLoadType.QUOTA_FULL, task.getTaskType(), taskId);
                return null;
        }


        @GetMapping("/{taskId}/quotas")
        @JsonView(ResourceViews.Detail.class)
        @Operation(summary = "配额")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<SurveyQuotaDto> quotas(@PathVariable Long taskId, HttpServletResponse response) throws IOException {
                DeliveryTask task = service.require(taskId);
                return new ResourceResponseDto(quotaService.getQuotasBySid(Long.parseLong(task.getSid())));
        }


        @PutMapping(path = "/{taskId}/on-off-quota")
        @Operation(summary = "开启关闭配额")
        public ResourceResponseDto<Boolean> onOffQuota(@PathVariable Long taskId) {
                service.onOffQuota(taskId);
                return new ResourceResponseDto<>(true);
        }


//        @PutMapping(path = "/{taskId}/close-quota")
//        @Operation(summary = "开启关闭配额")
//        public ResourceResponseDto<Boolean> closeQuota(@PathVariable Long taskId) {
//                service.closeQuota(taskId);
//                return new ResourceResponseDto<>(true);
//        }


        @GetMapping("/{taskId}/statics")
        @JsonView(ResourceViews.Basic.class)
        @Operation(summary = "回收状况")
        @PreAuthorize("isAuthenticated()")
        public ResourcePageResponseDto<SurveySendRecordDto> statics(@PathVariable Long taskId, @ResourceQueryPredicate ResourceEntityQueryDto<SurveySendRecordDto> queryDto) throws IOException {
                return new ResourcePageResponseDto(surveySendRecordService.getListByTaskId(taskId,queryDto));
        }

        @GetMapping("/get-supervisor-task")
        @JsonView(ResourceViews.Basic.class)
        @Operation(summary = "网格督导任务列表")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<DeliveryTaskDto> getSupervisorTask(String gridId) {
                return new ResourceResponseDto(service.getSupervisorTask(gridId));
        }


        @PostMapping("/{taskId}/open-supervisor")
        @JsonView(ResourceViews.Basic.class)
        @Operation(summary = "开启督导")
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<DeliveryTaskDto> openSupervisor(@PathVariable Long taskId) {
                return new ResourceResponseDto(service.openSupervisor(taskId));
        }

        @PostMapping("/check-conditions")
        @Operation(
                summary = "检查cuid是否符合条件",
                description = "检查cuid是否符合条件"
        )
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<Boolean> checkConditions(HttpServletRequest request, @RequestBody CheckTaskDto dto) {
                return new ResourceResponseDto<>(service.checkConditions(request, dto));
        }

        @GetMapping("/{taskId}/fence")
        @Operation(
                summary = "获取电子围栏",
                description = "获取电子围栏"
        )
        @PreAuthorize("isAuthenticated()")
        public ResourceResponseDto<TaskFenceDto> getElectronicFence(@PathVariable Long taskId) {
                return new ResourceResponseDto<>(service.getElectronicFence(taskId));
        }
}













