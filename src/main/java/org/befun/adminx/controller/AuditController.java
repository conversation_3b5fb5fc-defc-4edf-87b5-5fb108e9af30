package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.dto.audit.AuditEvaluateRequestDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.entity.Audit;
import org.befun.adminx.entity.AuditDto;
import org.befun.adminx.repository.AuditRepository;
import org.befun.adminx.service.AuditService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = Audit.class,
        repositoryClass = AuditRepository.class,
        dtoClass = AuditDto.class,
        serviceClass = AuditService.class,
        excludeActions = {
                ResourceMethod.COUNT
        }
)
@RestController
@RequestMapping("/community/audits")
@Tag(name = "审核配置")
public class AuditController extends BaseController<AuditService> {
        @ResourceCollectionAction(
                action = "evaluate",
                path = "evaluate",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResultDto> evaluate(AuditEvaluateRequestDto evaluateRequestDto) {
                AuditResultDto resultDto = service.evaluate(evaluateRequestDto);
                return new BaseResponseDto<>(resultDto);
        }
}
