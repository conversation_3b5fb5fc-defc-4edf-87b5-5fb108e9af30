package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.util.crypto.WxCpCryptUtil;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftList;
import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.wechat.WechatCpTagUserDto;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.adminx.service.wechat.WechatCpConfigService;
import org.befun.adminx.service.wechat.WechatCpHandler;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.WeChatCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/wechat-cp")
@Tag(name = "企微")
public class WechatCpController {

    @Autowired(required = false)
    private WeChatCpService weChatCpService;
    @Autowired(required = false)
    private WechatCpHandler wechatCpHandler;
    @Autowired(required = false)
    private WechatCpConfigService wechatCpConfigService;

    @GetMapping(value = "/{agentId}", produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable(required = false) Integer agentId,
                          @RequestParam(name = "msg_signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("\n接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        final WxCpService wxCpService = weChatCpService;
        if (wxCpService == null) {
            throw new IllegalArgumentException(String.format("未找到对应agentId=[%d]的配置，请核实！", agentId));
        }

        if (wxCpService.checkSignature(signature, timestamp, nonce, echostr)) {
            return new WxCpCryptUtil(wxCpService.getWxCpConfigStorage()).decrypt(echostr);
        }

        return "非法请求";
    }

    @PostMapping(value = "/{agentId}",produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable(required = false) Integer agentId,
                       @RequestBody String requestBody,
                       @RequestParam("msg_signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce) {
        log.info("\n接收微信请求：[signature=[{}], timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                signature, timestamp, nonce, requestBody);

        final WxCpService wxCpService = weChatCpService;
        WxCpXmlMessage inMessage = WxCpXmlMessage.fromEncryptedXml(requestBody, wxCpService.getWxCpConfigStorage(),
                timestamp, nonce, signature);
        log.info("\n消息解密后内容为：\n{} ", JsonHelper.toJson(inMessage));

        if(("change_external_contact".equals(inMessage.getEvent()) && "add_external_contact".equals(inMessage.getChangeType()))){
            wechatCpHandler.addWeChatUserHandler(inMessage);
        }
        WxCpXmlOutMessage outMessage = WxCpXmlOutMessage.TEXT()
                .fromUser(inMessage.getToUserName()).toUser(inMessage.getFromUserName())
                .build();

        String out = outMessage.toEncryptedXml(wxCpService.getWxCpConfigStorage());
        log.info("\n组装回复信息：{}", out);
        return out;
    }

    @GetMapping(value = "/{agentId}/03f3f90a-c0a7-4c00-97fc-64289f2f909d/syncWechatCpUserUnionId")
    public void syncWechatCpUserUnionId(){
        wechatCpConfigService.syncWechatCpUserUnionId();
    }

    @GetMapping(value = "/{agentId}/03f3f90a-c0a7-4c00-97fc-64289f2f909d/syncCommunityUserUnionId")
    public void syncCommunityUserUnionId(){
        wechatCpConfigService.syncCommunityUserUnionId(null);
    }

    @PostMapping(value = "/{agentId}/03f3f90a-c0a7-4c00-97fc-64289f2f909d/tagUser")
    public void tagUser(@RequestBody WechatCpTagUserDto userDto){
        wechatCpHandler.tagCpUser(userDto.getUserId(), userDto.getExternalUserId(), userDto.getOpenId());
    }

    @GetMapping(value = "/test")
    public void get() {
        wechatCpHandler.addWeChatUserHandler(new WxCpXmlMessage());
    }


}
