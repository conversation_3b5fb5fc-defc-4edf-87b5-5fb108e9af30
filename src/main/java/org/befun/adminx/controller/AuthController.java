package org.befun.adminx.controller;

import cn.hutool.core.util.HexUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.dto.sample.SimpleLoginRequestDto;
import org.befun.adminx.dto.sample.SimpleLoginResponseDto;
import org.befun.adminx.dto.user.SimpleUserInfoDto;
import org.befun.adminx.entity.Admin;
import org.befun.adminx.entity.AdminDto;
import org.befun.adminx.repository.AdminRepository;
import org.befun.adminx.service.AdminService;
import org.befun.adminx.service.SimpleAuthService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.UserDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.PermissionException;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourcePermission;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = Admin.class,
        repositoryClass = AdminRepository.class,
        dtoClass = AdminDto.class,
        serviceClass = AdminService.class,
        excludeActions = {
                ResourceMethod.COUNT,
                ResourceMethod.BATCH_UPDATE
        },
        permissions = {
                @ResourcePermission(action = "findAll", permission = "isAuthenticated()"),
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
        }
)
@RestController
@RequestMapping("/mgnt/users")
@Tag(name = "登录注册")
public class AuthController extends BaseController<AdminService> {
    @Autowired
    private SimpleAuthService authService;

    @PostMapping("login")
    public ResourceResponseDto<SimpleLoginResponseDto> login(@RequestBody SimpleLoginRequestDto requestDto) {
        if (!StringUtils.hasText(requestDto.getUsername()) || !StringUtils.hasText(requestDto.getPassword())) {
            throw new BadRequestException("账号或密码不能为空");
        }
        SimpleLoginResponseDto response = authService.login(requestDto);
        return new ResourceResponseDto(response);
    }

    @GetMapping("info")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SimpleUserInfoDto> userInfo() {
        // TBD
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        Object principal = authentication.getPrincipal();

        if (principal instanceof UserPrincipal) {
            UserDto user = ((UserPrincipal) principal).getUser();
            AdminDto admin = this.service.findOne(user.getId());
            return new ResourceResponseDto(SimpleUserInfoDto.builder()
                    .userId(user.getId())
                    .username(user.getUsername())
                    .roles(admin.getRoles())
                    .build());
        }
        throw new PermissionException();
    }

    @PostMapping("logout")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<String> logout() {
        return new ResourceResponseDto<>();
    }

    @PostMapping("{id}/unset-password")
    public ResourceResponseDto<SimpleLoginResponseDto> unsetPassWord(@NotNull @PathVariable Long id, @RequestBody AdminDto requestDto) throws NoSuchAlgorithmException {
        if (!StringUtils.hasText(requestDto.getUserName()) || !StringUtils.hasText(requestDto.getPassword())) {
            throw new BadRequestException("用户密码错误");
        }
        MessageDigest md = MessageDigest.getInstance("MD5");
        requestDto.setPassword(HexUtil.encodeHexStr(md.digest(requestDto.getPassword().getBytes())));
        super.service.updateOne(id, requestDto);
        return new ResourceResponseDto();
    }
}
