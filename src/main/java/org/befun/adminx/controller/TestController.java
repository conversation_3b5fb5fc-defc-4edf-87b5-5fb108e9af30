package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.dto.sms.MessageSendResponseInfo;
import org.befun.adminx.property.sms.SmsTemplateProperty;
import org.befun.adminx.service.BackupService;
import org.befun.adminx.task.sms.SmsService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/test")
@Tag(name = "测试")
public class TestController {

    @Autowired
    private SmsService smsService;

    private static final String QUOTA_TOKEN = "quota:token:%s:%s";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @RequestMapping("/send-msg")
    public MessageSendResponseInfo test1(){
        return smsService.sendMessageByText2("18620322114","conten",null);
    }

    @GetMapping("/template")
    public ResourceResponseDto<String> getTemplate() {
        SmsTemplateProperty template = smsService.getTemplate();
        return new ResourceResponseDto();
    }

    @RequestMapping("/{surveyId}/quota-data/{quotaId}")
    public ResourceResponseDto<List<Long>> getQuotaData(@PathVariable long surveyId, @PathVariable long quotaId) {
        return new ResourceResponseDto(stringRedisTemplate.opsForSet().members(getQuotaTokenKey(surveyId,quotaId)));
    }

    @RequestMapping("/{surveyId}/quota-size/{quotaId}")
    public ResourceResponseDto<List<Long>> getQuotaSize(@PathVariable long surveyId, @PathVariable long quotaId) {
        return new ResourceResponseDto(stringRedisTemplate.opsForSet().size(getQuotaTokenKey(surveyId,quotaId)));
    }

    @RequestMapping("/{surveyId}/quota-member/{quotaId}/{responseId}")
    public ResourceResponseDto<List<Long>> isQuotaMember(@PathVariable long surveyId, @PathVariable long quotaId, @PathVariable String responseId) {
        return new ResourceResponseDto(stringRedisTemplate.opsForSet().isMember(getQuotaTokenKey(surveyId,quotaId),responseId));
    }

    protected String getQuotaTokenKey(Long surveyId, Long quotaId) {
        return String.format(QUOTA_TOKEN, surveyId.toString(), quotaId.toString());
    }

}
