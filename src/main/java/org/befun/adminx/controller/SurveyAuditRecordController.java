package org.befun.adminx.controller;

import org.befun.adminx.dto.surveyaudit.SurveyAuditDto;
import org.befun.adminx.dto.surveyaudit.SurveyAuditRecordQueryDto;
import org.befun.adminx.dto.surveyaudit.SurveyReportDto;
import org.befun.adminx.entity.SurveyAuditRecord;
import org.befun.adminx.repository.SurveyAuditRecordRepository;
import org.befun.adminx.service.SurveyAuditRecordService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.*;


@ResourceController(
        entityClass = SurveyAuditRecord.class,
        repositoryClass = SurveyAuditRecordRepository.class,
        serviceClass = SurveyAuditRecordService.class,
        excludeActions = { ResourceMethod.CREATE, ResourceMethod.COUNT, ResourceMethod.UPDATE_ONE, ResourceMethod.BATCH_UPDATE},
        methodDtoClass = {@ResourceMethodDto(method = ResourceMethod.FIND_ALL, dtoClass = SurveyAuditRecordQueryDto.class)},
        permission = "isAuthenticated()"
)

@RestController
@RequestMapping("/community/surveyAudit")
public class SurveyAuditRecordController extends BaseController<SurveyAuditRecordService> {

    @PostMapping("/surveyReport")
    public BaseResponseDto<Boolean> surveyReport(@RequestBody SurveyReportDto dto) {
        return new BaseResponseDto<>(service.addBySurveyReport(dto));
    }

    @PostMapping("/{id}/edit")
    public BaseResponseDto<Boolean> audit(@PathVariable long id, @RequestBody SurveyAuditDto dto) {
        return new BaseResponseDto<>(service.audit(id, dto));
    }

}
