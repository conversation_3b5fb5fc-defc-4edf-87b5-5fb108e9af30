package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.ThirdPartyTemplate;
import org.befun.adminx.entity.ThirdPartyTemplateDto;
import org.befun.adminx.repository.ThirdPartyTemplateRepository;
import org.befun.adminx.service.ThirdPartyTemplateService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/5/28 14:50
 */
@ResourceController(
        entityClass = ThirdPartyTemplate.class,
        repositoryClass = ThirdPartyTemplateRepository.class,
        dtoClass = ThirdPartyTemplateDto.class,
        serviceClass = ThirdPartyTemplateService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/template/thirdparty-templates")
@PreAuthorize("isAuthenticated()")
@Tag(name = "第三方模板")
public class ThirdPartyTemplateController extends BaseController<ThirdPartyTemplateService> {
}
