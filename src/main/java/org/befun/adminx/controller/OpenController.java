package org.befun.adminx.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.constant.survey.SurveyCollectorMethod;
import org.befun.adminx.dto.event.SurveyChannelOperationDto;
import org.befun.adminx.dto.event.SurveyResponseViewDto;
import org.befun.adminx.dto.survey.SurveyPlusResponseMessageDto;
import org.befun.adminx.dto.survey.SurveyResponseMessageDto;
import org.befun.adminx.dto.sync.SyncCloudUserMessageDto;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyResponse;
import org.befun.adminx.entity.survey.SurveyResponseCell;
import org.befun.adminx.repository.SurveyResponseCellRepository;
import org.befun.adminx.service.*;
import org.befun.adminx.service.audit.ConsumerService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/7 10:57
 */
@Tag(name = "开放接口")
@RestController
@RequestMapping("/open")
public class OpenController {

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private CloudUserService userService;

    @Autowired
    private SampleOrderService orderService;

    @Autowired
    private ConsumerService consumerService;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private CustomerStatisticsService customerStatisticsService;

    @PostMapping("/response-submit-final")
    @Operation(
            summary = "消费体验家答卷详情"
    )
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto responseSubmitFinal(@RequestBody SurveyResponseMessageDto body) throws InterruptedException {

        try {
            var response = surveyBaseEntityService.require(SurveyResponse.class, body.getResponseId());
            var survey = surveyBaseEntityService.require(Survey.class, body.getSurveyId());
            List<SurveyResponseCell> cells = surveyResponseCellRepository.findAllBySurveyIdAndResponseId(response.getSurveyId(), response.getId());
            SurveyResponseMessageDto responseMessageDto = new SurveyResponseMessageDto(survey, response, cells);
            consumerService.consumer(responseMessageDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponseDto();
    }

    @PostMapping("/response-view")
    @Operation(
            summary = "打开答卷"
    )
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto responseView(@RequestBody SurveyResponseViewDto body) {
        deliveryService.surveyResponseViewConsumer(body);
        return new BaseResponseDto();
    }

    @PostMapping("/survey-plus-response-consumer")
    @Operation(
            summary = "消费调研家答卷详情 测试接口"
    )
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto surveyPlusResponseConsumer(@RequestBody SurveyPlusResponseMessageDto body) {
        deliveryService.surveyPlusResponseConsumer(body);
        return new BaseResponseDto();
    }

    @PostMapping("/sync-cloud")
    @Operation(
            summary = "消费修改云平台用户 测试接口"
    )
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto syncCloudUser(@RequestBody SyncCloudUserMessageDto body) {
        userService.setCloudUser(body);
        return new BaseResponseDto();
    }

    @PostMapping("/channel-operation")
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto surveyChannelOperation(@RequestBody SurveyChannelOperationDto operationDto) {
        orderService.channelOperation(operationDto);
        return new BaseResponseDto();
    }

    @PostMapping("/sync-customer-data")
    @Operation(
            summary = "企业客户问卷 预警 看榜 数据统计"
    )
    @JsonView(ResourceViews.Basic.class)
    public BaseResponseDto syncCustomerData() {
        customerStatisticsService.syncData();
        return new BaseResponseDto();
    }
}
