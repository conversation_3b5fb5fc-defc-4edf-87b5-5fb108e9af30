package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.community.*;
import org.befun.adminx.dto.notify.VerifyCodeDto;
import org.befun.adminx.dto.query.CommunityUserExportQueryDto;
import org.befun.adminx.dto.query.CommunityUserQueryDto;
import org.befun.adminx.dto.sample.SimpleUserDto;
import org.befun.adminx.dto.wechat.WechatAuthorizeDto;
import org.befun.adminx.dto.wechat.WechatSignatureResponseDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.CommunityUserDto;
import org.befun.adminx.entity.DeliveryTaskDto;
import org.befun.adminx.entity.FeatureDto;
import org.befun.adminx.repository.CommunityUserRepository;
import org.befun.adminx.service.CommunityUserService;
import org.befun.adminx.service.FeatureService;
import org.befun.adminx.service.FileService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.*;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import java.io.ByteArrayInputStream;
import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ALL;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = CommunityUser.class,
        repositoryClass = CommunityUserRepository.class,
        dtoClass = CommunityUserDto.class,
        serviceClass = CommunityUserService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = CommunityUserQueryDto.class),
        },
        docTag = "管理后台-用户",
        docCrud = "用户",
        permission = "isAuthenticated()"
)
@RestController


@Tag(name = "社区用户")
@RequestMapping("/community/users")
public class CommunityUserController extends BaseController<CommunityUserService> {

        @Autowired
        private FileService fileService;

        @Autowired
        private FeatureService featureService;

        @GetMapping("/{id}/get-user-account")
        @Operation(
                summary = "通过cuid获取用户信息",
                description = "通过cuid获取用户信息"
        )
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<CommunityUserDto> getUserAccount(@PathVariable Long id,String gridId) {
                return new BaseResponseDto(service.getUserAccount(id,gridId));
        }

        @PostMapping("/{id}/set-user-info")
        @Operation(
                summary = "修改用户信息",
                description = "修改用户信息"
        )
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<CommunityUserDto> setUserInfo(HttpServletRequest request,@PathVariable Long id, @RequestBody UserAdditionalDto additionalDto) {
                return new BaseResponseDto(service.setUserInfo(request,id, additionalDto));
        }

        @PostMapping("/{id}/update-location")
        @Operation(
                summary = "更新用户gps位置",
                description = "更新用户gps位置"
        )
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<CommunityUserDto> updateLocation(HttpServletRequest request,@PathVariable Long id, @RequestBody UserAdditionalDto additionalDto) {
                return new BaseResponseDto(service.updateLocation(request,id, additionalDto));
        }

        @PostMapping("/get-account-by-code")
        @Operation(
                summary = "通过code获取微信用户信息",
                description = "通过code获取微信用户信息"
        )
        @JsonView(ResourceViews.Detail.class)
        public BaseResponseDto<CommunityUserDto> getUserAccountByCode(@RequestBody WechatAuthorizeDto params) {
                return new BaseResponseDto(service.getUserAccountByCode(params));
        }

        @PostMapping("/wechat-redirect-url")
        @Operation(
                summary = "获取微信授权跳转地址",
                description = "获取微信授权跳转地址"
        )
        @JsonView(ResourceViews.Detail.class)
        public BaseResponseDto<String> getWechatRedirectUrl(@RequestBody WechatAuthorizeDto params) {
                return new BaseResponseDto(service.getWechatRedirectUrl(params));
        }
        @GetMapping("/wechat-share")
        @Operation(
                summary = "获取微信分享参数",
                description = "获取微信分享参数"
        )
        public BaseResponseDto<WechatSignatureResponseDto> getWechatShare(@RequestParam String url) {
                return new BaseResponseDto(service.getWxSignature(url));
        }

        @ResourceInstanceAction(
                action = "block",
                path = "block",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> block(CommunityUser user) {
                return new BaseResponseDto<>(service.block(user));
        }

        @ResourceInstanceAction(
                action = "unblock",
                path = "unblock",
                method = RequestMethod.POST
        )
        public BaseResponseDto<AuditResponseDto> unblock(CommunityUser user) {
                return new BaseResponseDto<>(service.unblock(user));
        }

        @PostMapping("/{id}/hand-draw-cash")
        @Operation(
                summary = "后台提现",
                description = "现金提现"
        )
        public BaseResponseDto<CommunityUserDto> handDrawCash(@PathVariable Long id, @RequestBody DrawCashDto dto) {
                return new BaseResponseDto<>(service.handDrawCash(id,dto));
        }

//        @PostMapping("/draw-cash")
//        @Operation(
//                summary = "提现",
//                description = "现金提现"
//        )
//        @PreAuthorize("isAuthenticated()")
//        public BaseResponseDto<DrawCashResult> drawCash(HttpServletRequest request, @RequestBody DrawCashDto dto) {
//                return new BaseResponseDto<>(service.drawCashNew(request,dto));
//        }

        @PostMapping("/{id}/skip-notice")
        @Operation(
                summary = "跳过提示",
                description = "跳过提示"
        )
        @JsonView(ResourceViews.Detail.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<AuditResponseDto> skipNotice(@PathVariable Long id, @RequestBody SkipNoticeDto dto) {
                return new BaseResponseDto<>(service.skipNotice(id,dto));
        }

        @GetMapping("/{id}/get-invite-user")
        @Operation(
                summary = "获取邀请列表",
                description = "获取邀请列表"
        )
        @JsonView(ResourceViews.Basic.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<SimpleUserDto> getInviteList(@PathVariable Long id) {
                return new BaseResponseDto(service.getInviteList(id));
        }

        @PostMapping("/{source}/verify-code")
        @Operation(
                summary = "获取短信验证码",
                description = "bind 绑定 unbind 解绑"
        )
        @JsonView(ResourceViews.Basic.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<AuditResponseDto> getVerifyCode(@PathVariable String source, @RequestBody VerifyCodeDto dto) {
                return new BaseResponseDto(service.getVerifyCode(source,dto));
        }

        @PostMapping("/{source}/bind-mobile")
        @Operation(
                summary = "绑定/解绑手机号",
                description = "bind 绑定 unbind 解绑"
        )
        @JsonView(ResourceViews.Basic.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<CommunityUserDto> bindMobile(@PathVariable String source, @RequestBody VerifyCodeDto dto) {
                return new BaseResponseDto(service.bindMobile(source,dto));
        }

        @SneakyThrows
        @PostMapping("/import-user-info")
        @Operation(
                summary = "导入用户信息",
                description = "导入用户信息"
        )
        @JsonView(ResourceViews.Basic.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<AuditResponseDto> importUserInfo(@RequestParam("file") MultipartFile file) {
                ByteArrayInputStream stream = new ByteArrayInputStream(file.getInputStream().readAllBytes());
                List<UserInfoImportDto> userInfoImportDtoList = fileService.parseCsv(stream);
                AuditResponseDto response = service.importUserInfoByCsv(userInfoImportDtoList);
                return new BaseResponseDto<>(response);
        }

        @SneakyThrows
        @GetMapping("/export-user-info")
        @Operation(
                summary = "导出用户信息",
                description = "导出用户信息"
        )
        public void exportUserInfo(HttpServletResponse response,@ResourceQueryCustom CommunityUserExportQueryDto dto) {
                service.exportUserInfo(response,dto);
        }

        @GetMapping("/features")
        @Operation(
                summary = "社区功能列表",
                description = "list功能列表，目前主要支持提现"
        )
        @JsonView(ResourceViews.Basic.class)
        public ResourcePageResponseDto<FeatureDto> features(@ResourceQueryPredicate ResourceEntityQueryDto<FeatureDto> params) {
                return new ResourcePageResponseDto(featureService.findAll(params));
        }

        @GetMapping("/grid/{id}")
        @Operation(
                summary = "社区功能列表",
                description = "list功能列表，目前主要支持提现"
        )
        @JsonView(ResourceViews.Basic.class)
        public ResourcePageResponseDto<FeatureDto> getUsersByGrid(@ResourceQueryPredicate ResourceEntityQueryDto<FeatureDto> params) {
                return new ResourcePageResponseDto(featureService.findAll(params));
        }

        @PostMapping("/batch-block")
        @Operation(
                summary = "批量操作",
                description = "批量启用禁用用户"
        )
        @JsonView(ResourceViews.Basic.class)
        @PreAuthorize("isAuthenticated()")
        public BaseResponseDto<AuditResponseDto> batchBlockUser(@NotNull @RequestBody BlockUserDto dto) {
                return new BaseResponseDto<>(service.batchBlock(dto));
        }
}






