package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.Tags;
import org.befun.adminx.entity.TagsDto;
import org.befun.adminx.repository.TagsRepository;
import org.befun.adminx.service.TagsService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/1 11:30
 */
@ResourceController(
        entityClass = Tags.class,
        repositoryClass = TagsRepository.class,
        dtoClass = TagsDto.class,
        serviceClass = TagsService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/tag/tags")
@Tag(name = "标签")
public class TagsController extends BaseController<TagsService> {
}
