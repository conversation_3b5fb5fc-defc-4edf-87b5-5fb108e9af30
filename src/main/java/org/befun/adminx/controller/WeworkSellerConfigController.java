package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.WeworkSellerConfig;
import org.befun.adminx.entity.WeworkSellerConfigDto;
import org.befun.adminx.repository.WeWorkSellerConfigRepository;
import org.befun.adminx.service.WeworkSellerConfigService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = WeworkSellerConfig.class,
        repositoryClass = WeWorkSellerConfigRepository.class,
        dtoClass = WeworkSellerConfigDto.class,
        serviceClass = WeworkSellerConfigService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/mgnt/wework")
@Tag(name = "企微管理")
public class WeworkSellerConfigController extends BaseController<WeworkSellerConfigService> {
}
