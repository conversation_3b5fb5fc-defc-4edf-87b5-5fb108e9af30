package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.CommunityUserScores;
import org.befun.adminx.entity.CommunityUserScoresDto;
import org.befun.adminx.repository.CommunityUserScoresRepository;
import org.befun.adminx.service.CommunityUserScoresService;
import org.befun.adminx.service.download.UserScoresDownloadHelper;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@ResourceController(
        entityClass = CommunityUserScores.class,
        repositoryClass = CommunityUserScoresRepository.class,
        dtoClass = CommunityUserScoresDto.class,
        serviceClass = CommunityUserScoresService.class,
        excludeActions = {
                ResourceMethod.COUNT
        }
)
@RestController
@RequestMapping("/community/scores")
@PreAuthorize("isAuthenticated()")
@Tag(name = "社区用户积分")
public class CommunityUserScoresController extends BaseController<CommunityUserScoresService> {

        @Autowired
        private UserScoresDownloadHelper userScoresDownloadHelper;

        @GetMapping("/download")
        @JsonView(ResourceViews.Detail.class)
        @Operation(summary = "下载积分日志")
        @PreAuthorize("isAuthenticated()")
        public void download(HttpServletResponse response,@ResourceQueryPredicate ResourceEntityQueryDto<CommunityUserScoresDto> params) throws Exception {
                userScoresDownloadHelper.download(response, params);
        }
}






