package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.order.QuoteRequest;
import org.befun.adminx.entity.SampleOrder;
import org.befun.adminx.entity.SampleOrderDto;
import org.befun.adminx.repository.SampleOrderRepository;
import org.befun.adminx.service.SampleOrderService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/10 14:54
 */
@ResourceController(
        entityClass = SampleOrder.class,
        repositoryClass = SampleOrderRepository.class,
        dtoClass = SampleOrderDto.class,
        serviceClass = SampleOrderService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/sample/order")
@PreAuthorize("isAuthenticated()")
@Tag(name = "样本订单")
public class SampleOrderController extends BaseController<SampleOrderService> {

    @ResourceInstanceAction(
            action = "send",
            path = "send",
            method = RequestMethod.POST
    )
    public BaseResponseDto<AuditResponseDto> send(@NotNull SampleOrder order) {
        return new BaseResponseDto<>(service.sendDeliveryTask(order));
    }

    @PostMapping("/{id}/quote")
    @PreAuthorize("isAuthenticated()")
    @Operation(description = "订单报价")
    public BaseResponseDto<AuditResponseDto> quote(@PathVariable @NotNull Long id, @RequestBody QuoteRequest quote) {
        return new BaseResponseDto<>(service.quoteOrder(id, quote));
    }


    @PostMapping("/{id}/reject")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<AuditResponseDto> reject(@PathVariable @NotNull Long id, @RequestBody SampleOrder order) {
        String message = order.getRejectMessage();
        return new BaseResponseDto<>(service.rejectOrder(id, message));
    }

    @PostMapping("/{id}/refund")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<AuditResponseDto> refund(@PathVariable @NotNull Long id) {
        return new BaseResponseDto<>(service.refundOrder(id));
    }

    @PostMapping("/{id}/re-refund")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<AuditResponseDto> reRefund(@PathVariable @NotNull Long id) {
        return new BaseResponseDto<>(service.reRefund(id));
    }

    @PostMapping("/{id}/edit")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<SampleOrderDto> edit(@PathVariable @NotNull Long id, @RequestBody @NotNull SampleOrder update) {
        return new BaseResponseDto<>(service.updateOne(id, service.mapToDto(update)));
    }
}
