package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import com.thoughtworks.xstream.converters.extended.NamedMapConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.dto.grid.GridDetailDto;
import org.befun.adminx.dto.grid.GridMemberDetailDto;
import org.befun.adminx.dto.grid.JoinGridDto;
import org.befun.adminx.dto.grid.SimpleGridMemberDto;
import org.befun.adminx.dto.sample.SimpleUserDto;
import org.befun.adminx.entity.CommunityGrid;
import org.befun.adminx.entity.CommunityGridDto;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.repository.CommunityGridRepository;
import org.befun.adminx.service.CommunityGridService;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;


@ResourceController(
        entityClass = CommunityGrid.class,
        repositoryClass = CommunityGridRepository.class,
        dtoClass = CommunityGridDto.class,
        serviceClass = CommunityGridService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/community/grid")
@Tag(name = "网格")
public class CommunityGridController extends BaseController<CommunityGridService> {

        @PostMapping("/import")
        @Operation(summary = "网格导入")
        public ResourceResponseDto<Boolean> importGridInspector(@RequestBody MultipartFile file) {
                return new ResourceResponseDto<>(service.importGridInspec(file));
        }

        @PostMapping("/join-grid")
        @Operation(summary = "加入网格")
        public ResourceResponseDto<Boolean> joinGrid(@Valid @RequestBody JoinGridDto dto) {
                return new ResourceResponseDto<>(service.joinGrid(dto));
        }

        @PostMapping("/{grid}/community-users")
        @Operation(summary = "获取网格所有成员")
        public ResourceListResponseDto<SimpleUserDto> getAllUserByGrid(@PathVariable String grid) {
                return new ResourceListResponseDto<>(service.getAllUserByGrid(grid));
        }

        @PostMapping("/{gridId}/task/{taskId}/task-statistics")
        @Operation(summary = "网格统计")
        public ResourceResponseDto<GridDetailDto> gridDetailStatistics(@PathVariable String gridId, @PathVariable Long taskId,
                                                                       @RequestParam(defaultValue = "1",required = false) int _page,
                                                                       @RequestParam(defaultValue = "20",required = false) int _limit) {
                return new ResourceResponseDto<>(service.gridDetailStatistics(gridId, taskId,_page,_limit));
        }

        @GetMapping("/{gridId}/members")
        @Operation(summary = "网格成员")
        public ResourcePageResponseDto<SimpleGridMemberDto> userList(@PathVariable String gridId,
                                                               @RequestParam(defaultValue = "1",required = false) int _page,
                                                               @RequestParam(defaultValue = "20",required = false) int _limit) {
                return new ResourcePageResponseDto(service.member(gridId,_page,_limit));
        }

        @GetMapping("/{gridId}/user/{cuid}/detail")
        @Operation(summary = "网格成员资料")
        public ResourceResponseDto<GridMemberDetailDto> memberDetail(@PathVariable String gridId, @PathVariable Long cuid) {
                return new ResourceResponseDto<>(service.memberDetail(gridId, cuid));
        }

}
























