package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.BackupAccount;
import org.befun.adminx.entity.BackupAccountDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.Feature;
import org.befun.adminx.entity.FeatureDto;
import org.befun.adminx.repository.BackupAccountRepository;
import org.befun.adminx.repository.FeatureRepository;
import org.befun.adminx.service.BackupService;
import org.befun.adminx.service.FeatureService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = Feature.class,
        repositoryClass = FeatureRepository.class,
        dtoClass = FeatureDto.class,
        serviceClass = FeatureService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/mgnt/features")
@Tag(name = "功能管理")
public class FeatureController extends BaseController<FeatureService> {
}
