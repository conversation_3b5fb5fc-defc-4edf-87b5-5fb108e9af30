package org.befun.adminx.controller;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.clone.CloneAccountDto;
import org.befun.adminx.dto.user.RegisterInfoDto;
import org.befun.adminx.dto.user.SendTemplateDto;
import org.befun.adminx.dto.user.UserInfoDto;
import org.befun.adminx.entity.JourneyMapDto;
import org.befun.adminx.entity.XmPlusUser;
import org.befun.adminx.entity.XmPlusUserDto;
import org.befun.adminx.entity.survey.SurveyDto;
import org.befun.adminx.entity.survey.TemplateGroupDto;
import org.befun.adminx.repository.UserRepository;
import org.befun.adminx.service.BackupService;
import org.befun.adminx.service.UserService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;


/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/8 19:26
 */
@ResourceController(
        entityClass = XmPlusUser.class,
        repositoryClass = UserRepository.class,
        dtoClass = XmPlusUserDto.class,
        serviceClass = UserService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/xmpuser/users")
@Tag(name = "用户")
public class UserController extends BaseController<UserService> {

    @Autowired
    private BackupService backupService;

    @GetMapping("/get-template-surveys")
    @Operation(
            summary = "获取问卷模板",
            description = "获取指定账号问卷模板"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyDto> getTemplateSurveys() {
        return new ResourceResponseDto(service.getTemplateSurveys());
    }

    @GetMapping("/get-template-journeymap")
    @Operation(
            summary = "获取旅程模板",
            description = "获取指定账号旅程模板"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<JourneyMapDto> getTemplateJourneyMap() {
        return new ResourceResponseDto(service.getTemplateJourneyMap());
    }

    @GetMapping("/get-template-questions")
    @Operation(
            summary = "获取问卷模板",
            description = "获取指定账号问卷模板"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<TemplateGroupDto> getTemplateQuestions() {
        return new ResourceResponseDto(service.getTemplateSurveyQuestions());
    }

    @SneakyThrows
    @ResourceInstanceAction(
            action = "sendTemplate",
            path = "send-template",
            method = RequestMethod.POST
    )
    @Tag(name = "设置问卷和旅程模板")
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto sendTemplate(XmPlusUserDto user, @RequestBody SendTemplateDto dto) {
        service.sendTemplate(user, dto);
        return new BaseResponseDto<>();
    }

    @ResourceInstanceAction(
            action = "block",
            path = "block",
            method = RequestMethod.POST
    )
    public BaseResponseDto<AuditResponseDto> block(XmPlusUserDto user) {
        return new BaseResponseDto<>(service.block(user));
    }

    @ResourceInstanceAction(
            action = "unblock",
            path = "unblock",
            method = RequestMethod.POST
    )
    public BaseResponseDto<AuditResponseDto> unblock(XmPlusUserDto user) {
        return new BaseResponseDto<>(service.unblock(user));
    }

    @ResourceInstanceAction(
            action = "unbind",
            path = "unbind",
            method = RequestMethod.POST
    )
    public BaseResponseDto<AuditResponseDto> unbind(XmPlusUserDto user) {
        return new BaseResponseDto<>(service.unbind(user));
    }

    @PostMapping("/{id}/update-version")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<AuditResponseDto> updateVersion(@PathVariable @NotNull Long id, @RequestBody @NotNull UserInfoDto update) {
        return new BaseResponseDto<>(service.updateVersion(id, update));
    }

    @PostMapping("/register")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<AuditResponseDto> register(@Valid @RequestBody RegisterInfoDto infoDto) {
        return new BaseResponseDto<>(service.register(infoDto));
    }

    @PostMapping("/{id}/toggle-survey-content-audit")
    @PreAuthorize("isAuthenticated()")
    public BaseResponseDto<Boolean> toggleSurveyContentAudit(@PathVariable @NotNull Long id) {
        return new BaseResponseDto<>(service.toggleSurveyContentAudit(id));
    }

    @ResourceInstanceAction(
            action = "clone",
            path = "/clone",
            method = RequestMethod.POST
    )
    public BaseResponseDto<String> clone(XmPlusUserDto user) {
        CloneAccountDto cloneAccountDto = new CloneAccountDto();
        cloneAccountDto.setTargetOrgId(user.getOrganization().getId());
        backupService.cloneAccount(cloneAccountDto);
        return new BaseResponseDto<>();
    }

    @ResourceInstanceAction(
            action = "purge",
            path = "/purge",
            method = RequestMethod.POST
    )
    public BaseResponseDto<String> purge(XmPlusUserDto user) {
        backupService.purgeDataByOrg(user.getOrganization().getId());
        return new BaseResponseDto<>();
    }
}
