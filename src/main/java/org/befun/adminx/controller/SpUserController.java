package org.befun.adminx.controller;

import org.befun.adminx.entity.SPlusUser;
import org.befun.adminx.entity.SPlusUserDto;
import org.befun.adminx.repository.SpUserRepository;
import org.befun.adminx.service.SpUserService;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/12/16 15:21
 */
@ResourceController(
        entityClass = SPlusUser.class,
        repositoryClass = SpUserRepository.class,
        dtoClass = SPlusUserDto.class,
        serviceClass = SpUserService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/spuser/users")
public class SpUserController {
}
