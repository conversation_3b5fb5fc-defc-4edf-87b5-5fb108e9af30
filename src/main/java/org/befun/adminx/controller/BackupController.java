package org.befun.adminx.controller;

/**
 * The class description
 *
 * <AUTHOR>
 */

import org.befun.adminx.dto.audit.AuditEvaluateRequestDto;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.audit.AuditResultDto;
import org.befun.adminx.dto.clone.CloneAccountDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.repository.AuditRepository;
import org.befun.adminx.repository.BackupAccountRepository;
import org.befun.adminx.repository.BackupLogRepository;
import org.befun.adminx.service.AuditService;
import org.befun.adminx.service.BackupService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.*;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;
import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ONE;

/**
 * The class description
 *
 * <AUTHOR>
 */
@ResourceController(
        entityClass = BackupAccount.class,
        repositoryClass = BackupAccountRepository.class,
        dtoClass = BackupAccountDto.class,
        serviceClass = BackupService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/mgnt/backup_accounts")
@ResourceEmbeddedMany(
        path = "logs",
        mappedBy = "logs",
        entityClass = BackupLog.class,
        repositoryClass = BackupLogRepository.class,
        dtoClass = BackupLogDto.class,
        excludeActions = {BATCH_UPDATE, DELETE_ONE, UPDATE_ONE, COUNT, CREATE, FIND_ONE},
        docTag = "备份帐号-日志",
        docCrud = "日志",
        permissions = {
        }
)
public class BackupController extends BaseController<BackupService> {

        @ResourceInstanceAction(
                action = "enable",
                path = "enable",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> enable(BackupAccount account) {
                service.toggle(account, true);
                return new BaseResponseDto<>();
        }

        @ResourceInstanceAction(
                action = "disable",
                path = "disable",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> disable(BackupAccount account) {
                service.toggle(account, false);
                return new BaseResponseDto<>();
        }

        @ResourceInstanceAction(
                action = "backup",
                path = "backup",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> backup(BackupAccount account) {
                service.backupAccount(account);
                return new BaseResponseDto<>();
        }

        @ResourceInstanceAction(
                action = "recovery",
                path = "recovery",
                method = RequestMethod.POST
        )
        public BaseResponseDto<String> recovery(BackupAccount account) {
                service.recoveryAccount(account, false);
                return new BaseResponseDto<>();
        }
}
