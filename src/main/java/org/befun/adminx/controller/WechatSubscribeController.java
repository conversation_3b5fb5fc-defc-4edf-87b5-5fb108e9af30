package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.adminx.entity.WechatSubscribeRecordDto;
import org.befun.adminx.entity.WechatSubscribeRecord;
import org.befun.adminx.repository.WechatSubscribeRecordRepository;
import org.befun.adminx.service.wechat.WechatSubscribeService;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/1 11:30
 */
@ResourceController(
        entityClass = WechatSubscribeRecord.class,
        repositoryClass = WechatSubscribeRecordRepository.class,
        dtoClass = WechatSubscribeRecordDto.class,
        serviceClass = WechatSubscribeService.class,
        excludeActions = {
                ResourceMethod.COUNT
        },
        permission = "isAuthenticated()"
)
@RestController
@RequestMapping("/wechat/wechat-subscribe")
@Tag(name = "微信订阅")
public class WechatSubscribeController extends BaseController<WechatSubscribeService> {
}
