package org.befun.adminx.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.draft.WxMpDraftList;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/15 15:43
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
@Tag(name = "微信")
public class WechatController {

    @Value("${community.wechat-reply-message:true}")
    private Boolean wechatReplyMessage;

    @Autowired
    private WechatConfigureService wechatConfigureService;

    @GetMapping(value = "/notify-message")
    public String verifyToken(@RequestParam String echostr , @RequestParam String signature, @RequestParam Long timestamp, @RequestParam String nonce) {
        return echostr;
    }

    @PostMapping(value = "/notify-message", produces = MediaType.APPLICATION_XML_VALUE)
    public String notify(@RequestBody String xml) {
        String msg = wechatConfigureService.notifyMessage(xml);
        return wechatReplyMessage ? msg : "";
    }

    @GetMapping(value = "/get-draft-list")
    public WxMpDraftList getDraftList() {
        return wechatConfigureService.getDraftList();
    }
}
