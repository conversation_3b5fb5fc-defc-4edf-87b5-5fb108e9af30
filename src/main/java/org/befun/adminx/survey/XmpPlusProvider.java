package org.befun.adminx.survey;

import org.befun.adminx.constant.survey.ChannelType;
import org.befun.adminx.dto.survey.AbstractSurvey;
import org.befun.adminx.dto.survey.AbstractSurveyChannel;
import org.befun.adminx.entity.survey.Survey;
import org.befun.adminx.entity.survey.SurveyChannel;
import org.befun.adminx.property.AdminProperty;
import org.befun.adminx.repository.SurveyChannelRepository;
import org.befun.adminx.repository.SurveyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component("XM_PLUS")
public class XmpPlusProvider implements Provider {
    @Autowired
    private AdminProperty adminProperty;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    /**
     *
     * @param sid
     * @return
     */
    @Override
    public AbstractSurvey getSurvey(String sid) {
        Long id = Long.valueOf(sid);
        Assert.notNull(id, "invalid sid");
        Optional<Survey> survey = surveyRepository.findById(id);

        if (survey.isEmpty()) {
            return null;
        }

        List<SurveyChannel> channels = surveyChannelRepository.findAllBySidAndType(id, ChannelType.SURVEY_PLUS);
        List<AbstractSurveyChannel> channelList = new ArrayList<>();

        for (SurveyChannel c : channels) {
            AbstractSurveyChannel channel = new AbstractSurveyChannel(c);
            channel.setUrl(String.format(adminProperty.getXmPlusUrl(), sid, channel.getId()));
            channelList.add(channel);
        }

        return new AbstractSurvey().toBuilder()
                .id(sid)
                .name(survey.get().getRealTitle())
                .title(survey.get().getTitle())
                .status(survey.get().getStatus() == 1 ? true : false)
                .channels(channelList)
                .build();
    }

    @Override
    public Boolean getSurveyStatus(String sid) {
        Long id = Long.valueOf(sid);
        Assert.notNull(id, "invalid sid");
        Optional<Survey> survey = surveyRepository.findById(id);

        if (survey.isEmpty()) {
            return false;
        }
        return survey.get().getStatus() == 1 ? true : false;
    }
}
