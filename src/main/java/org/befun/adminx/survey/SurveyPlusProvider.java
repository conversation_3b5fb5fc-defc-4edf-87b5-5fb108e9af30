package org.befun.adminx.survey;

import org.apache.commons.lang3.StringUtils;
import org.befun.adminx.dto.survey.AbstractSurvey;
import org.befun.adminx.property.AdminProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component("SURVEY_PLUS")
public class SurveyPlusProvider implements Provider {

    @Autowired
    private AdminProperty adminProperty;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     *
     * @param sid
     * @return
     */
    @Override
    public AbstractSurvey getSurvey(String sid) {
        // TBD, format check
        Assert.notNull(sid, "missing sid");
        String query = String.format("SELECT surveyls_title AS title FROM %s.surveys_languagesettings WHERE surveyls_survey_id = '%s'",
                adminProperty.getSurveyPlusDbName(),
                sid);
        String url = String.format(adminProperty.getSurveyPlusUrl(), sid);
        try {
            Object result = entityManager.createNativeQuery(query).getSingleResult();
            return new AbstractSurvey().builder()
                    .name(result.toString())
                    .url(url)
                    .id(sid)
                    .status(true)
                    .build();
        } catch (NoResultException ex) {
            return null;
        }
    }

    @Override
    public Boolean getSurveyStatus(String sid) {
        if(StringUtils.isEmpty(sid)) return false;
        String query = String.format("SELECT active AS title FROM %s.surveys WHERE sid = '%s'",
                adminProperty.getSurveyPlusDbName(),
                sid);
        try {
            Object result = entityManager.createNativeQuery(query).getSingleResult();
            if(result.toString().equals("Y")) return true;
            else return false;
        } catch (NoResultException ex) {
            return false;
        }
    }
}

