package org.befun.adminx.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component("CTMJsonHelper")
public class JsonHelper {

    @Autowired
    private ObjectMapper objectMapper;

    public <T> T toObject(String s, Class<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                return objectMapper.readValue(i, clazz);
            } catch (JsonProcessingException e) {
                log.error("[json to object]失败：目标类型={}, json={}", clazz.getName(), i);
            }
            return null;
        }).orElse(null);
    }

    public <T> List<T> toList(String s, Class<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                return objectMapper.readValue(i, objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz));
            } catch (JsonProcessingException e) {
                log.error("[json to List]失败：元素类型={}, json={}", clazz.getName(), i);
            }
            return (List<T>) null;
        }).orElse(null);
    }

    public Map<String, Object> toMap(Object object) {
        return Optional.ofNullable(object).map(i -> {
            try {
                objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                return objectMapper.convertValue(i, objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, Object.class));
            } catch (Exception e) {
                log.error("[object to Map]失败：对象类型={}", i.getClass().getName());
            }
            return (Map<String, Object>) null;
        }).orElse(null);
    }

    public String toJson(Object object) {
        return Optional.ofNullable(object).map(i -> {
            try {
                return objectMapper.writeValueAsString(i);
            } catch (JsonProcessingException e) {
                log.error("[object to json]失败：对象类型={}", i.getClass().getName());
            }
            return null;
        }).orElse(null);
    }
}
