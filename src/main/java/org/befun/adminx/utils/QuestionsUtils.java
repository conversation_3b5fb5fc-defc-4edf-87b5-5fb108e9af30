package org.befun.adminx.utils;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.survey.QuestionType;
import org.befun.adminx.entity.survey.BaseQuestion;
import org.befun.adminx.entity.survey.SurveyQuestion;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
public class QuestionsUtils {


    /**
     * 过滤题目中的题组并排序
     * @param questions
     * @return
     */
    public static List<SurveyQuestion> questionsFilterGroup(List<SurveyQuestion> questions) {
        List<SurveyQuestion> questionsOrderList = new ArrayList<>();
        if(questions.stream().noneMatch(q -> QuestionType.GROUP.equals(q.getType()))){
            return  questions;
        };
        questions.stream()
                .filter(q-> QuestionType.GROUP.equals(q.getType()))
                .sorted(Comparator.comparing(BaseQuestion::getSequence))
                .forEach(
                        group-> questions.stream()
                                .filter(q-> group.getName().equals(q.getGroupCode()))
                                .sorted(Comparator.comparing(BaseQuestion::getSequence))
                                .forEach(questionsOrderList::add)
                );
        return questionsOrderList;
    }
}
