package org.befun.adminx.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.befun.adminx.constant.GenerateCodeType;
import org.befun.core.generator.SysConvert;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/11 2:19 下午
 */
public class CreateRandomStr {
    /**
     * 1.生成的字符串每个位置都有可能是str中的一个字母或数字，需要导入的包是import java.util.Random;
     * @param length
     * @return
     */
    public static String createRandomStr(int length){
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            stringBuffer.append(str.charAt(number));
        }
        return stringBuffer.toString();
    }

    public static String generatorPayNo() {
        String ms = DateFormatter.format(LocalDateTime.now(), DateFormatter.DATE_TIME_FORMATTER2);
        return ms + RandomStringUtils.random(18, true, true);
    }

    public static String generatorPayNo(Long id) {
        String ms = DateFormatter.format(LocalDateTime.now(), DateFormatter.DATE_TIME_FORMATTER2);
        return ms + SysConvert.toX(id);
    }

    public static String generatorVerifyCode(int count, GenerateCodeType type) {
        boolean number = false;
        boolean character = false;

        switch (type) {
            case CHARACTER:
                character = true;
                break;
            case NUMBER_AND_CHARACTER:
                character = true;
                number = true;
                break;
            default:
                number = true;
        }
        return RandomStringUtils.random(count, character, number);
    }

}
