package org.befun.adminx.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class DateFormatter {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
    public static final SimpleDateFormat FORMATTER2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final TimeZone timeZoneSH = TimeZone.getTimeZone("Asia/Shanghai");

    public static Date parseDate(String s){

        Date date = null;
        if(s == null){
            Date currentTime    = new Date();
            s   = FORMATTER2.format(currentTime);
        }
        try {
            date = FORMATTER2.parse(s);
        }catch (ParseException e){
            e.printStackTrace();
        }
        return date;
    }

    public static Long dateToTimeStamp(String time) {
        try {
            Date date = FORMATTER.parse(time);
            return date.getTime()/1000;
        }catch (ParseException e){
            e.printStackTrace();
        }
        return null;
    }

    public static String timeStampToDate(String time, String type) {
        SimpleDateFormat sdf;
        switch (type){
            case "YEAR":
                sdf = new SimpleDateFormat("yyyy");break;
            case "MONTH":
                sdf = new SimpleDateFormat("yyyy-MM");break;
            case "DAY":
                sdf = new SimpleDateFormat("yyyy-MM-dd");break;
            case "HOUR":
                sdf = new SimpleDateFormat("yyyy-MM-dd HH");break;
            case "MINUTE":
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");break;
            default:
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
        Long timeLong = Long.parseLong(time);
        Date date;
        try {
            sdf.setTimeZone(timeZoneSH);
            date = sdf.parse(sdf.format(timeLong));
            return sdf.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 日期格式字符串转换
     * @param date_str 字符串日期
     * @return String
     */
    public static String parseDateToString(String date_str){
        Date date = null;
        try {
            date = FORMATTER.parse(date_str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return FORMATTER.format(date);
    }

    public static String getNowStringDate() {
        Date date = new Date();
        FORMATTER.setTimeZone(timeZoneSH);
        return FORMATTER.format(date);
    }

    public static Date getNowDate() {
        return new Date();
    }

    public static Integer getAge(Date birthDay){
        if(birthDay == null){
            return null;
        }
        Calendar birth = Calendar.getInstance();
        Calendar today = Calendar.getInstance();

        birth.setTime(birthDay);

        int yearDifference = today.get(Calendar.YEAR) - birth.get(Calendar.YEAR);

        if (today.get(Calendar.MONTH) < birth.get(Calendar.MONTH)) {
            yearDifference--;
        } else if (today.get(Calendar.MONTH) == birth.get(Calendar.MONTH)
                && today.get(Calendar.DAY_OF_MONTH) < birth.get(Calendar.DAY_OF_MONTH)) {
            yearDifference--;
        }

        return yearDifference;
    }

    /**
     * 根据age 获取出生年
     * @param age
     * @return
     */
    public static String getBirthdayFromAge(Integer age) {
        Calendar c = Calendar.getInstance();
        c.set(c.get(Calendar.YEAR) - age, c.get(Calendar.MONTH),c.get(Calendar.DAY_OF_MONTH),0,0,0);
        return getStringTime(c.getTime());
    }

    /**
     * 获取昨天0点的时间
     * @return
     */
    public static String getLastDay(int hour, int minute, int second) {
        Calendar c = Calendar.getInstance();
        c.set(c.get(Calendar.YEAR), c.get(Calendar.MONTH),c.get(Calendar.DAY_OF_MONTH)-1,hour,minute,second);
        return getStringTime(c.getTime());
    }

    /**
     * 设置时间为前一天的最后一秒*
     * @param date
     * @return
     */
    public static Date getLastEndDate(Date date) {
        if(date == null) return null;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);

        // 设置时间为当天的最后一秒
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999); // 可选，如果需要精确到毫秒级别

        return c.getTime();
    }

    public static void main(String[] args) {
        System.out.println(getAge(null));
        System.out.println(convertToYear(getNowDate()));
    }

    public static Date convertToDate(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = format.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    // 将Date转换为year
    public static Integer convertToYear(Date date) {
        if(date == null) return null;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    /**
     * 设置时间为当天的最后一秒*
     * @param date
     * @return
     */
    public static Date getEndDate(Date date) {
        if(date == null) return null;
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        // 设置时间为当天的最后一秒
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999); // 可选，如果需要精确到毫秒级别

        return c.getTime();
    }

    /**
     * 设置时间为当月的第一天*
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        // 将日期设置为当月的第一天
        c.set(Calendar.DAY_OF_MONTH, 1);

        // 清除时分秒毫秒
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        return c.getTime();
    }

    /**
     * 获取后一天的时间
     * @return
     */
    public static String getPlusDay(String time, Integer amount) {
        if(StringUtils.isEmpty(time)) return null;
        time += " 00:00:00";
        Calendar c = Calendar.getInstance();
        c.setTime(parseDate(time));
        c.add(Calendar.DATE, amount);
        return getStringTime(c.getTime()).substring(0,10);
    }

    public static String getStringDate(Date date) {
        if(date == null) return getNowStringDate();
        FORMATTER.setTimeZone(timeZoneSH);
        return FORMATTER.format(date);
    }

    public static String getStringTime(Date date) {
        FORMATTER2.setTimeZone(timeZoneSH);
        return FORMATTER2.format(date);
    }

    public static <D> String format(D dateTime, DateTimeFormatter formatter) {
        if (dateTime == null) {
            return null;
        } else if (dateTime instanceof Date) {
            return formatter.format(toLocalDateTime((Date)dateTime));
        } else if (dateTime instanceof LocalDateTime) {
            return formatter.format((LocalDateTime)dateTime);
        } else {
            return dateTime instanceof LocalDate ? formatter.format((LocalDate)dateTime) : null;
        }
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return date == null ? null : LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 增加天数*
     * @param date
     * @param days
     * @return
     */
    public static Date addDays(Date date, int days) {
        if(date == null) date = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }
}
