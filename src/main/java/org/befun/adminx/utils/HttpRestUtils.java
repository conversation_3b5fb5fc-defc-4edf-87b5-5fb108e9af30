package org.befun.adminx.utils;

import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/7/12 09:46
 */
public class HttpRestUtils {

    /**
     * http post
     * */
    public static String post(String url, MultiValueMap<String, String> params) throws IOException {
        return  httpRestClient(url, HttpMethod.POST, params);
    }

    /**
     * http get
     * */
    public static String get(String url, MultiValueMap<String, String> params) throws IOException {
        return  httpRestClient(url, HttpMethod.POST, params);
    }

    /**
     * HttpMethod  post/get
     * */
    private static String httpRestClient(String url, HttpMethod method, MultiValueMap<String, String> params) throws IOException {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10*1000);
        requestFactory.setReadTimeout(10*1000);
        RestTemplate client = new RestTemplate(requestFactory);
        HttpHeaders headers = new HttpHeaders();
        //headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);
        //  执行HTTP请求
        ResponseEntity<String> response = null;
        try{
            response = client.exchange(url, HttpMethod.POST, requestEntity, String.class);
            return response.getBody();
        }
        catch (HttpClientErrorException e){
            e.printStackTrace();
            return "";
        }
        catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static <T> void entitySuperclassToUrlParam(T t,Class clazz,StringBuffer urlParam){
        //如果实体类对象为Object类型，则不处理
        if(!clazz.equals(Object.class)) {
            //获取实体类对象下的所有成员，并保存到 URL 参数存储器中
            Arrays.stream(clazz.getDeclaredFields()).forEach(field -> {
                //设置可以操作私有成员
                field.setAccessible(true);
                try {
                    //获取成员值
                    Object value = field.get(t);
                    //成员值为 Null 时，则不处理
                    if (Objects.nonNull(value)) {
                        urlParam.append(field.getName()).append("=").append(value).append("&");
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            });
        }
        if(urlParam.length()>0) {
            //去除最后一个&字符
            urlParam.deleteCharAt(urlParam.length() - 1);
        }
    }
}
