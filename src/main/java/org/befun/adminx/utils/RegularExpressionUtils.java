package org.befun.adminx.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 */
@Slf4j
public class RegularExpressionUtils {

    static Pattern pattern = Pattern.compile("<.+?>", Pattern.DOTALL);
    static final Pattern PATTERN_MOBILE = Pattern.compile("^[0-9]{11}$");
    static final Pattern PATTERN_QUESTION_NAME = Pattern.compile("([\\S]*)\\.");
    static final Pattern PATTERN_THESAURUS_ID = Pattern.compile("([\\S]*)\\.containsThesaurus\\(([1-9]\\d*)\\)");
    static final Pattern PATTERN_THESAURUS_KEY = Pattern.compile("([\\S]*)\\.containsThesaurus\\([1-9]\\d*\\)");
    static final Pattern PATTERN_THESAURUS_VALUE = Pattern.compile("containsThesaurus\\([1-9]\\d*\\)");



    public static String replaceHtml(String html) {
        String result = (html == null) ? null : pattern.matcher(html).replaceAll("");
        return result;
    }

    public static String replaceThesaurus(String expression, HashMap<String, Object> content) {
        Matcher matcher = PATTERN_THESAURUS_KEY.matcher(expression);

        int matcherStart = 0;
        while (matcher.find(matcherStart)){
            String exp = String.format("%s.containsThesaurus\\([1-9]\\d*\\)", matcher.group(1));
            String target = String.format("%s.selected(\"%s\")",
                    matcher.group(1),
                    Objects.toString(((HashMap<?, ?>)content.get(matcher.group(1))).get("value"))
            );
            expression =  Pattern.compile(exp).matcher(expression).replaceAll(target);
            matcherStart = matcher.end();
        }
        return expression;
    }

    public static HashMap<Long, String> getThesaurusIds(String expression) {
        Matcher matcher = PATTERN_THESAURUS_ID.matcher(expression);
        HashMap<Long, String> thesaurusIdMap = new HashMap<>();

        int matcherStart = 0;
        while (matcher.find(matcherStart)){
            thesaurusIdMap.put(Long.valueOf(matcher.group(2)), matcher.group(1));
            matcherStart = matcher.end();
        }
        return thesaurusIdMap;
    }

    public static Set<String> getQuestionNames(String expression) {
        Matcher matcher = PATTERN_QUESTION_NAME.matcher(expression);
        Set<String> nameList= new HashSet<>();
        
        int matcherStart = 0;
        while (matcher.find(matcherStart)){
            nameList.add(matcher.group(1));
            matcherStart = matcher.end();
        }
        return nameList;
    }

    public static String hiddenMobile(String mobile) {
        if (StringUtils.isEmpty(mobile) || mobile.length() <= 7) {
            return "-";
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    public static String hiddenEmail(String email) {
        if (StringUtils.isEmpty(email) || email.length() <= 7) {
            return "-";
        }
        return email.replaceAll("[^@]+(?=@)", "****");
    }

    public static boolean isMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        } else {
            return PATTERN_MOBILE.matcher(mobile).matches();
        }
    }

    public static void main(String[] args){
    }



}
