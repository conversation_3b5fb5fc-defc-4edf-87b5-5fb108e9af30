package org.befun.adminx.utils;

import org.befun.adminx.dto.download.ZipFileDto;
import org.befun.core.exception.BadRequestException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
public class DownloadUtils {

    /**
     * 响应导出response
     *
     * @param response
     * @param fileName
     * @param outB
     */
    public static void responseRequest(HttpServletResponse response, String fileName, byte[] bytes) {
        ServletOutputStream outputStream = null;
        response.reset();
        try {
            // 流跨域
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "content-type, x-requested-with");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-type", "text/html;charset=UTF-8");

            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
//            response.setContentType("application/octet-stream");
            response.setContentType("application/zip");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            outputStream = response.getOutputStream();

            outputStream.write(bytes);
            outputStream.flush();
            outputStream.close();

        } catch (UnsupportedEncodingException e) {
            throw new BadRequestException("build urlCode file name error", e.getMessage());
        } catch (IOException e) {
            throw new BadRequestException(" response outputStream error", e.getMessage());
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                throw new BadRequestException("close response outputStream error", e.getMessage());
            }
        }
    }

    /**
     * 构建zip文件
     *
     * @param files
     * @return
     * @throws IOException
     */
    public static ByteArrayOutputStream zipFiles(List<ZipFileDto> files) throws IOException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream)) {
            byte[] bytes = new byte[2048];

            for (ZipFileDto file : files) {

                try (InputStream fis = new ByteArrayInputStream(file.getBytes()); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    zos.putNextEntry(new ZipEntry(file.getFileName()));
                    int bytesRead;
                    while ((bytesRead = bis.read(bytes)) != -1) {
                        zos.write(bytes, 0, bytesRead);
                    }
                    zos.closeEntry();
                }
            }
            zos.close();
            return byteArrayOutputStream;
        }
    }
}
