package org.befun.adminx.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述：根据城市查找编号，根据编号查找城市
 * @Author: leo
 * @Date 2022.5.13
 */
public class CityAndCodeUtil {
    private static Map<String,String> cityKeyMap = new HashMap();
    private static Map<String,String> codeKeyMap = new HashMap();

    static{
//        cityKeyMap.put("珠海","4404");
        codeKeyMap.put("110000","北京");
        codeKeyMap.put("110100","北京");
        codeKeyMap.put("110101","东城区");
        codeKeyMap.put("110102","西城区");
        codeKeyMap.put("110105","朝阳区");
        codeKeyMap.put("110106","丰台区");
        codeKeyMap.put("110107","石景山区");
        codeKeyMap.put("110108","海淀区");
        codeKeyMap.put("110109","门头沟区");
        codeKeyMap.put("110111","房山区");
        codeKeyMap.put("110112","通州区");
        codeKeyMap.put("110113","顺义区");
        codeKeyMap.put("110114","昌平区");
        codeKeyMap.put("110115","大兴区");
        codeKeyMap.put("110116","怀柔区");
        codeKeyMap.put("110117","平谷区");
        codeKeyMap.put("110118","密云区");
        codeKeyMap.put("110119","延庆区");
        codeKeyMap.put("120000","天津");
        codeKeyMap.put("120100","天津");
        codeKeyMap.put("120101","和平区");
        codeKeyMap.put("120102","河东区");
        codeKeyMap.put("120103","河西区");
        codeKeyMap.put("120104","南开区");
        codeKeyMap.put("120105","河北区");
        codeKeyMap.put("120106","红桥区");
        codeKeyMap.put("120110","东丽区");
        codeKeyMap.put("120111","西青区");
        codeKeyMap.put("120112","津南区");
        codeKeyMap.put("120113","北辰区");
        codeKeyMap.put("120114","武清区");
        codeKeyMap.put("120115","宝坻区");
        codeKeyMap.put("120116","滨海新区");
        codeKeyMap.put("120117","宁河区");
        codeKeyMap.put("120118","静海区");
        codeKeyMap.put("120119","蓟州区");
        codeKeyMap.put("130000","河北");
        codeKeyMap.put("130100","石家庄");
        codeKeyMap.put("130102","长安区");
        codeKeyMap.put("130104","桥西区");
        codeKeyMap.put("130105","新华区");
        codeKeyMap.put("130107","井陉矿区");
        codeKeyMap.put("130108","裕华区");
        codeKeyMap.put("130109","藁城区");
        codeKeyMap.put("130110","鹿泉区");
        codeKeyMap.put("130111","栾城区");
        codeKeyMap.put("130121","井陉县");
        codeKeyMap.put("130123","正定县");
        codeKeyMap.put("130125","行唐县");
        codeKeyMap.put("130126","灵寿县");
        codeKeyMap.put("130127","高邑县");
        codeKeyMap.put("130128","深泽县");
        codeKeyMap.put("130129","赞皇县");
        codeKeyMap.put("130130","无极县");
        codeKeyMap.put("130131","平山县");
        codeKeyMap.put("130132","元氏县");
        codeKeyMap.put("130133","赵县");
        codeKeyMap.put("130171","石家庄高新技术产业开发区");
        codeKeyMap.put("130172","石家庄循环化工园区");
        codeKeyMap.put("130181","辛集");
        codeKeyMap.put("130183","晋州");
        codeKeyMap.put("130184","新乐");
        codeKeyMap.put("130200","唐山");
        codeKeyMap.put("130202","路南区");
        codeKeyMap.put("130203","路北区");
        codeKeyMap.put("130204","古冶区");
        codeKeyMap.put("130205","开平区");
        codeKeyMap.put("130207","丰南区");
        codeKeyMap.put("130208","丰润区");
        codeKeyMap.put("130209","曹妃甸区");
        codeKeyMap.put("130224","滦南县");
        codeKeyMap.put("130225","乐亭县");
        codeKeyMap.put("130227","迁西县");
        codeKeyMap.put("130229","玉田县");
        codeKeyMap.put("130271","唐山芦台经济技术开发区");
        codeKeyMap.put("130272","唐山汉沽管理区");
        codeKeyMap.put("130273","唐山高新技术产业开发区");
        codeKeyMap.put("130274","河北唐山海港经济开发区");
        codeKeyMap.put("130281","遵化");
        codeKeyMap.put("130283","迁安");
        codeKeyMap.put("130284","滦州");
        codeKeyMap.put("130300","秦皇岛");
        codeKeyMap.put("130302","海港区");
        codeKeyMap.put("130303","山海关区");
        codeKeyMap.put("130304","北戴河区");
        codeKeyMap.put("130306","抚宁区");
        codeKeyMap.put("130321","青龙满族自治县");
        codeKeyMap.put("130322","昌黎县");
        codeKeyMap.put("130324","卢龙县");
        codeKeyMap.put("130371","秦皇岛经济技术开发区");
        codeKeyMap.put("130372","北戴河新区");
        codeKeyMap.put("130400","邯郸");
        codeKeyMap.put("130402","邯山区");
        codeKeyMap.put("130403","丛台区");
        codeKeyMap.put("130404","复兴区");
        codeKeyMap.put("130406","峰峰矿区");
        codeKeyMap.put("130407","肥乡区");
        codeKeyMap.put("130408","永年区");
        codeKeyMap.put("130423","临漳县");
        codeKeyMap.put("130424","成安县");
        codeKeyMap.put("130425","大名县");
        codeKeyMap.put("130426","涉县");
        codeKeyMap.put("130427","磁县");
        codeKeyMap.put("130430","邱县");
        codeKeyMap.put("130431","鸡泽县");
        codeKeyMap.put("130432","广平县");
        codeKeyMap.put("130433","馆陶县");
        codeKeyMap.put("130434","魏县");
        codeKeyMap.put("130435","曲周县");
        codeKeyMap.put("130471","邯郸经济技术开发区");
        codeKeyMap.put("130473","邯郸冀南新区");
        codeKeyMap.put("130481","武安");
        codeKeyMap.put("130500","邢台");
        codeKeyMap.put("130502","桥东区");
        codeKeyMap.put("130503","桥西区");
        codeKeyMap.put("130521","邢台县");
        codeKeyMap.put("130522","临城县");
        codeKeyMap.put("130523","内丘县");
        codeKeyMap.put("130524","柏乡县");
        codeKeyMap.put("130525","隆尧县");
        codeKeyMap.put("130526","任县");
        codeKeyMap.put("130527","南和县");
        codeKeyMap.put("130528","宁晋县");
        codeKeyMap.put("130529","巨鹿县");
        codeKeyMap.put("130530","新河县");
        codeKeyMap.put("130531","广宗县");
        codeKeyMap.put("130532","平乡县");
        codeKeyMap.put("130533","威县");
        codeKeyMap.put("130534","清河县");
        codeKeyMap.put("130535","临西县");
        codeKeyMap.put("130571","河北邢台经济开发区");
        codeKeyMap.put("130581","南宫");
        codeKeyMap.put("130582","沙河");
        codeKeyMap.put("130600","保定");
        codeKeyMap.put("130602","竞秀区");
        codeKeyMap.put("130606","莲池区");
        codeKeyMap.put("130607","满城区");
        codeKeyMap.put("130608","清苑区");
        codeKeyMap.put("130609","徐水区");
        codeKeyMap.put("130623","涞水县");
        codeKeyMap.put("130624","阜平县");
        codeKeyMap.put("130626","定兴县");
        codeKeyMap.put("130627","唐县");
        codeKeyMap.put("130628","高阳县");
        codeKeyMap.put("130629","容城县");
        codeKeyMap.put("130630","涞源县");
        codeKeyMap.put("130631","望都县");
        codeKeyMap.put("130632","安新县");
        codeKeyMap.put("130633","易县");
        codeKeyMap.put("130634","曲阳县");
        codeKeyMap.put("130635","蠡县");
        codeKeyMap.put("130636","顺平县");
        codeKeyMap.put("130637","博野县");
        codeKeyMap.put("130638","雄县");
        codeKeyMap.put("130671","保定高新技术产业开发区");
        codeKeyMap.put("130672","保定白沟新城");
        codeKeyMap.put("130681","涿州");
        codeKeyMap.put("130682","定州");
        codeKeyMap.put("130683","安国");
        codeKeyMap.put("130684","高碑店");
        codeKeyMap.put("130700","张家口");
        codeKeyMap.put("130702","桥东区");
        codeKeyMap.put("130703","桥西区");
        codeKeyMap.put("130705","宣化区");
        codeKeyMap.put("130706","下花园区");
        codeKeyMap.put("130708","万全区");
        codeKeyMap.put("130709","崇礼区");
        codeKeyMap.put("130722","张北县");
        codeKeyMap.put("130723","康保县");
        codeKeyMap.put("130724","沽源县");
        codeKeyMap.put("130725","尚义县");
        codeKeyMap.put("130726","蔚县");
        codeKeyMap.put("130727","阳原县");
        codeKeyMap.put("130728","怀安县");
        codeKeyMap.put("130730","怀来县");
        codeKeyMap.put("130731","涿鹿县");
        codeKeyMap.put("130732","赤城县");
        codeKeyMap.put("130771","张家口高新技术产业开发区");
        codeKeyMap.put("130772","张家口察北管理区");
        codeKeyMap.put("130773","张家口塞北管理区");
        codeKeyMap.put("130800","承德");
        codeKeyMap.put("130802","双桥区");
        codeKeyMap.put("130803","双滦区");
        codeKeyMap.put("130804","鹰手营子矿区");
        codeKeyMap.put("130821","承德县");
        codeKeyMap.put("130822","兴隆县");
        codeKeyMap.put("130824","滦平县");
        codeKeyMap.put("130825","隆化县");
        codeKeyMap.put("130826","丰宁满族自治县");
        codeKeyMap.put("130827","宽城满族自治县");
        codeKeyMap.put("130828","围场满族蒙古族自治县");
        codeKeyMap.put("130871","承德高新技术产业开发区");
        codeKeyMap.put("130881","平泉");
        codeKeyMap.put("130900","沧州");
        codeKeyMap.put("130902","新华区");
        codeKeyMap.put("130903","运河区");
        codeKeyMap.put("130921","沧县");
        codeKeyMap.put("130922","青县");
        codeKeyMap.put("130923","东光县");
        codeKeyMap.put("130924","海兴县");
        codeKeyMap.put("130925","盐山县");
        codeKeyMap.put("130926","肃宁县");
        codeKeyMap.put("130927","南皮县");
        codeKeyMap.put("130928","吴桥县");
        codeKeyMap.put("130929","献县");
        codeKeyMap.put("130930","孟村回族自治县");
        codeKeyMap.put("130971","河北沧州经济开发区");
        codeKeyMap.put("130972","沧州高新技术产业开发区");
        codeKeyMap.put("130973","沧州渤海新区");
        codeKeyMap.put("130981","泊头");
        codeKeyMap.put("130982","任丘");
        codeKeyMap.put("130983","黄骅");
        codeKeyMap.put("130984","河间");
        codeKeyMap.put("131000","廊坊");
        codeKeyMap.put("131002","安次区");
        codeKeyMap.put("131003","广阳区");
        codeKeyMap.put("131022","固安县");
        codeKeyMap.put("131023","永清县");
        codeKeyMap.put("131024","香河县");
        codeKeyMap.put("131025","大城县");
        codeKeyMap.put("131026","文安县");
        codeKeyMap.put("131028","大厂回族自治县");
        codeKeyMap.put("131071","廊坊经济技术开发区");
        codeKeyMap.put("131081","霸州");
        codeKeyMap.put("131082","三河");
        codeKeyMap.put("131100","衡水");
        codeKeyMap.put("131102","桃城区");
        codeKeyMap.put("131103","冀州区");
        codeKeyMap.put("131121","枣强县");
        codeKeyMap.put("131122","武邑县");
        codeKeyMap.put("131123","武强县");
        codeKeyMap.put("131124","饶阳县");
        codeKeyMap.put("131125","安平县");
        codeKeyMap.put("131126","故城县");
        codeKeyMap.put("131127","景县");
        codeKeyMap.put("131128","阜城县");
        codeKeyMap.put("131171","河北衡水高新技术产业开发区");
        codeKeyMap.put("131172","衡水滨湖新区");
        codeKeyMap.put("131182","深州");
        codeKeyMap.put("140000","山西");
        codeKeyMap.put("140100","太原");
        codeKeyMap.put("140105","小店区");
        codeKeyMap.put("140106","迎泽区");
        codeKeyMap.put("140107","杏花岭区");
        codeKeyMap.put("140108","尖草坪区");
        codeKeyMap.put("140109","万柏林区");
        codeKeyMap.put("140110","晋源区");
        codeKeyMap.put("140121","清徐县");
        codeKeyMap.put("140122","阳曲县");
        codeKeyMap.put("140123","娄烦县");
        codeKeyMap.put("140171","山西转型综合改革示范区");
        codeKeyMap.put("140181","古交");
        codeKeyMap.put("140200","大同");
        codeKeyMap.put("140212","新荣区");
        codeKeyMap.put("140213","平城区");
        codeKeyMap.put("140214","云冈区");
        codeKeyMap.put("140215","云州区");
        codeKeyMap.put("140221","阳高县");
        codeKeyMap.put("140222","天镇县");
        codeKeyMap.put("140223","广灵县");
        codeKeyMap.put("140224","灵丘县");
        codeKeyMap.put("140225","浑源县");
        codeKeyMap.put("140226","左云县");
        codeKeyMap.put("140271","山西大同经济开发区");
        codeKeyMap.put("140300","阳泉");
        codeKeyMap.put("140302","城区");
        codeKeyMap.put("140303","矿区");
        codeKeyMap.put("140311","郊区");
        codeKeyMap.put("140321","平定县");
        codeKeyMap.put("140322","盂县");
        codeKeyMap.put("140400","长治");
        codeKeyMap.put("140403","潞州区");
        codeKeyMap.put("140404","上党区");
        codeKeyMap.put("140405","屯留区");
        codeKeyMap.put("140406","潞城区");
        codeKeyMap.put("140423","襄垣县");
        codeKeyMap.put("140425","平顺县");
        codeKeyMap.put("140426","黎城县");
        codeKeyMap.put("140427","壶关县");
        codeKeyMap.put("140428","长子县");
        codeKeyMap.put("140429","武乡县");
        codeKeyMap.put("140430","沁县");
        codeKeyMap.put("140431","沁源县");
        codeKeyMap.put("140471","山西长治高新技术产业园区");
        codeKeyMap.put("140500","晋城");
        codeKeyMap.put("140502","城区");
        codeKeyMap.put("140521","沁水县");
        codeKeyMap.put("140522","阳城县");
        codeKeyMap.put("140524","陵川县");
        codeKeyMap.put("140525","泽州县");
        codeKeyMap.put("140581","高平");
        codeKeyMap.put("140600","朔州");
        codeKeyMap.put("140602","朔城区");
        codeKeyMap.put("140603","平鲁区");
        codeKeyMap.put("140621","山阴县");
        codeKeyMap.put("140622","应县");
        codeKeyMap.put("140623","右玉县");
        codeKeyMap.put("140671","山西朔州经济开发区");
        codeKeyMap.put("140681","怀仁");
        codeKeyMap.put("140700","晋中");
        codeKeyMap.put("140702","榆次区");
        codeKeyMap.put("140721","榆社县");
        codeKeyMap.put("140722","左权县");
        codeKeyMap.put("140723","和顺县");
        codeKeyMap.put("140724","昔阳县");
        codeKeyMap.put("140725","寿阳县");
        codeKeyMap.put("140726","太谷县");
        codeKeyMap.put("140727","祁县");
        codeKeyMap.put("140728","平遥县");
        codeKeyMap.put("140729","灵石县");
        codeKeyMap.put("140781","介休");
        codeKeyMap.put("140800","运城");
        codeKeyMap.put("140802","盐湖区");
        codeKeyMap.put("140821","临猗县");
        codeKeyMap.put("140822","万荣县");
        codeKeyMap.put("140823","闻喜县");
        codeKeyMap.put("140824","稷山县");
        codeKeyMap.put("140825","新绛县");
        codeKeyMap.put("140826","绛县");
        codeKeyMap.put("140827","垣曲县");
        codeKeyMap.put("140828","夏县");
        codeKeyMap.put("140829","平陆县");
        codeKeyMap.put("140830","芮城县");
        codeKeyMap.put("140881","永济");
        codeKeyMap.put("140882","河津");
        codeKeyMap.put("140900","忻州");
        codeKeyMap.put("140902","忻府区");
        codeKeyMap.put("140921","定襄县");
        codeKeyMap.put("140922","五台县");
        codeKeyMap.put("140923","代县");
        codeKeyMap.put("140924","繁峙县");
        codeKeyMap.put("140925","宁武县");
        codeKeyMap.put("140926","静乐县");
        codeKeyMap.put("140927","神池县");
        codeKeyMap.put("140928","五寨县");
        codeKeyMap.put("140929","岢岚县");
        codeKeyMap.put("140930","河曲县");
        codeKeyMap.put("140931","保德县");
        codeKeyMap.put("140932","偏关县");
        codeKeyMap.put("140971","五台山风景名胜区");
        codeKeyMap.put("140981","原平");
        codeKeyMap.put("141000","临汾");
        codeKeyMap.put("141002","尧都区");
        codeKeyMap.put("141021","曲沃县");
        codeKeyMap.put("141022","翼城县");
        codeKeyMap.put("141023","襄汾县");
        codeKeyMap.put("141024","洪洞县");
        codeKeyMap.put("141025","古县");
        codeKeyMap.put("141026","安泽县");
        codeKeyMap.put("141027","浮山县");
        codeKeyMap.put("141028","吉县");
        codeKeyMap.put("141029","乡宁县");
        codeKeyMap.put("141030","大宁县");
        codeKeyMap.put("141031","隰县");
        codeKeyMap.put("141032","永和县");
        codeKeyMap.put("141033","蒲县");
        codeKeyMap.put("141034","汾西县");
        codeKeyMap.put("141081","侯马");
        codeKeyMap.put("141082","霍州");
        codeKeyMap.put("141100","吕梁");
        codeKeyMap.put("141102","离石区");
        codeKeyMap.put("141121","文水县");
        codeKeyMap.put("141122","交城县");
        codeKeyMap.put("141123","兴县");
        codeKeyMap.put("141124","临县");
        codeKeyMap.put("141125","柳林县");
        codeKeyMap.put("141126","石楼县");
        codeKeyMap.put("141127","岚县");
        codeKeyMap.put("141128","方山县");
        codeKeyMap.put("141129","中阳县");
        codeKeyMap.put("141130","交口县");
        codeKeyMap.put("141181","孝义");
        codeKeyMap.put("141182","汾阳");
        codeKeyMap.put("150000","内蒙古自治区");
        codeKeyMap.put("150100","呼和浩特");
        codeKeyMap.put("150102","新城区");
        codeKeyMap.put("150103","回民区");
        codeKeyMap.put("150104","玉泉区");
        codeKeyMap.put("150105","赛罕区");
        codeKeyMap.put("150121","土默特左旗");
        codeKeyMap.put("150122","托克托县");
        codeKeyMap.put("150123","和林格尔县");
        codeKeyMap.put("150124","清水河县");
        codeKeyMap.put("150125","武川县");
        codeKeyMap.put("150171","呼和浩特金海工业园区");
        codeKeyMap.put("150172","呼和浩特经济技术开发区");
        codeKeyMap.put("150200","包头");
        codeKeyMap.put("150202","东河区");
        codeKeyMap.put("150203","昆都仑区");
        codeKeyMap.put("150204","青山区");
        codeKeyMap.put("150205","石拐区");
        codeKeyMap.put("150206","白云鄂博矿区");
        codeKeyMap.put("150207","九原区");
        codeKeyMap.put("150221","土默特右旗");
        codeKeyMap.put("150222","固阳县");
        codeKeyMap.put("150223","达尔罕茂明安联合旗");
        codeKeyMap.put("150271","包头稀土高新技术产业开发区");
        codeKeyMap.put("150300","乌海");
        codeKeyMap.put("150302","海勃湾区");
        codeKeyMap.put("150303","海南区");
        codeKeyMap.put("150304","乌达区");
        codeKeyMap.put("150400","赤峰");
        codeKeyMap.put("150402","红山区");
        codeKeyMap.put("150403","元宝山区");
        codeKeyMap.put("150404","松山区");
        codeKeyMap.put("150421","阿鲁科尔沁旗");
        codeKeyMap.put("150422","巴林左旗");
        codeKeyMap.put("150423","巴林右旗");
        codeKeyMap.put("150424","林西县");
        codeKeyMap.put("150425","克什克腾旗");
        codeKeyMap.put("150426","翁牛特旗");
        codeKeyMap.put("150428","喀喇沁旗");
        codeKeyMap.put("150429","宁城县");
        codeKeyMap.put("150430","敖汉旗");
        codeKeyMap.put("150500","通辽");
        codeKeyMap.put("150502","科尔沁区");
        codeKeyMap.put("150521","科尔沁左翼中旗");
        codeKeyMap.put("150522","科尔沁左翼后旗");
        codeKeyMap.put("150523","开鲁县");
        codeKeyMap.put("150524","库伦旗");
        codeKeyMap.put("150525","奈曼旗");
        codeKeyMap.put("150526","扎鲁特旗");
        codeKeyMap.put("150571","通辽经济技术开发区");
        codeKeyMap.put("150581","霍林郭勒");
        codeKeyMap.put("150600","鄂尔多斯");
        codeKeyMap.put("150602","东胜区");
        codeKeyMap.put("150603","康巴什区");
        codeKeyMap.put("150621","达拉特旗");
        codeKeyMap.put("150622","准格尔旗");
        codeKeyMap.put("150623","鄂托克前旗");
        codeKeyMap.put("150624","鄂托克旗");
        codeKeyMap.put("150625","杭锦旗");
        codeKeyMap.put("150626","乌审旗");
        codeKeyMap.put("150627","伊金霍洛旗");
        codeKeyMap.put("150700","呼伦贝尔");
        codeKeyMap.put("150702","海拉尔区");
        codeKeyMap.put("150703","扎赉诺尔区");
        codeKeyMap.put("150721","阿荣旗");
        codeKeyMap.put("150722","莫力达瓦达斡尔族自治旗");
        codeKeyMap.put("150723","鄂伦春自治旗");
        codeKeyMap.put("150724","鄂温克族自治旗");
        codeKeyMap.put("150725","陈巴尔虎旗");
        codeKeyMap.put("150726","新巴尔虎左旗");
        codeKeyMap.put("150727","新巴尔虎右旗");
        codeKeyMap.put("150781","满洲里");
        codeKeyMap.put("150782","牙克石");
        codeKeyMap.put("150783","扎兰屯");
        codeKeyMap.put("150784","额尔古纳");
        codeKeyMap.put("150785","根河");
        codeKeyMap.put("150800","巴彦淖尔");
        codeKeyMap.put("150802","临河区");
        codeKeyMap.put("150821","五原县");
        codeKeyMap.put("150822","磴口县");
        codeKeyMap.put("150823","乌拉特前旗");
        codeKeyMap.put("150824","乌拉特中旗");
        codeKeyMap.put("150825","乌拉特后旗");
        codeKeyMap.put("150826","杭锦后旗");
        codeKeyMap.put("150900","乌兰察布");
        codeKeyMap.put("150902","集宁区");
        codeKeyMap.put("150921","卓资县");
        codeKeyMap.put("150922","化德县");
        codeKeyMap.put("150923","商都县");
        codeKeyMap.put("150924","兴和县");
        codeKeyMap.put("150925","凉城县");
        codeKeyMap.put("150926","察哈尔右翼前旗");
        codeKeyMap.put("150927","察哈尔右翼中旗");
        codeKeyMap.put("150928","察哈尔右翼后旗");
        codeKeyMap.put("150929","四子王旗");
        codeKeyMap.put("150981","丰镇");
        codeKeyMap.put("152200","兴安盟");
        codeKeyMap.put("152201","乌兰浩特");
        codeKeyMap.put("152202","阿尔山");
        codeKeyMap.put("152221","科尔沁右翼前旗");
        codeKeyMap.put("152222","科尔沁右翼中旗");
        codeKeyMap.put("152223","扎赉特旗");
        codeKeyMap.put("152224","突泉县");
        codeKeyMap.put("152500","锡林郭勒盟");
        codeKeyMap.put("152501","二连浩特");
        codeKeyMap.put("152502","锡林浩特");
        codeKeyMap.put("152522","阿巴嘎旗");
        codeKeyMap.put("152523","苏尼特左旗");
        codeKeyMap.put("152524","苏尼特右旗");
        codeKeyMap.put("152525","东乌珠穆沁旗");
        codeKeyMap.put("152526","西乌珠穆沁旗");
        codeKeyMap.put("152527","太仆寺旗");
        codeKeyMap.put("152528","镶黄旗");
        codeKeyMap.put("152529","正镶白旗");
        codeKeyMap.put("152530","正蓝旗");
        codeKeyMap.put("152531","多伦县");
        codeKeyMap.put("152571","乌拉盖管委会");
        codeKeyMap.put("152900","阿拉善盟");
        codeKeyMap.put("152921","阿拉善左旗");
        codeKeyMap.put("152922","阿拉善右旗");
        codeKeyMap.put("152923","额济纳旗");
        codeKeyMap.put("152971","内蒙古阿拉善经济开发区");
        codeKeyMap.put("210000","辽宁");
        codeKeyMap.put("210100","沈阳");
        codeKeyMap.put("210102","和平区");
        codeKeyMap.put("210103","沈河区");
        codeKeyMap.put("210104","大东区");
        codeKeyMap.put("210105","皇姑区");
        codeKeyMap.put("210106","铁西区");
        codeKeyMap.put("210111","苏家屯区");
        codeKeyMap.put("210112","浑南区");
        codeKeyMap.put("210113","沈北新区");
        codeKeyMap.put("210114","于洪区");
        codeKeyMap.put("210115","辽中区");
        codeKeyMap.put("210123","康平县");
        codeKeyMap.put("210124","法库县");
        codeKeyMap.put("210181","新民");
        codeKeyMap.put("210200","大连");
        codeKeyMap.put("210202","中山区");
        codeKeyMap.put("210203","西岗区");
        codeKeyMap.put("210204","沙河口区");
        codeKeyMap.put("210211","甘井子区");
        codeKeyMap.put("210212","旅顺口区");
        codeKeyMap.put("210213","金州区");
        codeKeyMap.put("210214","普兰店区");
        codeKeyMap.put("210224","长海县");
        codeKeyMap.put("210281","瓦房店");
        codeKeyMap.put("210283","庄河");
        codeKeyMap.put("210300","鞍山");
        codeKeyMap.put("210302","铁东区");
        codeKeyMap.put("210303","铁西区");
        codeKeyMap.put("210304","立山区");
        codeKeyMap.put("210311","千山区");
        codeKeyMap.put("210321","台安县");
        codeKeyMap.put("210323","岫岩满族自治县");
        codeKeyMap.put("210381","海城");
        codeKeyMap.put("210400","抚顺");
        codeKeyMap.put("210402","新抚区");
        codeKeyMap.put("210403","东洲区");
        codeKeyMap.put("210404","望花区");
        codeKeyMap.put("210411","顺城区");
        codeKeyMap.put("210421","抚顺县");
        codeKeyMap.put("210422","新宾满族自治县");
        codeKeyMap.put("210423","清原满族自治县");
        codeKeyMap.put("210500","本溪");
        codeKeyMap.put("210502","平山区");
        codeKeyMap.put("210503","溪湖区");
        codeKeyMap.put("210504","明山区");
        codeKeyMap.put("210505","南芬区");
        codeKeyMap.put("210521","本溪满族自治县");
        codeKeyMap.put("210522","桓仁满族自治县");
        codeKeyMap.put("210600","丹东");
        codeKeyMap.put("210602","元宝区");
        codeKeyMap.put("210603","振兴区");
        codeKeyMap.put("210604","振安区");
        codeKeyMap.put("210624","宽甸满族自治县");
        codeKeyMap.put("210681","东港");
        codeKeyMap.put("210682","凤城");
        codeKeyMap.put("210700","锦州");
        codeKeyMap.put("210702","古塔区");
        codeKeyMap.put("210703","凌河区");
        codeKeyMap.put("210711","太和区");
        codeKeyMap.put("210726","黑山县");
        codeKeyMap.put("210727","义县");
        codeKeyMap.put("210781","凌海");
        codeKeyMap.put("210782","北镇");
        codeKeyMap.put("210800","营口");
        codeKeyMap.put("210802","站前区");
        codeKeyMap.put("210803","西区");
        codeKeyMap.put("210804","鲅鱼圈区");
        codeKeyMap.put("210811","老边区");
        codeKeyMap.put("210881","盖州");
        codeKeyMap.put("210882","大石桥");
        codeKeyMap.put("210900","阜新");
        codeKeyMap.put("210902","海州区");
        codeKeyMap.put("210903","新邱区");
        codeKeyMap.put("210904","太平区");
        codeKeyMap.put("210905","清河门区");
        codeKeyMap.put("210911","细河区");
        codeKeyMap.put("210921","阜新蒙古族自治县");
        codeKeyMap.put("210922","彰武县");
        codeKeyMap.put("211000","辽阳");
        codeKeyMap.put("211002","白塔区");
        codeKeyMap.put("211003","文圣区");
        codeKeyMap.put("211004","宏伟区");
        codeKeyMap.put("211005","弓长岭区");
        codeKeyMap.put("211011","太子河区");
        codeKeyMap.put("211021","辽阳县");
        codeKeyMap.put("211081","灯塔");
        codeKeyMap.put("211100","盘锦");
        codeKeyMap.put("211102","双台子区");
        codeKeyMap.put("211103","兴隆台区");
        codeKeyMap.put("211104","大洼区");
        codeKeyMap.put("211122","盘山县");
        codeKeyMap.put("211200","铁岭");
        codeKeyMap.put("211202","银州区");
        codeKeyMap.put("211204","清河区");
        codeKeyMap.put("211221","铁岭县");
        codeKeyMap.put("211223","西丰县");
        codeKeyMap.put("211224","昌图县");
        codeKeyMap.put("211281","调兵山");
        codeKeyMap.put("211282","开原");
        codeKeyMap.put("211300","朝阳");
        codeKeyMap.put("211302","双塔区");
        codeKeyMap.put("211303","龙城区");
        codeKeyMap.put("211321","朝阳县");
        codeKeyMap.put("211322","建平县");
        codeKeyMap.put("211324","喀喇沁左翼蒙古族自治县");
        codeKeyMap.put("211381","北票");
        codeKeyMap.put("211382","凌源");
        codeKeyMap.put("211400","葫芦岛");
        codeKeyMap.put("211402","连山区");
        codeKeyMap.put("211403","龙港区");
        codeKeyMap.put("211404","南票区");
        codeKeyMap.put("211421","绥中县");
        codeKeyMap.put("211422","建昌县");
        codeKeyMap.put("211481","兴城");
        codeKeyMap.put("220000","吉林");
        codeKeyMap.put("220100","长春");
        codeKeyMap.put("220102","南关区");
        codeKeyMap.put("220103","宽城区");
        codeKeyMap.put("220104","朝阳区");
        codeKeyMap.put("220105","二道区");
        codeKeyMap.put("220106","绿园区");
        codeKeyMap.put("220112","双阳区");
        codeKeyMap.put("220113","九台区");
        codeKeyMap.put("220122","农安县");
        codeKeyMap.put("220171","长春经济技术开发区");
        codeKeyMap.put("220172","长春净月高新技术产业开发区");
        codeKeyMap.put("220173","长春高新技术产业开发区");
        codeKeyMap.put("220174","长春汽车经济技术开发区");
        codeKeyMap.put("220182","榆树");
        codeKeyMap.put("220183","德惠");
        codeKeyMap.put("220200","吉林");
        codeKeyMap.put("220202","昌邑区");
        codeKeyMap.put("220203","龙潭区");
        codeKeyMap.put("220204","船营区");
        codeKeyMap.put("220211","丰满区");
        codeKeyMap.put("220221","永吉县");
        codeKeyMap.put("220271","吉林经济开发区");
        codeKeyMap.put("220272","吉林高新技术产业开发区");
        codeKeyMap.put("220273","吉林中国新加坡食品区");
        codeKeyMap.put("220281","蛟河");
        codeKeyMap.put("220282","桦甸");
        codeKeyMap.put("220283","舒兰");
        codeKeyMap.put("220284","磐石");
        codeKeyMap.put("220300","四平");
        codeKeyMap.put("220302","铁西区");
        codeKeyMap.put("220303","铁东区");
        codeKeyMap.put("220322","梨树县");
        codeKeyMap.put("220323","伊通满族自治县");
        codeKeyMap.put("220381","公主岭");
        codeKeyMap.put("220382","双辽");
        codeKeyMap.put("220400","辽源");
        codeKeyMap.put("220402","龙山区");
        codeKeyMap.put("220403","西安区");
        codeKeyMap.put("220421","东丰县");
        codeKeyMap.put("220422","东辽县");
        codeKeyMap.put("220500","通化");
        codeKeyMap.put("220502","东昌区");
        codeKeyMap.put("220503","二道江区");
        codeKeyMap.put("220521","通化县");
        codeKeyMap.put("220523","辉南县");
        codeKeyMap.put("220524","柳河县");
        codeKeyMap.put("220581","梅河口");
        codeKeyMap.put("220582","集安");
        codeKeyMap.put("220600","白山");
        codeKeyMap.put("220602","浑江区");
        codeKeyMap.put("220605","江源区");
        codeKeyMap.put("220621","抚松县");
        codeKeyMap.put("220622","靖宇县");
        codeKeyMap.put("220623","长白朝鲜族自治县");
        codeKeyMap.put("220681","临江");
        codeKeyMap.put("220700","松原");
        codeKeyMap.put("220702","宁江区");
        codeKeyMap.put("220721","前郭尔罗斯蒙古族自治县");
        codeKeyMap.put("220722","长岭县");
        codeKeyMap.put("220723","乾安县");
        codeKeyMap.put("220771","吉林松原经济开发区");
        codeKeyMap.put("220781","扶余");
        codeKeyMap.put("220800","白城");
        codeKeyMap.put("220802","洮北区");
        codeKeyMap.put("220821","镇赉县");
        codeKeyMap.put("220822","通榆县");
        codeKeyMap.put("220871","吉林白城经济开发区");
        codeKeyMap.put("220881","洮南");
        codeKeyMap.put("220882","大安");
        codeKeyMap.put("222400","延边朝鲜族自治州");
        codeKeyMap.put("222401","延吉");
        codeKeyMap.put("222402","图们");
        codeKeyMap.put("222403","敦化");
        codeKeyMap.put("222404","珲春");
        codeKeyMap.put("222405","龙井");
        codeKeyMap.put("222406","和龙");
        codeKeyMap.put("222424","汪清县");
        codeKeyMap.put("222426","安图县");
        codeKeyMap.put("230000","黑龙江");
        codeKeyMap.put("230100","哈尔滨");
        codeKeyMap.put("230102","道里区");
        codeKeyMap.put("230103","南岗区");
        codeKeyMap.put("230104","道外区");
        codeKeyMap.put("230108","平房区");
        codeKeyMap.put("230109","松北区");
        codeKeyMap.put("230110","香坊区");
        codeKeyMap.put("230111","呼兰区");
        codeKeyMap.put("230112","阿城区");
        codeKeyMap.put("230113","双城区");
        codeKeyMap.put("230123","依兰县");
        codeKeyMap.put("230124","方正县");
        codeKeyMap.put("230125","宾县");
        codeKeyMap.put("230126","巴彦县");
        codeKeyMap.put("230127","木兰县");
        codeKeyMap.put("230128","通河县");
        codeKeyMap.put("230129","延寿县");
        codeKeyMap.put("230183","尚志");
        codeKeyMap.put("230184","五常");
        codeKeyMap.put("230200","齐齐哈尔");
        codeKeyMap.put("230202","龙沙区");
        codeKeyMap.put("230203","建华区");
        codeKeyMap.put("230204","铁锋区");
        codeKeyMap.put("230205","昂昂溪区");
        codeKeyMap.put("230206","富拉尔基区");
        codeKeyMap.put("230207","碾子山区");
        codeKeyMap.put("230208","梅里斯达斡尔族区");
        codeKeyMap.put("230221","龙江县");
        codeKeyMap.put("230223","依安县");
        codeKeyMap.put("230224","泰来县");
        codeKeyMap.put("230225","甘南县");
        codeKeyMap.put("230227","富裕县");
        codeKeyMap.put("230229","克山县");
        codeKeyMap.put("230230","克东县");
        codeKeyMap.put("230231","拜泉县");
        codeKeyMap.put("230281","讷河");
        codeKeyMap.put("230300","鸡西");
        codeKeyMap.put("230302","鸡冠区");
        codeKeyMap.put("230303","恒山区");
        codeKeyMap.put("230304","滴道区");
        codeKeyMap.put("230305","梨树区");
        codeKeyMap.put("230306","城子河区");
        codeKeyMap.put("230307","麻山区");
        codeKeyMap.put("230321","鸡东县");
        codeKeyMap.put("230381","虎林");
        codeKeyMap.put("230382","密山");
        codeKeyMap.put("230400","鹤岗");
        codeKeyMap.put("230402","向阳区");
        codeKeyMap.put("230403","工农区");
        codeKeyMap.put("230404","南山区");
        codeKeyMap.put("230405","兴安区");
        codeKeyMap.put("230406","东山区");
        codeKeyMap.put("230407","兴山区");
        codeKeyMap.put("230421","萝北县");
        codeKeyMap.put("230422","绥滨县");
        codeKeyMap.put("230500","双鸭山");
        codeKeyMap.put("230502","尖山区");
        codeKeyMap.put("230503","岭东区");
        codeKeyMap.put("230505","四方台区");
        codeKeyMap.put("230506","宝山区");
        codeKeyMap.put("230521","集贤县");
        codeKeyMap.put("230522","友谊县");
        codeKeyMap.put("230523","宝清县");
        codeKeyMap.put("230524","饶河县");
        codeKeyMap.put("230600","大庆");
        codeKeyMap.put("230602","萨尔图区");
        codeKeyMap.put("230603","龙凤区");
        codeKeyMap.put("230604","让胡路区");
        codeKeyMap.put("230605","红岗区");
        codeKeyMap.put("230606","大同区");
        codeKeyMap.put("230621","肇州县");
        codeKeyMap.put("230622","肇源县");
        codeKeyMap.put("230623","林甸县");
        codeKeyMap.put("230624","杜尔伯特蒙古族自治县");
        codeKeyMap.put("230671","大庆高新技术产业开发区");
        codeKeyMap.put("230700","伊春");
        codeKeyMap.put("230702","伊春区");
        codeKeyMap.put("230703","南岔区");
        codeKeyMap.put("230704","友好区");
        codeKeyMap.put("230705","西林区");
        codeKeyMap.put("230706","翠峦区");
        codeKeyMap.put("230707","新青区");
        codeKeyMap.put("230708","美溪区");
        codeKeyMap.put("230709","金山屯区");
        codeKeyMap.put("230710","五营区");
        codeKeyMap.put("230711","乌马河区");
        codeKeyMap.put("230712","汤旺河区");
        codeKeyMap.put("230713","带岭区");
        codeKeyMap.put("230714","乌伊岭区");
        codeKeyMap.put("230715","红星区");
        codeKeyMap.put("230716","上甘岭区");
        codeKeyMap.put("230722","嘉荫县");
        codeKeyMap.put("230781","铁力");
        codeKeyMap.put("230800","佳木斯");
        codeKeyMap.put("230803","向阳区");
        codeKeyMap.put("230804","前进区");
        codeKeyMap.put("230805","东风区");
        codeKeyMap.put("230811","郊区");
        codeKeyMap.put("230822","桦南县");
        codeKeyMap.put("230826","桦川县");
        codeKeyMap.put("230828","汤原县");
        codeKeyMap.put("230881","同江");
        codeKeyMap.put("230882","富锦");
        codeKeyMap.put("230883","抚远");
        codeKeyMap.put("230900","七台河");
        codeKeyMap.put("230902","新兴区");
        codeKeyMap.put("230903","桃山区");
        codeKeyMap.put("230904","茄子河区");
        codeKeyMap.put("230921","勃利县");
        codeKeyMap.put("231000","牡丹江");
        codeKeyMap.put("231002","东安区");
        codeKeyMap.put("231003","阳明区");
        codeKeyMap.put("231004","爱民区");
        codeKeyMap.put("231005","西安区");
        codeKeyMap.put("231025","林口县");
        codeKeyMap.put("231071","牡丹江经济技术开发区");
        codeKeyMap.put("231081","绥芬河");
        codeKeyMap.put("231083","海林");
        codeKeyMap.put("231084","宁安");
        codeKeyMap.put("231085","穆棱");
        codeKeyMap.put("231086","东宁");
        codeKeyMap.put("231100","黑河");
        codeKeyMap.put("231102","爱辉区");
        codeKeyMap.put("231121","嫩江县");
        codeKeyMap.put("231123","逊克县");
        codeKeyMap.put("231124","孙吴县");
        codeKeyMap.put("231181","北安");
        codeKeyMap.put("231182","五大连池");
        codeKeyMap.put("231200","绥化");
        codeKeyMap.put("231202","北林区");
        codeKeyMap.put("231221","望奎县");
        codeKeyMap.put("231222","兰西县");
        codeKeyMap.put("231223","青冈县");
        codeKeyMap.put("231224","庆安县");
        codeKeyMap.put("231225","明水县");
        codeKeyMap.put("231226","绥棱县");
        codeKeyMap.put("231281","安达");
        codeKeyMap.put("231282","肇东");
        codeKeyMap.put("231283","海伦");
        codeKeyMap.put("232700","大兴安岭地区");
        codeKeyMap.put("232701","漠河");
        codeKeyMap.put("232721","呼玛县");
        codeKeyMap.put("232722","塔河县");
        codeKeyMap.put("232761","加格达奇区");
        codeKeyMap.put("232762","松岭区");
        codeKeyMap.put("232763","新林区");
        codeKeyMap.put("232764","呼中区");
        codeKeyMap.put("310000","上海");
        codeKeyMap.put("310100","上海");
        codeKeyMap.put("310101","黄浦区");
        codeKeyMap.put("310104","徐汇区");
        codeKeyMap.put("310105","长宁区");
        codeKeyMap.put("310106","静安区");
        codeKeyMap.put("310107","普陀区");
        codeKeyMap.put("310109","虹口区");
        codeKeyMap.put("310110","杨浦区");
        codeKeyMap.put("310112","闵行区");
        codeKeyMap.put("310113","宝山区");
        codeKeyMap.put("310114","嘉定区");
        codeKeyMap.put("310115","浦东新区");
        codeKeyMap.put("310116","金山区");
        codeKeyMap.put("310117","松江区");
        codeKeyMap.put("310118","青浦区");
        codeKeyMap.put("310120","奉贤区");
        codeKeyMap.put("310151","崇明区");
        codeKeyMap.put("320000","江苏");
        codeKeyMap.put("320100","南京");
        codeKeyMap.put("320102","玄武区");
        codeKeyMap.put("320104","秦淮区");
        codeKeyMap.put("320105","建邺区");
        codeKeyMap.put("320106","鼓楼区");
        codeKeyMap.put("320111","浦口区");
        codeKeyMap.put("320113","栖霞区");
        codeKeyMap.put("320114","雨花台区");
        codeKeyMap.put("320115","江宁区");
        codeKeyMap.put("320116","六合区");
        codeKeyMap.put("320117","溧水区");
        codeKeyMap.put("320118","高淳区");
        codeKeyMap.put("320200","无锡");
        codeKeyMap.put("320205","锡山区");
        codeKeyMap.put("320206","惠山区");
        codeKeyMap.put("320211","滨湖区");
        codeKeyMap.put("320213","梁溪区");
        codeKeyMap.put("320214","新吴区");
        codeKeyMap.put("320281","江阴");
        codeKeyMap.put("320282","宜兴");
        codeKeyMap.put("320300","徐州");
        codeKeyMap.put("320302","鼓楼区");
        codeKeyMap.put("320303","云龙区");
        codeKeyMap.put("320305","贾汪区");
        codeKeyMap.put("320311","泉山区");
        codeKeyMap.put("320312","铜山区");
        codeKeyMap.put("320321","丰县");
        codeKeyMap.put("320322","沛县");
        codeKeyMap.put("320324","睢宁县");
        codeKeyMap.put("320371","徐州经济技术开发区");
        codeKeyMap.put("320381","新沂");
        codeKeyMap.put("320382","邳州");
        codeKeyMap.put("320400","常州");
        codeKeyMap.put("320402","天宁区");
        codeKeyMap.put("320404","钟楼区");
        codeKeyMap.put("320411","新北区");
        codeKeyMap.put("320412","武进区");
        codeKeyMap.put("320413","金坛区");
        codeKeyMap.put("320481","溧阳");
        codeKeyMap.put("320500","苏州");
        codeKeyMap.put("320505","虎丘区");
        codeKeyMap.put("320506","吴中区");
        codeKeyMap.put("320507","相城区");
        codeKeyMap.put("320508","姑苏区");
        codeKeyMap.put("320509","吴江区");
        codeKeyMap.put("320571","苏州工业园区");
        codeKeyMap.put("320581","常熟");
        codeKeyMap.put("320582","张家港");
        codeKeyMap.put("320583","昆山");
        codeKeyMap.put("320585","太仓");
        codeKeyMap.put("320600","南通");
        codeKeyMap.put("320602","崇川区");
        codeKeyMap.put("320611","港闸区");
        codeKeyMap.put("320612","通州区");
        codeKeyMap.put("320623","如东县");
        codeKeyMap.put("320671","南通经济技术开发区");
        codeKeyMap.put("320681","启东");
        codeKeyMap.put("320682","如皋");
        codeKeyMap.put("320684","海门");
        codeKeyMap.put("320685","海安");
        codeKeyMap.put("320700","连云港");
        codeKeyMap.put("320703","连云区");
        codeKeyMap.put("320706","海州区");
        codeKeyMap.put("320707","赣榆区");
        codeKeyMap.put("320722","东海县");
        codeKeyMap.put("320723","灌云县");
        codeKeyMap.put("320724","灌南县");
        codeKeyMap.put("320771","连云港经济技术开发区");
        codeKeyMap.put("320772","连云港高新技术产业开发区");
        codeKeyMap.put("320800","淮安");
        codeKeyMap.put("320803","淮安区");
        codeKeyMap.put("320804","淮阴区");
        codeKeyMap.put("320812","清江浦区");
        codeKeyMap.put("320813","洪泽区");
        codeKeyMap.put("320826","涟水县");
        codeKeyMap.put("320830","盱眙县");
        codeKeyMap.put("320831","金湖县");
        codeKeyMap.put("320871","淮安经济技术开发区");
        codeKeyMap.put("320900","盐城");
        codeKeyMap.put("320902","亭湖区");
        codeKeyMap.put("320903","盐都区");
        codeKeyMap.put("320904","大丰区");
        codeKeyMap.put("320921","响水县");
        codeKeyMap.put("320922","滨海县");
        codeKeyMap.put("320923","阜宁县");
        codeKeyMap.put("320924","射阳县");
        codeKeyMap.put("320925","建湖县");
        codeKeyMap.put("320971","盐城经济技术开发区");
        codeKeyMap.put("320981","东台");
        codeKeyMap.put("321000","扬州");
        codeKeyMap.put("321002","广陵区");
        codeKeyMap.put("321003","邗江区");
        codeKeyMap.put("321012","江都区");
        codeKeyMap.put("321023","宝应县");
        codeKeyMap.put("321071","扬州经济技术开发区");
        codeKeyMap.put("321081","仪征");
        codeKeyMap.put("321084","高邮");
        codeKeyMap.put("321100","镇江");
        codeKeyMap.put("321102","京口区");
        codeKeyMap.put("321111","润州区");
        codeKeyMap.put("321112","丹徒区");
        codeKeyMap.put("321171","镇江新区");
        codeKeyMap.put("321181","丹阳");
        codeKeyMap.put("321182","扬中");
        codeKeyMap.put("321183","句容");
        codeKeyMap.put("321200","泰州");
        codeKeyMap.put("321202","海陵区");
        codeKeyMap.put("321203","高港区");
        codeKeyMap.put("321204","姜堰区");
        codeKeyMap.put("321271","泰州医药高新技术产业开发区");
        codeKeyMap.put("321281","兴化");
        codeKeyMap.put("321282","靖江");
        codeKeyMap.put("321283","泰兴");
        codeKeyMap.put("321300","宿迁");
        codeKeyMap.put("321302","宿城区");
        codeKeyMap.put("321311","宿豫区");
        codeKeyMap.put("321322","沭阳县");
        codeKeyMap.put("321323","泗阳县");
        codeKeyMap.put("321324","泗洪县");
        codeKeyMap.put("321371","宿迁经济技术开发区");
        codeKeyMap.put("330000","浙江");
        codeKeyMap.put("330100","杭州");
        codeKeyMap.put("330102","上城区");
        codeKeyMap.put("330103","下城区");
        codeKeyMap.put("330104","江干区");
        codeKeyMap.put("330105","拱墅区");
        codeKeyMap.put("330106","西湖区");
        codeKeyMap.put("330108","滨江区");
        codeKeyMap.put("330109","萧山区");
        codeKeyMap.put("330110","余杭区");
        codeKeyMap.put("330111","富阳区");
        codeKeyMap.put("330112","临安区");
        codeKeyMap.put("330122","桐庐县");
        codeKeyMap.put("330127","淳安县");
        codeKeyMap.put("330182","建德");
        codeKeyMap.put("330200","宁波");
        codeKeyMap.put("330203","海曙区");
        codeKeyMap.put("330205","江北区");
        codeKeyMap.put("330206","北仑区");
        codeKeyMap.put("330211","镇海区");
        codeKeyMap.put("330212","鄞州区");
        codeKeyMap.put("330213","奉化区");
        codeKeyMap.put("330225","象山县");
        codeKeyMap.put("330226","宁海县");
        codeKeyMap.put("330281","余姚");
        codeKeyMap.put("330282","慈溪");
        codeKeyMap.put("330300","温州");
        codeKeyMap.put("330302","鹿城区");
        codeKeyMap.put("330303","龙湾区");
        codeKeyMap.put("330304","瓯海区");
        codeKeyMap.put("330305","洞头区");
        codeKeyMap.put("330324","永嘉县");
        codeKeyMap.put("330326","平阳县");
        codeKeyMap.put("330327","苍南县");
        codeKeyMap.put("330328","文成县");
        codeKeyMap.put("330329","泰顺县");
        codeKeyMap.put("330371","温州经济技术开发区");
        codeKeyMap.put("330381","瑞安");
        codeKeyMap.put("330382","乐清");
        codeKeyMap.put("330400","嘉兴");
        codeKeyMap.put("330402","南湖区");
        codeKeyMap.put("330411","秀洲区");
        codeKeyMap.put("330421","嘉善县");
        codeKeyMap.put("330424","海盐县");
        codeKeyMap.put("330481","海宁");
        codeKeyMap.put("330482","平湖");
        codeKeyMap.put("330483","桐乡");
        codeKeyMap.put("330500","湖州");
        codeKeyMap.put("330502","吴兴区");
        codeKeyMap.put("330503","南浔区");
        codeKeyMap.put("330521","德清县");
        codeKeyMap.put("330522","长兴县");
        codeKeyMap.put("330523","安吉县");
        codeKeyMap.put("330600","绍兴");
        codeKeyMap.put("330602","越城区");
        codeKeyMap.put("330603","柯桥区");
        codeKeyMap.put("330604","上虞区");
        codeKeyMap.put("330624","新昌县");
        codeKeyMap.put("330681","诸暨");
        codeKeyMap.put("330683","嵊州");
        codeKeyMap.put("330700","金华");
        codeKeyMap.put("330702","婺城区");
        codeKeyMap.put("330703","金东区");
        codeKeyMap.put("330723","武义县");
        codeKeyMap.put("330726","浦江县");
        codeKeyMap.put("330727","磐安县");
        codeKeyMap.put("330781","兰溪");
        codeKeyMap.put("330782","义乌");
        codeKeyMap.put("330783","东阳");
        codeKeyMap.put("330784","永康");
        codeKeyMap.put("330800","衢州");
        codeKeyMap.put("330802","柯城区");
        codeKeyMap.put("330803","衢江区");
        codeKeyMap.put("330822","常山县");
        codeKeyMap.put("330824","开化县");
        codeKeyMap.put("330825","龙游县");
        codeKeyMap.put("330881","江山");
        codeKeyMap.put("330900","舟山");
        codeKeyMap.put("330902","定海区");
        codeKeyMap.put("330903","普陀区");
        codeKeyMap.put("330921","岱山县");
        codeKeyMap.put("330922","嵊泗县");
        codeKeyMap.put("331000","台州");
        codeKeyMap.put("331002","椒江区");
        codeKeyMap.put("331003","黄岩区");
        codeKeyMap.put("331004","路桥区");
        codeKeyMap.put("331022","三门县");
        codeKeyMap.put("331023","天台县");
        codeKeyMap.put("331024","仙居县");
        codeKeyMap.put("331081","温岭");
        codeKeyMap.put("331082","临海");
        codeKeyMap.put("331083","玉环");
        codeKeyMap.put("331100","丽水");
        codeKeyMap.put("331102","莲都区");
        codeKeyMap.put("331121","青田县");
        codeKeyMap.put("331122","缙云县");
        codeKeyMap.put("331123","遂昌县");
        codeKeyMap.put("331124","松阳县");
        codeKeyMap.put("331125","云和县");
        codeKeyMap.put("331126","庆元县");
        codeKeyMap.put("331127","景宁畲族自治县");
        codeKeyMap.put("331181","龙泉");
        codeKeyMap.put("340000","安徽");
        codeKeyMap.put("340100","合肥");
        codeKeyMap.put("340102","瑶海区");
        codeKeyMap.put("340103","庐阳区");
        codeKeyMap.put("340104","蜀山区");
        codeKeyMap.put("340111","包河区");
        codeKeyMap.put("340121","长丰县");
        codeKeyMap.put("340122","肥东县");
        codeKeyMap.put("340123","肥西县");
        codeKeyMap.put("340124","庐江县");
        codeKeyMap.put("340171","合肥高新技术产业开发区");
        codeKeyMap.put("340172","合肥经济技术开发区");
        codeKeyMap.put("340173","合肥新站高新技术产业开发区");
        codeKeyMap.put("340181","巢湖");
        codeKeyMap.put("340200","芜湖");
        codeKeyMap.put("340202","镜湖区");
        codeKeyMap.put("340203","弋江区");
        codeKeyMap.put("340207","鸠江区");
        codeKeyMap.put("340208","三山区");
        codeKeyMap.put("340221","芜湖县");
        codeKeyMap.put("340222","繁昌县");
        codeKeyMap.put("340223","南陵县");
        codeKeyMap.put("340225","无为县");
        codeKeyMap.put("340271","芜湖经济技术开发区");
        codeKeyMap.put("340272","安徽芜湖长江大桥经济开发区");
        codeKeyMap.put("340300","蚌埠");
        codeKeyMap.put("340302","龙子湖区");
        codeKeyMap.put("340303","蚌山区");
        codeKeyMap.put("340304","禹会区");
        codeKeyMap.put("340311","淮上区");
        codeKeyMap.put("340321","怀远县");
        codeKeyMap.put("340322","五河县");
        codeKeyMap.put("340323","固镇县");
        codeKeyMap.put("340371","蚌埠高新技术开发区");
        codeKeyMap.put("340372","蚌埠经济开发区");
        codeKeyMap.put("340400","淮南");
        codeKeyMap.put("340402","大通区");
        codeKeyMap.put("340403","田家庵区");
        codeKeyMap.put("340404","谢家集区");
        codeKeyMap.put("340405","八公山区");
        codeKeyMap.put("340406","潘集区");
        codeKeyMap.put("340421","凤台县");
        codeKeyMap.put("340422","寿县");
        codeKeyMap.put("340500","马鞍山");
        codeKeyMap.put("340503","花山区");
        codeKeyMap.put("340504","雨山区");
        codeKeyMap.put("340506","博望区");
        codeKeyMap.put("340521","当涂县");
        codeKeyMap.put("340522","含山县");
        codeKeyMap.put("340523","和县");
        codeKeyMap.put("340600","淮北");
        codeKeyMap.put("340602","杜集区");
        codeKeyMap.put("340603","相山区");
        codeKeyMap.put("340604","烈山区");
        codeKeyMap.put("340621","濉溪县");
        codeKeyMap.put("340700","铜陵");
        codeKeyMap.put("340705","铜官区");
        codeKeyMap.put("340706","义安区");
        codeKeyMap.put("340711","郊区");
        codeKeyMap.put("340722","枞阳县");
        codeKeyMap.put("340800","安庆");
        codeKeyMap.put("340802","迎江区");
        codeKeyMap.put("340803","大观区");
        codeKeyMap.put("340811","宜秀区");
        codeKeyMap.put("340822","怀宁县");
        codeKeyMap.put("340825","太湖县");
        codeKeyMap.put("340826","宿松县");
        codeKeyMap.put("340827","望江县");
        codeKeyMap.put("340828","岳西县");
        codeKeyMap.put("340871","安徽安庆经济开发区");
        codeKeyMap.put("340881","桐城");
        codeKeyMap.put("340882","潜山");
        codeKeyMap.put("341000","黄山");
        codeKeyMap.put("341002","屯溪区");
        codeKeyMap.put("341003","黄山区");
        codeKeyMap.put("341004","徽州区");
        codeKeyMap.put("341021","歙县");
        codeKeyMap.put("341022","休宁县");
        codeKeyMap.put("341023","黟县");
        codeKeyMap.put("341024","祁门县");
        codeKeyMap.put("341100","滁州");
        codeKeyMap.put("341102","琅琊区");
        codeKeyMap.put("341103","南谯区");
        codeKeyMap.put("341122","来安县");
        codeKeyMap.put("341124","全椒县");
        codeKeyMap.put("341125","定远县");
        codeKeyMap.put("341126","凤阳县");
        codeKeyMap.put("341171","苏滁现代产业园");
        codeKeyMap.put("341172","滁州经济技术开发区");
        codeKeyMap.put("341181","天长");
        codeKeyMap.put("341182","明光");
        codeKeyMap.put("341200","阜阳");
        codeKeyMap.put("341202","颍州区");
        codeKeyMap.put("341203","颍东区");
        codeKeyMap.put("341204","颍泉区");
        codeKeyMap.put("341221","临泉县");
        codeKeyMap.put("341222","太和县");
        codeKeyMap.put("341225","阜南县");
        codeKeyMap.put("341226","颍上县");
        codeKeyMap.put("341271","阜阳合肥现代产业园区");
        codeKeyMap.put("341272","阜阳经济技术开发区");
        codeKeyMap.put("341282","界首");
        codeKeyMap.put("341300","宿州");
        codeKeyMap.put("341302","埇桥区");
        codeKeyMap.put("341321","砀山县");
        codeKeyMap.put("341322","萧县");
        codeKeyMap.put("341323","灵璧县");
        codeKeyMap.put("341324","泗县");
        codeKeyMap.put("341371","宿州马鞍山现代产业园区");
        codeKeyMap.put("341372","宿州经济技术开发区");
        codeKeyMap.put("341500","六安");
        codeKeyMap.put("341502","金安区");
        codeKeyMap.put("341503","裕安区");
        codeKeyMap.put("341504","叶集区");
        codeKeyMap.put("341522","霍邱县");
        codeKeyMap.put("341523","舒城县");
        codeKeyMap.put("341524","金寨县");
        codeKeyMap.put("341525","霍山县");
        codeKeyMap.put("341600","亳州");
        codeKeyMap.put("341602","谯城区");
        codeKeyMap.put("341621","涡阳县");
        codeKeyMap.put("341622","蒙城县");
        codeKeyMap.put("341623","利辛县");
        codeKeyMap.put("341700","池州");
        codeKeyMap.put("341702","贵池区");
        codeKeyMap.put("341721","东至县");
        codeKeyMap.put("341722","石台县");
        codeKeyMap.put("341723","青阳县");
        codeKeyMap.put("341800","宣城");
        codeKeyMap.put("341802","宣州区");
        codeKeyMap.put("341821","郎溪县");
        codeKeyMap.put("341822","广德县");
        codeKeyMap.put("341823","泾县");
        codeKeyMap.put("341824","绩溪县");
        codeKeyMap.put("341825","旌德县");
        codeKeyMap.put("341871","宣城经济开发区");
        codeKeyMap.put("341881","宁国");
        codeKeyMap.put("350000","福建");
        codeKeyMap.put("350100","福州");
        codeKeyMap.put("350102","鼓楼区");
        codeKeyMap.put("350103","台江区");
        codeKeyMap.put("350104","仓山区");
        codeKeyMap.put("350105","马尾区");
        codeKeyMap.put("350111","晋安区");
        codeKeyMap.put("350112","长乐区");
        codeKeyMap.put("350121","闽侯县");
        codeKeyMap.put("350122","连江县");
        codeKeyMap.put("350123","罗源县");
        codeKeyMap.put("350124","闽清县");
        codeKeyMap.put("350125","永泰县");
        codeKeyMap.put("350128","平潭县");
        codeKeyMap.put("350181","福清");
        codeKeyMap.put("350200","厦门");
        codeKeyMap.put("350203","思明区");
        codeKeyMap.put("350205","海沧区");
        codeKeyMap.put("350206","湖里区");
        codeKeyMap.put("350211","集美区");
        codeKeyMap.put("350212","同安区");
        codeKeyMap.put("350213","翔安区");
        codeKeyMap.put("350300","莆田");
        codeKeyMap.put("350302","城厢区");
        codeKeyMap.put("350303","涵江区");
        codeKeyMap.put("350304","荔城区");
        codeKeyMap.put("350305","秀屿区");
        codeKeyMap.put("350322","仙游县");
        codeKeyMap.put("350400","三明");
        codeKeyMap.put("350402","梅列区");
        codeKeyMap.put("350403","三元区");
        codeKeyMap.put("350421","明溪县");
        codeKeyMap.put("350423","清流县");
        codeKeyMap.put("350424","宁化县");
        codeKeyMap.put("350425","大田县");
        codeKeyMap.put("350426","尤溪县");
        codeKeyMap.put("350427","沙县");
        codeKeyMap.put("350428","将乐县");
        codeKeyMap.put("350429","泰宁县");
        codeKeyMap.put("350430","建宁县");
        codeKeyMap.put("350481","永安");
        codeKeyMap.put("350500","泉州");
        codeKeyMap.put("350502","鲤城区");
        codeKeyMap.put("350503","丰泽区");
        codeKeyMap.put("350504","洛江区");
        codeKeyMap.put("350505","泉港区");
        codeKeyMap.put("350521","惠安县");
        codeKeyMap.put("350524","安溪县");
        codeKeyMap.put("350525","永春县");
        codeKeyMap.put("350526","德化县");
        codeKeyMap.put("350527","金门县");
        codeKeyMap.put("350581","石狮");
        codeKeyMap.put("350582","晋江");
        codeKeyMap.put("350583","南安");
        codeKeyMap.put("350600","漳州");
        codeKeyMap.put("350602","芗城区");
        codeKeyMap.put("350603","龙文区");
        codeKeyMap.put("350622","云霄县");
        codeKeyMap.put("350623","漳浦县");
        codeKeyMap.put("350624","诏安县");
        codeKeyMap.put("350625","长泰县");
        codeKeyMap.put("350626","东山县");
        codeKeyMap.put("350627","南靖县");
        codeKeyMap.put("350628","平和县");
        codeKeyMap.put("350629","华安县");
        codeKeyMap.put("350681","龙海");
        codeKeyMap.put("350700","南平");
        codeKeyMap.put("350702","延平区");
        codeKeyMap.put("350703","建阳区");
        codeKeyMap.put("350721","顺昌县");
        codeKeyMap.put("350722","浦城县");
        codeKeyMap.put("350723","光泽县");
        codeKeyMap.put("350724","松溪县");
        codeKeyMap.put("350725","政和县");
        codeKeyMap.put("350781","邵武");
        codeKeyMap.put("350782","武夷山");
        codeKeyMap.put("350783","建瓯");
        codeKeyMap.put("350800","龙岩");
        codeKeyMap.put("350802","新罗区");
        codeKeyMap.put("350803","永定区");
        codeKeyMap.put("350821","长汀县");
        codeKeyMap.put("350823","上杭县");
        codeKeyMap.put("350824","武平县");
        codeKeyMap.put("350825","连城县");
        codeKeyMap.put("350881","漳平");
        codeKeyMap.put("350900","宁德");
        codeKeyMap.put("350902","蕉城区");
        codeKeyMap.put("350921","霞浦县");
        codeKeyMap.put("350922","古田县");
        codeKeyMap.put("350923","屏南县");
        codeKeyMap.put("350924","寿宁县");
        codeKeyMap.put("350925","周宁县");
        codeKeyMap.put("350926","柘荣县");
        codeKeyMap.put("350981","福安");
        codeKeyMap.put("350982","福鼎");
        codeKeyMap.put("360000","江西");
        codeKeyMap.put("360100","南昌");
        codeKeyMap.put("360102","东湖区");
        codeKeyMap.put("360103","西湖区");
        codeKeyMap.put("360104","青云谱区");
        codeKeyMap.put("360105","湾里区");
        codeKeyMap.put("360111","青山湖区");
        codeKeyMap.put("360112","新建区");
        codeKeyMap.put("360121","南昌县");
        codeKeyMap.put("360123","安义县");
        codeKeyMap.put("360124","进贤县");
        codeKeyMap.put("360200","景德镇");
        codeKeyMap.put("360202","昌江区");
        codeKeyMap.put("360203","珠山区");
        codeKeyMap.put("360222","浮梁县");
        codeKeyMap.put("360281","乐平");
        codeKeyMap.put("360300","萍乡");
        codeKeyMap.put("360302","安源区");
        codeKeyMap.put("360313","湘东区");
        codeKeyMap.put("360321","莲花县");
        codeKeyMap.put("360322","上栗县");
        codeKeyMap.put("360323","芦溪县");
        codeKeyMap.put("360400","九江");
        codeKeyMap.put("360402","濂溪区");
        codeKeyMap.put("360403","浔阳区");
        codeKeyMap.put("360404","柴桑区");
        codeKeyMap.put("360423","武宁县");
        codeKeyMap.put("360424","修水县");
        codeKeyMap.put("360425","永修县");
        codeKeyMap.put("360426","德安县");
        codeKeyMap.put("360428","都昌县");
        codeKeyMap.put("360429","湖口县");
        codeKeyMap.put("360430","彭泽县");
        codeKeyMap.put("360481","瑞昌");
        codeKeyMap.put("360482","共青城");
        codeKeyMap.put("360483","庐山");
        codeKeyMap.put("360500","新余");
        codeKeyMap.put("360502","渝水区");
        codeKeyMap.put("360521","分宜县");
        codeKeyMap.put("360600","鹰潭");
        codeKeyMap.put("360602","月湖区");
        codeKeyMap.put("360603","余江区");
        codeKeyMap.put("360681","贵溪");
        codeKeyMap.put("360700","赣州");
        codeKeyMap.put("360702","章贡区");
        codeKeyMap.put("360703","南康区");
        codeKeyMap.put("360704","赣县区");
        codeKeyMap.put("360722","信丰县");
        codeKeyMap.put("360723","大余县");
        codeKeyMap.put("360724","上犹县");
        codeKeyMap.put("360725","崇义县");
        codeKeyMap.put("360726","安远县");
        codeKeyMap.put("360727","龙南县");
        codeKeyMap.put("360728","定南县");
        codeKeyMap.put("360729","全南县");
        codeKeyMap.put("360730","宁都县");
        codeKeyMap.put("360731","于都县");
        codeKeyMap.put("360732","兴国县");
        codeKeyMap.put("360733","会昌县");
        codeKeyMap.put("360734","寻乌县");
        codeKeyMap.put("360735","石城县");
        codeKeyMap.put("360781","瑞金");
        codeKeyMap.put("360800","吉安");
        codeKeyMap.put("360802","吉州区");
        codeKeyMap.put("360803","青原区");
        codeKeyMap.put("360821","吉安县");
        codeKeyMap.put("360822","吉水县");
        codeKeyMap.put("360823","峡江县");
        codeKeyMap.put("360824","新干县");
        codeKeyMap.put("360825","永丰县");
        codeKeyMap.put("360826","泰和县");
        codeKeyMap.put("360827","遂川县");
        codeKeyMap.put("360828","万安县");
        codeKeyMap.put("360829","安福县");
        codeKeyMap.put("360830","永新县");
        codeKeyMap.put("360881","井冈山");
        codeKeyMap.put("360900","宜春");
        codeKeyMap.put("360902","袁州区");
        codeKeyMap.put("360921","奉新县");
        codeKeyMap.put("360922","万载县");
        codeKeyMap.put("360923","上高县");
        codeKeyMap.put("360924","宜丰县");
        codeKeyMap.put("360925","靖安县");
        codeKeyMap.put("360926","铜鼓县");
        codeKeyMap.put("360981","丰城");
        codeKeyMap.put("360982","樟树");
        codeKeyMap.put("360983","高安");
        codeKeyMap.put("361000","抚州");
        codeKeyMap.put("361002","临川区");
        codeKeyMap.put("361003","东乡区");
        codeKeyMap.put("361021","南城县");
        codeKeyMap.put("361022","黎川县");
        codeKeyMap.put("361023","南丰县");
        codeKeyMap.put("361024","崇仁县");
        codeKeyMap.put("361025","乐安县");
        codeKeyMap.put("361026","宜黄县");
        codeKeyMap.put("361027","金溪县");
        codeKeyMap.put("361028","资溪县");
        codeKeyMap.put("361030","广昌县");
        codeKeyMap.put("361100","上饶");
        codeKeyMap.put("361102","信州区");
        codeKeyMap.put("361103","广丰区");
        codeKeyMap.put("361121","上饶县");
        codeKeyMap.put("361123","玉山县");
        codeKeyMap.put("361124","铅山县");
        codeKeyMap.put("361125","横峰县");
        codeKeyMap.put("361126","弋阳县");
        codeKeyMap.put("361127","余干县");
        codeKeyMap.put("361128","鄱阳县");
        codeKeyMap.put("361129","万年县");
        codeKeyMap.put("361130","婺源县");
        codeKeyMap.put("361181","德兴");
        codeKeyMap.put("370000","山东");
        codeKeyMap.put("370100","济南");
        codeKeyMap.put("370102","历下区");
        codeKeyMap.put("370103","中区");
        codeKeyMap.put("370104","槐荫区");
        codeKeyMap.put("370105","天桥区");
        codeKeyMap.put("370112","历城区");
        codeKeyMap.put("370113","长清区");
        codeKeyMap.put("370114","章丘区");
        codeKeyMap.put("370115","济阳区");
        codeKeyMap.put("370124","平阴县");
        codeKeyMap.put("370126","商河县");
        codeKeyMap.put("370171","济南高新技术产业开发区");
        codeKeyMap.put("370200","青岛");
        codeKeyMap.put("370202","南区");
        codeKeyMap.put("370203","北区");
        codeKeyMap.put("370211","黄岛区");
        codeKeyMap.put("370212","崂山区");
        codeKeyMap.put("370213","李沧区");
        codeKeyMap.put("370214","城阳区");
        codeKeyMap.put("370215","即墨区");
        codeKeyMap.put("370271","青岛高新技术产业开发区");
        codeKeyMap.put("370281","胶州");
        codeKeyMap.put("370283","平度");
        codeKeyMap.put("370285","莱西");
        codeKeyMap.put("370300","淄博");
        codeKeyMap.put("370302","淄川区");
        codeKeyMap.put("370303","张店区");
        codeKeyMap.put("370304","博山区");
        codeKeyMap.put("370305","临淄区");
        codeKeyMap.put("370306","周村区");
        codeKeyMap.put("370321","桓台县");
        codeKeyMap.put("370322","高青县");
        codeKeyMap.put("370323","沂源县");
        codeKeyMap.put("370400","枣庄");
        codeKeyMap.put("370402","中区");
        codeKeyMap.put("370403","薛城区");
        codeKeyMap.put("370404","峄城区");
        codeKeyMap.put("370405","台儿庄区");
        codeKeyMap.put("370406","山亭区");
        codeKeyMap.put("370481","滕州");
        codeKeyMap.put("370500","东营");
        codeKeyMap.put("370502","东营区");
        codeKeyMap.put("370503","河口区");
        codeKeyMap.put("370505","垦利区");
        codeKeyMap.put("370522","利津县");
        codeKeyMap.put("370523","广饶县");
        codeKeyMap.put("370571","东营经济技术开发区");
        codeKeyMap.put("370572","东营港经济开发区");
        codeKeyMap.put("370600","烟台");
        codeKeyMap.put("370602","芝罘区");
        codeKeyMap.put("370611","福山区");
        codeKeyMap.put("370612","牟平区");
        codeKeyMap.put("370613","莱山区");
        codeKeyMap.put("370634","长岛县");
        codeKeyMap.put("370671","烟台高新技术产业开发区");
        codeKeyMap.put("370672","烟台经济技术开发区");
        codeKeyMap.put("370681","龙口");
        codeKeyMap.put("370682","莱阳");
        codeKeyMap.put("370683","莱州");
        codeKeyMap.put("370684","蓬莱");
        codeKeyMap.put("370685","招远");
        codeKeyMap.put("370686","栖霞");
        codeKeyMap.put("370687","海阳");
        codeKeyMap.put("370700","潍坊");
        codeKeyMap.put("370702","潍城区");
        codeKeyMap.put("370703","寒亭区");
        codeKeyMap.put("370704","坊子区");
        codeKeyMap.put("370705","奎文区");
        codeKeyMap.put("370724","临朐县");
        codeKeyMap.put("370725","昌乐县");
        codeKeyMap.put("370772","潍坊滨海经济技术开发区");
        codeKeyMap.put("370781","青州");
        codeKeyMap.put("370782","诸城");
        codeKeyMap.put("370783","寿光");
        codeKeyMap.put("370784","安丘");
        codeKeyMap.put("370785","高密");
        codeKeyMap.put("370786","昌邑");
        codeKeyMap.put("370800","济宁");
        codeKeyMap.put("370811","任城区");
        codeKeyMap.put("370812","兖州区");
        codeKeyMap.put("370826","微山县");
        codeKeyMap.put("370827","鱼台县");
        codeKeyMap.put("370828","金乡县");
        codeKeyMap.put("370829","嘉祥县");
        codeKeyMap.put("370830","汶上县");
        codeKeyMap.put("370831","泗水县");
        codeKeyMap.put("370832","梁山县");
        codeKeyMap.put("370871","济宁高新技术产业开发区");
        codeKeyMap.put("370881","曲阜");
        codeKeyMap.put("370883","邹城");
        codeKeyMap.put("370900","泰安");
        codeKeyMap.put("370902","泰山区");
        codeKeyMap.put("370911","岱岳区");
        codeKeyMap.put("370921","宁阳县");
        codeKeyMap.put("370923","东平县");
        codeKeyMap.put("370982","新泰");
        codeKeyMap.put("370983","肥城");
        codeKeyMap.put("371000","威海");
        codeKeyMap.put("371002","环翠区");
        codeKeyMap.put("371003","文登区");
        codeKeyMap.put("371071","威海火炬高技术产业开发区");
        codeKeyMap.put("371072","威海经济技术开发区");
        codeKeyMap.put("371073","威海临港经济技术开发区");
        codeKeyMap.put("371082","荣成");
        codeKeyMap.put("371083","乳山");
        codeKeyMap.put("371100","日照");
        codeKeyMap.put("371102","东港区");
        codeKeyMap.put("371103","岚山区");
        codeKeyMap.put("371121","五莲县");
        codeKeyMap.put("371122","莒县");
        codeKeyMap.put("371171","日照经济技术开发区");
        codeKeyMap.put("371200","莱芜");
        codeKeyMap.put("371202","莱城区");
        codeKeyMap.put("371203","钢城区");
        codeKeyMap.put("371300","临沂");
        codeKeyMap.put("371302","兰山区");
        codeKeyMap.put("371311","罗庄区");
        codeKeyMap.put("371312","河东区");
        codeKeyMap.put("371321","沂南县");
        codeKeyMap.put("371322","郯城县");
        codeKeyMap.put("371323","沂水县");
        codeKeyMap.put("371324","兰陵县");
        codeKeyMap.put("371325","费县");
        codeKeyMap.put("371326","平邑县");
        codeKeyMap.put("371327","莒南县");
        codeKeyMap.put("371328","蒙阴县");
        codeKeyMap.put("371329","临沭县");
        codeKeyMap.put("371371","临沂高新技术产业开发区");
        codeKeyMap.put("371372","临沂经济技术开发区");
        codeKeyMap.put("371373","临沂临港经济开发区");
        codeKeyMap.put("371400","德州");
        codeKeyMap.put("371402","德城区");
        codeKeyMap.put("371403","陵城区");
        codeKeyMap.put("371422","宁津县");
        codeKeyMap.put("371423","庆云县");
        codeKeyMap.put("371424","临邑县");
        codeKeyMap.put("371425","齐河县");
        codeKeyMap.put("371426","平原县");
        codeKeyMap.put("371427","夏津县");
        codeKeyMap.put("371428","武城县");
        codeKeyMap.put("371471","德州经济技术开发区");
        codeKeyMap.put("371472","德州运河经济开发区");
        codeKeyMap.put("371481","乐陵");
        codeKeyMap.put("371482","禹城");
        codeKeyMap.put("371500","聊城");
        codeKeyMap.put("371502","东昌府区");
        codeKeyMap.put("371521","阳谷县");
        codeKeyMap.put("371522","莘县");
        codeKeyMap.put("371523","茌平县");
        codeKeyMap.put("371524","东阿县");
        codeKeyMap.put("371525","冠县");
        codeKeyMap.put("371526","高唐县");
        codeKeyMap.put("371581","临清");
        codeKeyMap.put("371600","滨州");
        codeKeyMap.put("371602","滨城区");
        codeKeyMap.put("371603","沾化区");
        codeKeyMap.put("371621","惠民县");
        codeKeyMap.put("371622","阳信县");
        codeKeyMap.put("371623","无棣县");
        codeKeyMap.put("371625","博兴县");
        codeKeyMap.put("371681","邹平");
        codeKeyMap.put("371700","菏泽");
        codeKeyMap.put("371702","牡丹区");
        codeKeyMap.put("371703","定陶区");
        codeKeyMap.put("371721","曹县");
        codeKeyMap.put("371722","单县");
        codeKeyMap.put("371723","成武县");
        codeKeyMap.put("371724","巨野县");
        codeKeyMap.put("371725","郓城县");
        codeKeyMap.put("371726","鄄城县");
        codeKeyMap.put("371728","东明县");
        codeKeyMap.put("371771","菏泽经济技术开发区");
        codeKeyMap.put("371772","菏泽高新技术开发区");
        codeKeyMap.put("410000","河南");
        codeKeyMap.put("410100","郑州");
        codeKeyMap.put("410102","中原区");
        codeKeyMap.put("410103","二七区");
        codeKeyMap.put("410104","管城回族区");
        codeKeyMap.put("410105","金水区");
        codeKeyMap.put("410106","上街区");
        codeKeyMap.put("410108","惠济区");
        codeKeyMap.put("410122","中牟县");
        codeKeyMap.put("410171","郑州经济技术开发区");
        codeKeyMap.put("410172","郑州高新技术产业开发区");
        codeKeyMap.put("410173","郑州航空港经济综合实验区");
        codeKeyMap.put("410181","巩义");
        codeKeyMap.put("410182","荥阳");
        codeKeyMap.put("410183","新密");
        codeKeyMap.put("410184","新郑");
        codeKeyMap.put("410185","登封");
        codeKeyMap.put("410200","开封");
        codeKeyMap.put("410202","龙亭区");
        codeKeyMap.put("410203","顺河回族区");
        codeKeyMap.put("410204","鼓楼区");
        codeKeyMap.put("410205","禹王台区");
        codeKeyMap.put("410212","祥符区");
        codeKeyMap.put("410221","杞县");
        codeKeyMap.put("410222","通许县");
        codeKeyMap.put("410223","尉氏县");
        codeKeyMap.put("410225","兰考县");
        codeKeyMap.put("410300","洛阳");
        codeKeyMap.put("410302","老城区");
        codeKeyMap.put("410303","西工区");
        codeKeyMap.put("410304","瀍河回族区");
        codeKeyMap.put("410305","涧西区");
        codeKeyMap.put("410306","吉利区");
        codeKeyMap.put("410311","洛龙区");
        codeKeyMap.put("410322","孟津县");
        codeKeyMap.put("410323","新安县");
        codeKeyMap.put("410324","栾川县");
        codeKeyMap.put("410325","嵩县");
        codeKeyMap.put("410326","汝阳县");
        codeKeyMap.put("410327","宜阳县");
        codeKeyMap.put("410328","洛宁县");
        codeKeyMap.put("410329","伊川县");
        codeKeyMap.put("410371","洛阳高新技术产业开发区");
        codeKeyMap.put("410381","偃师");
        codeKeyMap.put("410400","平顶山");
        codeKeyMap.put("410402","新华区");
        codeKeyMap.put("410403","卫东区");
        codeKeyMap.put("410404","石龙区");
        codeKeyMap.put("410411","湛河区");
        codeKeyMap.put("410421","宝丰县");
        codeKeyMap.put("410422","叶县");
        codeKeyMap.put("410423","鲁山县");
        codeKeyMap.put("410425","郏县");
        codeKeyMap.put("410471","平顶山高新技术产业开发区");
        codeKeyMap.put("410472","平顶山新城区");
        codeKeyMap.put("410481","舞钢");
        codeKeyMap.put("410482","汝州");
        codeKeyMap.put("410500","安阳");
        codeKeyMap.put("410502","文峰区");
        codeKeyMap.put("410503","北关区");
        codeKeyMap.put("410505","殷都区");
        codeKeyMap.put("410506","龙安区");
        codeKeyMap.put("410522","安阳县");
        codeKeyMap.put("410523","汤阴县");
        codeKeyMap.put("410526","滑县");
        codeKeyMap.put("410527","内黄县");
        codeKeyMap.put("410571","安阳高新技术产业开发区");
        codeKeyMap.put("410581","林州");
        codeKeyMap.put("410600","鹤壁");
        codeKeyMap.put("410602","鹤山区");
        codeKeyMap.put("410603","山城区");
        codeKeyMap.put("410611","淇滨区");
        codeKeyMap.put("410621","浚县");
        codeKeyMap.put("410622","淇县");
        codeKeyMap.put("410671","鹤壁经济技术开发区");
        codeKeyMap.put("410700","新乡");
        codeKeyMap.put("410702","红旗区");
        codeKeyMap.put("410703","卫滨区");
        codeKeyMap.put("410704","凤泉区");
        codeKeyMap.put("410711","牧野区");
        codeKeyMap.put("410721","新乡县");
        codeKeyMap.put("410724","获嘉县");
        codeKeyMap.put("410725","原阳县");
        codeKeyMap.put("410726","延津县");
        codeKeyMap.put("410727","封丘县");
        codeKeyMap.put("410728","长垣县");
        codeKeyMap.put("410771","新乡高新技术产业开发区");
        codeKeyMap.put("410772","新乡经济技术开发区");
        codeKeyMap.put("410773","新乡平原城乡一体化示范区");
        codeKeyMap.put("410781","卫辉");
        codeKeyMap.put("410782","辉县");
        codeKeyMap.put("410800","焦作");
        codeKeyMap.put("410802","解放区");
        codeKeyMap.put("410803","中站区");
        codeKeyMap.put("410804","马村区");
        codeKeyMap.put("410811","山阳区");
        codeKeyMap.put("410821","修武县");
        codeKeyMap.put("410822","博爱县");
        codeKeyMap.put("410823","武陟县");
        codeKeyMap.put("410825","温县");
        codeKeyMap.put("410871","焦作城乡一体化示范区");
        codeKeyMap.put("410882","沁阳");
        codeKeyMap.put("410883","孟州");
        codeKeyMap.put("410900","濮阳");
        codeKeyMap.put("410902","华龙区");
        codeKeyMap.put("410922","清丰县");
        codeKeyMap.put("410923","南乐县");
        codeKeyMap.put("410926","范县");
        codeKeyMap.put("410927","台前县");
        codeKeyMap.put("410928","濮阳县");
        codeKeyMap.put("410971","河南濮阳工业园区");
        codeKeyMap.put("410972","濮阳经济技术开发区");
        codeKeyMap.put("411000","许昌");
        codeKeyMap.put("411002","魏都区");
        codeKeyMap.put("411003","建安区");
        codeKeyMap.put("411024","鄢陵县");
        codeKeyMap.put("411025","襄城县");
        codeKeyMap.put("411071","许昌经济技术开发区");
        codeKeyMap.put("411081","禹州");
        codeKeyMap.put("411082","长葛");
        codeKeyMap.put("411100","漯河");
        codeKeyMap.put("411102","源汇区");
        codeKeyMap.put("411103","郾城区");
        codeKeyMap.put("411104","召陵区");
        codeKeyMap.put("411121","舞阳县");
        codeKeyMap.put("411122","临颍县");
        codeKeyMap.put("411171","漯河经济技术开发区");
        codeKeyMap.put("411200","三门峡");
        codeKeyMap.put("411202","湖滨区");
        codeKeyMap.put("411203","陕州区");
        codeKeyMap.put("411221","渑池县");
        codeKeyMap.put("411224","卢氏县");
        codeKeyMap.put("411271","河南三门峡经济开发区");
        codeKeyMap.put("411281","义马");
        codeKeyMap.put("411282","灵宝");
        codeKeyMap.put("411300","南阳");
        codeKeyMap.put("411302","宛城区");
        codeKeyMap.put("411303","卧龙区");
        codeKeyMap.put("411321","南召县");
        codeKeyMap.put("411322","方城县");
        codeKeyMap.put("411323","西峡县");
        codeKeyMap.put("411324","镇平县");
        codeKeyMap.put("411325","内乡县");
        codeKeyMap.put("411326","淅川县");
        codeKeyMap.put("411327","社旗县");
        codeKeyMap.put("411328","唐河县");
        codeKeyMap.put("411329","新野县");
        codeKeyMap.put("411330","桐柏县");
        codeKeyMap.put("411371","南阳高新技术产业开发区");
        codeKeyMap.put("411372","南阳城乡一体化示范区");
        codeKeyMap.put("411381","邓州");
        codeKeyMap.put("411400","商丘");
        codeKeyMap.put("411402","梁园区");
        codeKeyMap.put("411403","睢阳区");
        codeKeyMap.put("411421","民权县");
        codeKeyMap.put("411422","睢县");
        codeKeyMap.put("411423","宁陵县");
        codeKeyMap.put("411424","柘城县");
        codeKeyMap.put("411425","虞城县");
        codeKeyMap.put("411426","夏邑县");
        codeKeyMap.put("411471","豫东综合物流产业聚集区");
        codeKeyMap.put("411472","河南商丘经济开发区");
        codeKeyMap.put("411481","永城");
        codeKeyMap.put("411500","信阳");
        codeKeyMap.put("411502","浉河区");
        codeKeyMap.put("411503","平桥区");
        codeKeyMap.put("411521","罗山县");
        codeKeyMap.put("411522","光山县");
        codeKeyMap.put("411523","新县");
        codeKeyMap.put("411524","商城县");
        codeKeyMap.put("411525","固始县");
        codeKeyMap.put("411526","潢川县");
        codeKeyMap.put("411527","淮滨县");
        codeKeyMap.put("411528","息县");
        codeKeyMap.put("411571","信阳高新技术产业开发区");
        codeKeyMap.put("411600","周口");
        codeKeyMap.put("411602","川汇区");
        codeKeyMap.put("411621","扶沟县");
        codeKeyMap.put("411622","西华县");
        codeKeyMap.put("411623","商水县");
        codeKeyMap.put("411624","沈丘县");
        codeKeyMap.put("411625","郸城县");
        codeKeyMap.put("411626","淮阳县");
        codeKeyMap.put("411627","太康县");
        codeKeyMap.put("411628","鹿邑县");
        codeKeyMap.put("411671","河南周口经济开发区");
        codeKeyMap.put("411681","项城");
        codeKeyMap.put("411700","驻马店");
        codeKeyMap.put("411702","驿城区");
        codeKeyMap.put("411721","西平县");
        codeKeyMap.put("411722","上蔡县");
        codeKeyMap.put("411723","平舆县");
        codeKeyMap.put("411724","正阳县");
        codeKeyMap.put("411725","确山县");
        codeKeyMap.put("411726","泌阳县");
        codeKeyMap.put("411727","汝南县");
        codeKeyMap.put("411728","遂平县");
        codeKeyMap.put("411729","新蔡县");
        codeKeyMap.put("411771","河南驻马店经济开发区");
        codeKeyMap.put("419000","直辖县级行政区划");
        codeKeyMap.put("419001","济源");
        codeKeyMap.put("420000","湖北");
        codeKeyMap.put("420100","武汉");
        codeKeyMap.put("420102","江岸区");
        codeKeyMap.put("420103","江汉区");
        codeKeyMap.put("420104","硚口区");
        codeKeyMap.put("420105","汉阳区");
        codeKeyMap.put("420106","武昌区");
        codeKeyMap.put("420107","青山区");
        codeKeyMap.put("420111","洪山区");
        codeKeyMap.put("420112","东西湖区");
        codeKeyMap.put("420113","汉南区");
        codeKeyMap.put("420114","蔡甸区");
        codeKeyMap.put("420115","江夏区");
        codeKeyMap.put("420116","黄陂区");
        codeKeyMap.put("420117","新洲区");
        codeKeyMap.put("420200","黄石");
        codeKeyMap.put("420202","黄石港区");
        codeKeyMap.put("420203","西塞山区");
        codeKeyMap.put("420204","下陆区");
        codeKeyMap.put("420205","铁山区");
        codeKeyMap.put("420222","阳新县");
        codeKeyMap.put("420281","大冶");
        codeKeyMap.put("420300","十堰");
        codeKeyMap.put("420302","茅箭区");
        codeKeyMap.put("420303","张湾区");
        codeKeyMap.put("420304","郧阳区");
        codeKeyMap.put("420322","郧西县");
        codeKeyMap.put("420323","竹山县");
        codeKeyMap.put("420324","竹溪县");
        codeKeyMap.put("420325","房县");
        codeKeyMap.put("420381","丹江口");
        codeKeyMap.put("420500","宜昌");
        codeKeyMap.put("420502","西陵区");
        codeKeyMap.put("420503","伍家岗区");
        codeKeyMap.put("420504","点军区");
        codeKeyMap.put("420505","猇亭区");
        codeKeyMap.put("420506","夷陵区");
        codeKeyMap.put("420525","远安县");
        codeKeyMap.put("420526","兴山县");
        codeKeyMap.put("420527","秭归县");
        codeKeyMap.put("420528","长阳土家族自治县");
        codeKeyMap.put("420529","五峰土家族自治县");
        codeKeyMap.put("420581","宜都");
        codeKeyMap.put("420582","当阳");
        codeKeyMap.put("420583","枝江");
        codeKeyMap.put("420600","襄阳");
        codeKeyMap.put("420602","襄城区");
        codeKeyMap.put("420606","樊城区");
        codeKeyMap.put("420607","襄州区");
        codeKeyMap.put("420624","南漳县");
        codeKeyMap.put("420625","谷城县");
        codeKeyMap.put("420626","保康县");
        codeKeyMap.put("420682","老河口");
        codeKeyMap.put("420683","枣阳");
        codeKeyMap.put("420684","宜城");
        codeKeyMap.put("420700","鄂州");
        codeKeyMap.put("420702","梁子湖区");
        codeKeyMap.put("420703","华容区");
        codeKeyMap.put("420704","鄂城区");
        codeKeyMap.put("420800","荆门");
        codeKeyMap.put("420802","东宝区");
        codeKeyMap.put("420804","掇刀区");
        codeKeyMap.put("420822","沙洋县");
        codeKeyMap.put("420881","钟祥");
        codeKeyMap.put("420882","京山");
        codeKeyMap.put("420900","孝感");
        codeKeyMap.put("420902","孝南区");
        codeKeyMap.put("420921","孝昌县");
        codeKeyMap.put("420922","大悟县");
        codeKeyMap.put("420923","云梦县");
        codeKeyMap.put("420981","应城");
        codeKeyMap.put("420982","安陆");
        codeKeyMap.put("420984","汉川");
        codeKeyMap.put("421000","荆州");
        codeKeyMap.put("421002","沙区");
        codeKeyMap.put("421003","荆州区");
        codeKeyMap.put("421022","公安县");
        codeKeyMap.put("421023","监利县");
        codeKeyMap.put("421024","江陵县");
        codeKeyMap.put("421071","荆州经济技术开发区");
        codeKeyMap.put("421081","石首");
        codeKeyMap.put("421083","洪湖");
        codeKeyMap.put("421087","松滋");
        codeKeyMap.put("421100","黄冈");
        codeKeyMap.put("421102","黄州区");
        codeKeyMap.put("421121","团风县");
        codeKeyMap.put("421122","红安县");
        codeKeyMap.put("421123","罗田县");
        codeKeyMap.put("421124","英山县");
        codeKeyMap.put("421125","浠水县");
        codeKeyMap.put("421126","蕲春县");
        codeKeyMap.put("421127","黄梅县");
        codeKeyMap.put("421171","龙感湖管理区");
        codeKeyMap.put("421181","麻城");
        codeKeyMap.put("421182","武穴");
        codeKeyMap.put("421200","咸宁");
        codeKeyMap.put("421202","咸安区");
        codeKeyMap.put("421221","嘉鱼县");
        codeKeyMap.put("421222","通城县");
        codeKeyMap.put("421223","崇阳县");
        codeKeyMap.put("421224","通山县");
        codeKeyMap.put("421281","赤壁");
        codeKeyMap.put("421300","随州");
        codeKeyMap.put("421303","曾都区");
        codeKeyMap.put("421321","随县");
        codeKeyMap.put("421381","广水");
        codeKeyMap.put("422800","恩施土家族苗族自治州");
        codeKeyMap.put("422801","恩施");
        codeKeyMap.put("422802","利川");
        codeKeyMap.put("422822","建始县");
        codeKeyMap.put("422823","巴东县");
        codeKeyMap.put("422825","宣恩县");
        codeKeyMap.put("422826","咸丰县");
        codeKeyMap.put("422827","来凤县");
        codeKeyMap.put("422828","鹤峰县");
        codeKeyMap.put("429000","直辖县级行政区划");
        codeKeyMap.put("429004","仙桃");
        codeKeyMap.put("429005","潜江");
        codeKeyMap.put("429006","天门");
        codeKeyMap.put("429021","神农架林区");
        codeKeyMap.put("430000","湖南");
        codeKeyMap.put("430100","长沙");
        codeKeyMap.put("430102","芙蓉区");
        codeKeyMap.put("430103","天心区");
        codeKeyMap.put("430104","岳麓区");
        codeKeyMap.put("430105","开福区");
        codeKeyMap.put("430111","雨花区");
        codeKeyMap.put("430112","望城区");
        codeKeyMap.put("430121","长沙县");
        codeKeyMap.put("430181","浏阳");
        codeKeyMap.put("430182","宁乡");
        codeKeyMap.put("430200","株洲");
        codeKeyMap.put("430202","荷塘区");
        codeKeyMap.put("430203","芦淞区");
        codeKeyMap.put("430204","石峰区");
        codeKeyMap.put("430211","天元区");
        codeKeyMap.put("430212","渌口区");
        codeKeyMap.put("430223","攸县");
        codeKeyMap.put("430224","茶陵县");
        codeKeyMap.put("430225","炎陵县");
        codeKeyMap.put("430271","云龙示范区");
        codeKeyMap.put("430281","醴陵");
        codeKeyMap.put("430300","湘潭");
        codeKeyMap.put("430302","雨湖区");
        codeKeyMap.put("430304","岳塘区");
        codeKeyMap.put("430321","湘潭县");
        codeKeyMap.put("430371","湖南湘潭高新技术产业园区");
        codeKeyMap.put("430372","湘潭昭山示范区");
        codeKeyMap.put("430373","湘潭九华示范区");
        codeKeyMap.put("430381","湘乡");
        codeKeyMap.put("430382","韶山");
        codeKeyMap.put("430400","衡阳");
        codeKeyMap.put("430405","珠晖区");
        codeKeyMap.put("430406","雁峰区");
        codeKeyMap.put("430407","石鼓区");
        codeKeyMap.put("430408","蒸湘区");
        codeKeyMap.put("430412","南岳区");
        codeKeyMap.put("430421","衡阳县");
        codeKeyMap.put("430422","衡南县");
        codeKeyMap.put("430423","衡山县");
        codeKeyMap.put("430424","衡东县");
        codeKeyMap.put("430426","祁东县");
        codeKeyMap.put("430471","衡阳综合保税区");
        codeKeyMap.put("430472","湖南衡阳高新技术产业园区");
        codeKeyMap.put("430473","湖南衡阳松木经济开发区");
        codeKeyMap.put("430481","耒阳");
        codeKeyMap.put("430482","常宁");
        codeKeyMap.put("430500","邵阳");
        codeKeyMap.put("430502","双清区");
        codeKeyMap.put("430503","大祥区");
        codeKeyMap.put("430511","北塔区");
        codeKeyMap.put("430521","邵东县");
        codeKeyMap.put("430522","新邵县");
        codeKeyMap.put("430523","邵阳县");
        codeKeyMap.put("430524","隆回县");
        codeKeyMap.put("430525","洞口县");
        codeKeyMap.put("430527","绥宁县");
        codeKeyMap.put("430528","新宁县");
        codeKeyMap.put("430529","城步苗族自治县");
        codeKeyMap.put("430581","武冈");
        codeKeyMap.put("430600","岳阳");
        codeKeyMap.put("430602","岳阳楼区");
        codeKeyMap.put("430603","云溪区");
        codeKeyMap.put("430611","君山区");
        codeKeyMap.put("430621","岳阳县");
        codeKeyMap.put("430623","华容县");
        codeKeyMap.put("430624","湘阴县");
        codeKeyMap.put("430626","平江县");
        codeKeyMap.put("430671","岳阳屈原管理区");
        codeKeyMap.put("430681","汨罗");
        codeKeyMap.put("430682","临湘");
        codeKeyMap.put("430700","常德");
        codeKeyMap.put("430702","武陵区");
        codeKeyMap.put("430703","鼎城区");
        codeKeyMap.put("430721","安乡县");
        codeKeyMap.put("430722","汉寿县");
        codeKeyMap.put("430723","澧县");
        codeKeyMap.put("430724","临澧县");
        codeKeyMap.put("430725","桃源县");
        codeKeyMap.put("430726","石门县");
        codeKeyMap.put("430771","常德西洞庭管理区");
        codeKeyMap.put("430781","津");
        codeKeyMap.put("430800","张家界");
        codeKeyMap.put("430802","永定区");
        codeKeyMap.put("430811","武陵源区");
        codeKeyMap.put("430821","慈利县");
        codeKeyMap.put("430822","桑植县");
        codeKeyMap.put("430900","益阳");
        codeKeyMap.put("430902","资阳区");
        codeKeyMap.put("430903","赫山区");
        codeKeyMap.put("430921","南县");
        codeKeyMap.put("430922","桃江县");
        codeKeyMap.put("430923","安化县");
        codeKeyMap.put("430971","益阳大通湖管理区");
        codeKeyMap.put("430972","湖南益阳高新技术产业园区");
        codeKeyMap.put("430981","沅江");
        codeKeyMap.put("431000","郴州");
        codeKeyMap.put("431002","北湖区");
        codeKeyMap.put("431003","苏仙区");
        codeKeyMap.put("431021","桂阳县");
        codeKeyMap.put("431022","宜章县");
        codeKeyMap.put("431023","永兴县");
        codeKeyMap.put("431024","嘉禾县");
        codeKeyMap.put("431025","临武县");
        codeKeyMap.put("431026","汝城县");
        codeKeyMap.put("431027","桂东县");
        codeKeyMap.put("431028","安仁县");
        codeKeyMap.put("431081","资兴");
        codeKeyMap.put("431100","永州");
        codeKeyMap.put("431102","零陵区");
        codeKeyMap.put("431103","冷水滩区");
        codeKeyMap.put("431121","祁阳县");
        codeKeyMap.put("431122","东安县");
        codeKeyMap.put("431123","双牌县");
        codeKeyMap.put("431124","道县");
        codeKeyMap.put("431125","江永县");
        codeKeyMap.put("431126","宁远县");
        codeKeyMap.put("431127","蓝山县");
        codeKeyMap.put("431128","新田县");
        codeKeyMap.put("431129","江华瑶族自治县");
        codeKeyMap.put("431171","永州经济技术开发区");
        codeKeyMap.put("431172","永州金洞管理区");
        codeKeyMap.put("431173","永州回龙圩管理区");
        codeKeyMap.put("431200","怀化");
        codeKeyMap.put("431202","鹤城区");
        codeKeyMap.put("431221","中方县");
        codeKeyMap.put("431222","沅陵县");
        codeKeyMap.put("431223","辰溪县");
        codeKeyMap.put("431224","溆浦县");
        codeKeyMap.put("431225","会同县");
        codeKeyMap.put("431226","麻阳苗族自治县");
        codeKeyMap.put("431227","新晃侗族自治县");
        codeKeyMap.put("431228","芷江侗族自治县");
        codeKeyMap.put("431229","靖州苗族侗族自治县");
        codeKeyMap.put("431230","通道侗族自治县");
        codeKeyMap.put("431271","怀化洪江管理区");
        codeKeyMap.put("431281","洪江");
        codeKeyMap.put("431300","娄底");
        codeKeyMap.put("431302","娄星区");
        codeKeyMap.put("431321","双峰县");
        codeKeyMap.put("431322","新化县");
        codeKeyMap.put("431381","冷水江");
        codeKeyMap.put("431382","涟源");
        codeKeyMap.put("433100","湘西土家族苗族自治州");
        codeKeyMap.put("433101","吉首");
        codeKeyMap.put("433122","泸溪县");
        codeKeyMap.put("433123","凤凰县");
        codeKeyMap.put("433124","花垣县");
        codeKeyMap.put("433125","保靖县");
        codeKeyMap.put("433126","古丈县");
        codeKeyMap.put("433127","永顺县");
        codeKeyMap.put("433130","龙山县");
        codeKeyMap.put("433172","湖南吉首经济开发区");
        codeKeyMap.put("433173","湖南永顺经济开发区");
        codeKeyMap.put("440000","广东");
        codeKeyMap.put("440100","广州");
        codeKeyMap.put("440103","荔湾区");
        codeKeyMap.put("440104","越秀区");
        codeKeyMap.put("440105","海珠区");
        codeKeyMap.put("440106","天河区");
        codeKeyMap.put("440111","白云区");
        codeKeyMap.put("440112","黄埔区");
        codeKeyMap.put("440113","番禺区");
        codeKeyMap.put("440114","花都区");
        codeKeyMap.put("440115","南沙区");
        codeKeyMap.put("440117","从化区");
        codeKeyMap.put("440118","增城区");
        codeKeyMap.put("440200","韶关");
        codeKeyMap.put("440203","武江区");
        codeKeyMap.put("440204","浈江区");
        codeKeyMap.put("440205","曲江区");
        codeKeyMap.put("440222","始兴县");
        codeKeyMap.put("440224","仁化县");
        codeKeyMap.put("440229","翁源县");
        codeKeyMap.put("440232","乳源瑶族自治县");
        codeKeyMap.put("440233","新丰县");
        codeKeyMap.put("440281","乐昌");
        codeKeyMap.put("440282","南雄");
        codeKeyMap.put("440300","深圳");
        codeKeyMap.put("440303","罗湖区");
        codeKeyMap.put("440304","福田区");
        codeKeyMap.put("440305","南山区");
        codeKeyMap.put("440306","宝安区");
        codeKeyMap.put("440307","龙岗区");
        codeKeyMap.put("440308","盐田区");
        codeKeyMap.put("440309","龙华区");
        codeKeyMap.put("440310","坪山区");
        codeKeyMap.put("440311","光明区");
        codeKeyMap.put("440400","珠海");
        codeKeyMap.put("440402","香洲区");
        codeKeyMap.put("440403","斗门区");
        codeKeyMap.put("440404","金湾区");
        codeKeyMap.put("440500","汕头");
        codeKeyMap.put("440507","龙湖区");
        codeKeyMap.put("440511","金平区");
        codeKeyMap.put("440512","濠江区");
        codeKeyMap.put("440513","潮阳区");
        codeKeyMap.put("440514","潮南区");
        codeKeyMap.put("440515","澄海区");
        codeKeyMap.put("440523","南澳县");
        codeKeyMap.put("440600","佛山");
        codeKeyMap.put("440604","禅城区");
        codeKeyMap.put("440605","南海区");
        codeKeyMap.put("440606","顺德区");
        codeKeyMap.put("440607","三水区");
        codeKeyMap.put("440608","高明区");
        codeKeyMap.put("440700","江门");
        codeKeyMap.put("440703","蓬江区");
        codeKeyMap.put("440704","江海区");
        codeKeyMap.put("440705","新会区");
        codeKeyMap.put("440781","台山");
        codeKeyMap.put("440783","开平");
        codeKeyMap.put("440784","鹤山");
        codeKeyMap.put("440785","恩平");
        codeKeyMap.put("440800","湛江");
        codeKeyMap.put("440802","赤坎区");
        codeKeyMap.put("440803","霞山区");
        codeKeyMap.put("440804","坡头区");
        codeKeyMap.put("440811","麻章区");
        codeKeyMap.put("440823","遂溪县");
        codeKeyMap.put("440825","徐闻县");
        codeKeyMap.put("440881","廉江");
        codeKeyMap.put("440882","雷州");
        codeKeyMap.put("440883","吴川");
        codeKeyMap.put("440900","茂名");
        codeKeyMap.put("440902","茂南区");
        codeKeyMap.put("440904","电白区");
        codeKeyMap.put("440981","高州");
        codeKeyMap.put("440982","化州");
        codeKeyMap.put("440983","信宜");
        codeKeyMap.put("441200","肇庆");
        codeKeyMap.put("441202","端州区");
        codeKeyMap.put("441203","鼎湖区");
        codeKeyMap.put("441204","高要区");
        codeKeyMap.put("441223","广宁县");
        codeKeyMap.put("441224","怀集县");
        codeKeyMap.put("441225","封开县");
        codeKeyMap.put("441226","德庆县");
        codeKeyMap.put("441284","四会");
        codeKeyMap.put("441300","惠州");
        codeKeyMap.put("441302","惠城区");
        codeKeyMap.put("441303","惠阳区");
        codeKeyMap.put("441322","博罗县");
        codeKeyMap.put("441323","惠东县");
        codeKeyMap.put("441324","龙门县");
        codeKeyMap.put("441400","梅州");
        codeKeyMap.put("441402","梅江区");
        codeKeyMap.put("441403","梅县区");
        codeKeyMap.put("441422","大埔县");
        codeKeyMap.put("441423","丰顺县");
        codeKeyMap.put("441424","五华县");
        codeKeyMap.put("441426","平远县");
        codeKeyMap.put("441427","蕉岭县");
        codeKeyMap.put("441481","兴宁");
        codeKeyMap.put("441500","汕尾");
        codeKeyMap.put("441502","城区");
        codeKeyMap.put("441523","陆河县");
        codeKeyMap.put("441581","陆丰");
        codeKeyMap.put("441600","河源");
        codeKeyMap.put("441602","源城区");
        codeKeyMap.put("441621","紫金县");
        codeKeyMap.put("441622","龙川县");
        codeKeyMap.put("441623","连平县");
        codeKeyMap.put("441624","和平县");
        codeKeyMap.put("441625","东源县");
        codeKeyMap.put("441700","阳江");
        codeKeyMap.put("441702","江城区");
        codeKeyMap.put("441704","阳东区");
        codeKeyMap.put("441721","阳西县");
        codeKeyMap.put("441781","阳春");
        codeKeyMap.put("441800","清远");
        codeKeyMap.put("441802","清城区");
        codeKeyMap.put("441803","清新区");
        codeKeyMap.put("441821","佛冈县");
        codeKeyMap.put("441823","阳山县");
        codeKeyMap.put("441825","连山壮族瑶族自治县");
        codeKeyMap.put("441826","连南瑶族自治县");
        codeKeyMap.put("441881","英德");
        codeKeyMap.put("441882","连州");
        codeKeyMap.put("441900","东莞");
        codeKeyMap.put("441900003","东城街道");
        codeKeyMap.put("441900004","南城街道");
        codeKeyMap.put("441900005","万江街道");
        codeKeyMap.put("441900006","莞城街道");
        codeKeyMap.put("441900101","石碣镇");
        codeKeyMap.put("441900102","石龙镇");
        codeKeyMap.put("441900103","茶山镇");
        codeKeyMap.put("441900104","石排镇");
        codeKeyMap.put("441900105","企石镇");
        codeKeyMap.put("441900106","横沥镇");
        codeKeyMap.put("441900107","桥头镇");
        codeKeyMap.put("441900108","谢岗镇");
        codeKeyMap.put("441900109","东坑镇");
        codeKeyMap.put("441900110","常平镇");
        codeKeyMap.put("441900111","寮步镇");
        codeKeyMap.put("441900112","樟木头镇");
        codeKeyMap.put("441900113","大朗镇");
        codeKeyMap.put("441900114","黄江镇");
        codeKeyMap.put("441900115","清溪镇");
        codeKeyMap.put("441900116","塘厦镇");
        codeKeyMap.put("441900117","凤岗镇");
        codeKeyMap.put("441900118","大岭山镇");
        codeKeyMap.put("441900119","长安镇");
        codeKeyMap.put("441900121","虎门镇");
        codeKeyMap.put("441900122","厚街镇");
        codeKeyMap.put("441900123","沙田镇");
        codeKeyMap.put("441900124","道滘镇");
        codeKeyMap.put("441900125","洪梅镇");
        codeKeyMap.put("441900126","麻涌镇");
        codeKeyMap.put("441900127","望牛墩镇");
        codeKeyMap.put("441900128","中堂镇");
        codeKeyMap.put("441900129","高埗镇");
        codeKeyMap.put("441900401","松山湖管委会");
        codeKeyMap.put("441900402","东莞港");
        codeKeyMap.put("441900403","东莞生态园");
        codeKeyMap.put("442000","中山");
        codeKeyMap.put("442000001","石岐区街道");
        codeKeyMap.put("442000002","东区街道");
        codeKeyMap.put("442000003","火炬开发区街道");
        codeKeyMap.put("442000004","西区街道");
        codeKeyMap.put("442000005","南区街道");
        codeKeyMap.put("442000006","五桂山街道");
        codeKeyMap.put("442000100","小榄镇");
        codeKeyMap.put("442000101","黄圃镇");
        codeKeyMap.put("442000102","民众镇");
        codeKeyMap.put("442000103","东凤镇");
        codeKeyMap.put("442000104","东升镇");
        codeKeyMap.put("442000105","古镇镇");
        codeKeyMap.put("442000106","沙溪镇");
        codeKeyMap.put("442000107","坦洲镇");
        codeKeyMap.put("442000108","港口镇");
        codeKeyMap.put("442000109","三角镇");
        codeKeyMap.put("442000110","横栏镇");
        codeKeyMap.put("442000111","南头镇");
        codeKeyMap.put("442000112","阜沙镇");
        codeKeyMap.put("442000113","南朗镇");
        codeKeyMap.put("442000114","三乡镇");
        codeKeyMap.put("442000115","板芙镇");
        codeKeyMap.put("442000116","大涌镇");
        codeKeyMap.put("442000117","神湾镇");
        codeKeyMap.put("445100","潮州");
        codeKeyMap.put("445102","湘桥区");
        codeKeyMap.put("445103","潮安区");
        codeKeyMap.put("445122","饶平县");
        codeKeyMap.put("445200","揭阳");
        codeKeyMap.put("445202","榕城区");
        codeKeyMap.put("445203","揭东区");
        codeKeyMap.put("445222","揭西县");
        codeKeyMap.put("445224","惠来县");
        codeKeyMap.put("445281","普宁");
        codeKeyMap.put("445300","云浮");
        codeKeyMap.put("445302","云城区");
        codeKeyMap.put("445303","云安区");
        codeKeyMap.put("445321","新兴县");
        codeKeyMap.put("445322","郁南县");
        codeKeyMap.put("445381","罗定");
        codeKeyMap.put("450000","广西壮族自治区");
        codeKeyMap.put("450100","南宁");
        codeKeyMap.put("450102","兴宁区");
        codeKeyMap.put("450103","青秀区");
        codeKeyMap.put("450105","江南区");
        codeKeyMap.put("450107","西乡塘区");
        codeKeyMap.put("450108","良庆区");
        codeKeyMap.put("450109","邕宁区");
        codeKeyMap.put("450110","武鸣区");
        codeKeyMap.put("450123","隆安县");
        codeKeyMap.put("450124","马山县");
        codeKeyMap.put("450125","上林县");
        codeKeyMap.put("450126","宾阳县");
        codeKeyMap.put("450127","横县");
        codeKeyMap.put("450200","柳州");
        codeKeyMap.put("450202","城中区");
        codeKeyMap.put("450203","鱼峰区");
        codeKeyMap.put("450204","柳南区");
        codeKeyMap.put("450205","柳北区");
        codeKeyMap.put("450206","柳江区");
        codeKeyMap.put("450222","柳城县");
        codeKeyMap.put("450223","鹿寨县");
        codeKeyMap.put("450224","融安县");
        codeKeyMap.put("450225","融水苗族自治县");
        codeKeyMap.put("450226","三江侗族自治县");
        codeKeyMap.put("450300","桂林");
        codeKeyMap.put("450302","秀峰区");
        codeKeyMap.put("450303","叠彩区");
        codeKeyMap.put("450304","象山区");
        codeKeyMap.put("450305","七星区");
        codeKeyMap.put("450311","雁山区");
        codeKeyMap.put("450312","临桂区");
        codeKeyMap.put("450321","阳朔县");
        codeKeyMap.put("450323","灵川县");
        codeKeyMap.put("450324","全州县");
        codeKeyMap.put("450325","兴安县");
        codeKeyMap.put("450326","永福县");
        codeKeyMap.put("450327","灌阳县");
        codeKeyMap.put("450328","龙胜各族自治县");
        codeKeyMap.put("450329","资源县");
        codeKeyMap.put("450330","平乐县");
        codeKeyMap.put("450332","恭城瑶族自治县");
        codeKeyMap.put("450381","荔浦");
        codeKeyMap.put("450400","梧州");
        codeKeyMap.put("450403","万秀区");
        codeKeyMap.put("450405","长洲区");
        codeKeyMap.put("450406","龙圩区");
        codeKeyMap.put("450421","苍梧县");
        codeKeyMap.put("450422","藤县");
        codeKeyMap.put("450423","蒙山县");
        codeKeyMap.put("450481","岑溪");
        codeKeyMap.put("450500","北海");
        codeKeyMap.put("450502","海城区");
        codeKeyMap.put("450503","银海区");
        codeKeyMap.put("450512","铁山港区");
        codeKeyMap.put("450521","合浦县");
        codeKeyMap.put("450600","防城港");
        codeKeyMap.put("450602","港口区");
        codeKeyMap.put("450603","防城区");
        codeKeyMap.put("450621","上思县");
        codeKeyMap.put("450681","东兴");
        codeKeyMap.put("450700","钦州");
        codeKeyMap.put("450702","钦南区");
        codeKeyMap.put("450703","钦北区");
        codeKeyMap.put("450721","灵山县");
        codeKeyMap.put("450722","浦北县");
        codeKeyMap.put("450800","贵港");
        codeKeyMap.put("450802","港北区");
        codeKeyMap.put("450803","港南区");
        codeKeyMap.put("450804","覃塘区");
        codeKeyMap.put("450821","平南县");
        codeKeyMap.put("450881","桂平");
        codeKeyMap.put("450900","玉林");
        codeKeyMap.put("450902","玉州区");
        codeKeyMap.put("450903","福绵区");
        codeKeyMap.put("450921","容县");
        codeKeyMap.put("450922","陆川县");
        codeKeyMap.put("450923","博白县");
        codeKeyMap.put("450924","兴业县");
        codeKeyMap.put("450981","北流");
        codeKeyMap.put("451000","百色");
        codeKeyMap.put("451002","右江区");
        codeKeyMap.put("451021","田阳县");
        codeKeyMap.put("451022","田东县");
        codeKeyMap.put("451023","平果县");
        codeKeyMap.put("451024","德保县");
        codeKeyMap.put("451026","那坡县");
        codeKeyMap.put("451027","凌云县");
        codeKeyMap.put("451028","乐业县");
        codeKeyMap.put("451029","田林县");
        codeKeyMap.put("451030","西林县");
        codeKeyMap.put("451031","隆林各族自治县");
        codeKeyMap.put("451081","靖西");
        codeKeyMap.put("451100","贺州");
        codeKeyMap.put("451102","八步区");
        codeKeyMap.put("451103","平桂区");
        codeKeyMap.put("451121","昭平县");
        codeKeyMap.put("451122","钟山县");
        codeKeyMap.put("451123","富川瑶族自治县");
        codeKeyMap.put("451200","河池");
        codeKeyMap.put("451202","金城江区");
        codeKeyMap.put("451203","宜州区");
        codeKeyMap.put("451221","南丹县");
        codeKeyMap.put("451222","天峨县");
        codeKeyMap.put("451223","凤山县");
        codeKeyMap.put("451224","东兰县");
        codeKeyMap.put("451225","罗城仫佬族自治县");
        codeKeyMap.put("451226","环江毛南族自治县");
        codeKeyMap.put("451227","巴马瑶族自治县");
        codeKeyMap.put("451228","都安瑶族自治县");
        codeKeyMap.put("451229","大化瑶族自治县");
        codeKeyMap.put("451300","来宾");
        codeKeyMap.put("451302","兴宾区");
        codeKeyMap.put("451321","忻城县");
        codeKeyMap.put("451322","象州县");
        codeKeyMap.put("451323","武宣县");
        codeKeyMap.put("451324","金秀瑶族自治县");
        codeKeyMap.put("451381","合山");
        codeKeyMap.put("451400","崇左");
        codeKeyMap.put("451402","江州区");
        codeKeyMap.put("451421","扶绥县");
        codeKeyMap.put("451422","宁明县");
        codeKeyMap.put("451423","龙州县");
        codeKeyMap.put("451424","大新县");
        codeKeyMap.put("451425","天等县");
        codeKeyMap.put("451481","凭祥");
        codeKeyMap.put("460000","海南");
        codeKeyMap.put("460100","海口");
        codeKeyMap.put("460105","秀英区");
        codeKeyMap.put("460106","龙华区");
        codeKeyMap.put("460107","琼山区");
        codeKeyMap.put("460108","美兰区");
        codeKeyMap.put("460200","三亚");
        codeKeyMap.put("460202","海棠区");
        codeKeyMap.put("460203","吉阳区");
        codeKeyMap.put("460204","天涯区");
        codeKeyMap.put("460205","崖州区");
        codeKeyMap.put("460300","三沙");
        codeKeyMap.put("460321","西沙群岛");
        codeKeyMap.put("460322","南沙群岛");
        codeKeyMap.put("460323","中沙群岛的岛礁及其海域");
        codeKeyMap.put("460400","儋州");
        codeKeyMap.put("460400100","那大镇");
        codeKeyMap.put("460400101","和庆镇");
        codeKeyMap.put("460400102","南丰镇");
        codeKeyMap.put("460400103","大成镇");
        codeKeyMap.put("460400104","雅星镇");
        codeKeyMap.put("460400105","兰洋镇");
        codeKeyMap.put("460400106","光村镇");
        codeKeyMap.put("460400107","木棠镇");
        codeKeyMap.put("460400108","海头镇");
        codeKeyMap.put("460400109","峨蔓镇");
        codeKeyMap.put("460400111","王五镇");
        codeKeyMap.put("460400112","白马井镇");
        codeKeyMap.put("460400113","中和镇");
        codeKeyMap.put("460400114","排浦镇");
        codeKeyMap.put("460400115","东成镇");
        codeKeyMap.put("460400116","新州镇");
        codeKeyMap.put("460400499","洋浦经济开发区");
        codeKeyMap.put("460400500","华南热作学院");
        codeKeyMap.put("469000","直辖县级行政区划");
        codeKeyMap.put("469001","五指山");
        codeKeyMap.put("469002","琼海");
        codeKeyMap.put("469005","文昌");
        codeKeyMap.put("469006","万宁");
        codeKeyMap.put("469007","东方");
        codeKeyMap.put("469021","定安县");
        codeKeyMap.put("469022","屯昌县");
        codeKeyMap.put("469023","澄迈县");
        codeKeyMap.put("469024","临高县");
        codeKeyMap.put("469025","白沙黎族自治县");
        codeKeyMap.put("469026","昌江黎族自治县");
        codeKeyMap.put("469027","乐东黎族自治县");
        codeKeyMap.put("469028","陵水黎族自治县");
        codeKeyMap.put("469029","保亭黎族苗族自治县");
        codeKeyMap.put("469030","琼中黎族苗族自治县");
        codeKeyMap.put("500000","重庆");
        codeKeyMap.put("500100","重庆");
        codeKeyMap.put("500101","万州区");
        codeKeyMap.put("500102","涪陵区");
        codeKeyMap.put("500103","渝中区");
        codeKeyMap.put("500104","大渡口区");
        codeKeyMap.put("500105","江北区");
        codeKeyMap.put("500106","沙坪坝区");
        codeKeyMap.put("500107","九龙坡区");
        codeKeyMap.put("500108","南岸区");
        codeKeyMap.put("500109","北碚区");
        codeKeyMap.put("500110","綦江区");
        codeKeyMap.put("500111","大足区");
        codeKeyMap.put("500112","渝北区");
        codeKeyMap.put("500113","巴南区");
        codeKeyMap.put("500114","黔江区");
        codeKeyMap.put("500115","长寿区");
        codeKeyMap.put("500116","江津区");
        codeKeyMap.put("500117","合川区");
        codeKeyMap.put("500118","永川区");
        codeKeyMap.put("500119","南川区");
        codeKeyMap.put("500120","璧山区");
        codeKeyMap.put("500151","铜梁区");
        codeKeyMap.put("500152","潼南区");
        codeKeyMap.put("500153","荣昌区");
        codeKeyMap.put("500154","开州区");
        codeKeyMap.put("500155","梁平区");
        codeKeyMap.put("500156","武隆区");
        codeKeyMap.put("500229","城口县");
        codeKeyMap.put("500230","丰都县");
        codeKeyMap.put("500231","垫江县");
        codeKeyMap.put("500233","忠县");
        codeKeyMap.put("500235","云阳县");
        codeKeyMap.put("500236","奉节县");
        codeKeyMap.put("500237","巫山县");
        codeKeyMap.put("500238","巫溪县");
        codeKeyMap.put("500240","石柱土家族自治县");
        codeKeyMap.put("500241","秀山土家族苗族自治县");
        codeKeyMap.put("500242","酉阳土家族苗族自治县");
        codeKeyMap.put("500243","彭水苗族土家族自治县");
        codeKeyMap.put("510000","四川");
        codeKeyMap.put("510100","成都");
        codeKeyMap.put("510104","锦江区");
        codeKeyMap.put("510105","青羊区");
        codeKeyMap.put("510106","金牛区");
        codeKeyMap.put("510107","武侯区");
        codeKeyMap.put("510108","成华区");
        codeKeyMap.put("510112","龙泉驿区");
        codeKeyMap.put("510113","青白江区");
        codeKeyMap.put("510114","新都区");
        codeKeyMap.put("510115","温江区");
        codeKeyMap.put("510116","双流区");
        codeKeyMap.put("510117","郫都区");
        codeKeyMap.put("510121","金堂县");
        codeKeyMap.put("510129","大邑县");
        codeKeyMap.put("510131","蒲江县");
        codeKeyMap.put("510132","新津县");
        codeKeyMap.put("510181","都江堰");
        codeKeyMap.put("510182","彭州");
        codeKeyMap.put("510183","邛崃");
        codeKeyMap.put("510184","崇州");
        codeKeyMap.put("510185","简阳");
        codeKeyMap.put("510300","自贡");
        codeKeyMap.put("510302","自流井区");
        codeKeyMap.put("510303","贡井区");
        codeKeyMap.put("510304","大安区");
        codeKeyMap.put("510311","沿滩区");
        codeKeyMap.put("510321","荣县");
        codeKeyMap.put("510322","富顺县");
        codeKeyMap.put("510400","攀枝花");
        codeKeyMap.put("510402","东区");
        codeKeyMap.put("510403","西区");
        codeKeyMap.put("510411","仁和区");
        codeKeyMap.put("510421","米易县");
        codeKeyMap.put("510422","盐边县");
        codeKeyMap.put("510500","泸州");
        codeKeyMap.put("510502","江阳区");
        codeKeyMap.put("510503","纳溪区");
        codeKeyMap.put("510504","龙马潭区");
        codeKeyMap.put("510521","泸县");
        codeKeyMap.put("510522","合江县");
        codeKeyMap.put("510524","叙永县");
        codeKeyMap.put("510525","古蔺县");
        codeKeyMap.put("510600","德阳");
        codeKeyMap.put("510603","旌阳区");
        codeKeyMap.put("510604","罗江区");
        codeKeyMap.put("510623","中江县");
        codeKeyMap.put("510681","广汉");
        codeKeyMap.put("510682","什邡");
        codeKeyMap.put("510683","绵竹");
        codeKeyMap.put("510700","绵阳");
        codeKeyMap.put("510703","涪城区");
        codeKeyMap.put("510704","游仙区");
        codeKeyMap.put("510705","安州区");
        codeKeyMap.put("510722","三台县");
        codeKeyMap.put("510723","盐亭县");
        codeKeyMap.put("510725","梓潼县");
        codeKeyMap.put("510726","北川羌族自治县");
        codeKeyMap.put("510727","平武县");
        codeKeyMap.put("510781","江油");
        codeKeyMap.put("510800","广元");
        codeKeyMap.put("510802","利州区");
        codeKeyMap.put("510811","昭化区");
        codeKeyMap.put("510812","朝天区");
        codeKeyMap.put("510821","旺苍县");
        codeKeyMap.put("510822","青川县");
        codeKeyMap.put("510823","剑阁县");
        codeKeyMap.put("510824","苍溪县");
        codeKeyMap.put("510900","遂宁");
        codeKeyMap.put("510903","船山区");
        codeKeyMap.put("510904","安居区");
        codeKeyMap.put("510921","蓬溪县");
        codeKeyMap.put("510922","射洪县");
        codeKeyMap.put("510923","大英县");
        codeKeyMap.put("511000","内江");
        codeKeyMap.put("511002","中区");
        codeKeyMap.put("511011","东兴区");
        codeKeyMap.put("511024","威远县");
        codeKeyMap.put("511025","资中县");
        codeKeyMap.put("511071","内江经济开发区");
        codeKeyMap.put("511083","隆昌");
        codeKeyMap.put("511100","乐山");
        codeKeyMap.put("511102","中区");
        codeKeyMap.put("511111","沙湾区");
        codeKeyMap.put("511112","五通桥区");
        codeKeyMap.put("511113","金口河区");
        codeKeyMap.put("511123","犍为县");
        codeKeyMap.put("511124","井研县");
        codeKeyMap.put("511126","夹江县");
        codeKeyMap.put("511129","沐川县");
        codeKeyMap.put("511132","峨边彝族自治县");
        codeKeyMap.put("511133","马边彝族自治县");
        codeKeyMap.put("511181","峨眉山");
        codeKeyMap.put("511300","南充");
        codeKeyMap.put("511302","顺庆区");
        codeKeyMap.put("511303","高坪区");
        codeKeyMap.put("511304","嘉陵区");
        codeKeyMap.put("511321","南部县");
        codeKeyMap.put("511322","营山县");
        codeKeyMap.put("511323","蓬安县");
        codeKeyMap.put("511324","仪陇县");
        codeKeyMap.put("511325","西充县");
        codeKeyMap.put("511381","阆中");
        codeKeyMap.put("511400","眉山");
        codeKeyMap.put("511402","东坡区");
        codeKeyMap.put("511403","彭山区");
        codeKeyMap.put("511421","仁寿县");
        codeKeyMap.put("511423","洪雅县");
        codeKeyMap.put("511424","丹棱县");
        codeKeyMap.put("511425","青神县");
        codeKeyMap.put("511500","宜宾");
        codeKeyMap.put("511502","翠屏区");
        codeKeyMap.put("511503","南溪区");
        codeKeyMap.put("511504","叙州区");
        codeKeyMap.put("511523","江安县");
        codeKeyMap.put("511524","长宁县");
        codeKeyMap.put("511525","高县");
        codeKeyMap.put("511526","珙县");
        codeKeyMap.put("511527","筠连县");
        codeKeyMap.put("511528","兴文县");
        codeKeyMap.put("511529","屏山县");
        codeKeyMap.put("511600","广安");
        codeKeyMap.put("511602","广安区");
        codeKeyMap.put("511603","前锋区");
        codeKeyMap.put("511621","岳池县");
        codeKeyMap.put("511622","武胜县");
        codeKeyMap.put("511623","邻水县");
        codeKeyMap.put("511681","华蓥");
        codeKeyMap.put("511700","达州");
        codeKeyMap.put("511702","通川区");
        codeKeyMap.put("511703","达川区");
        codeKeyMap.put("511722","宣汉县");
        codeKeyMap.put("511723","开江县");
        codeKeyMap.put("511724","大竹县");
        codeKeyMap.put("511725","渠县");
        codeKeyMap.put("511771","达州经济开发区");
        codeKeyMap.put("511781","万源");
        codeKeyMap.put("511800","雅安");
        codeKeyMap.put("511802","雨城区");
        codeKeyMap.put("511803","名山区");
        codeKeyMap.put("511822","荥经县");
        codeKeyMap.put("511823","汉源县");
        codeKeyMap.put("511824","石棉县");
        codeKeyMap.put("511825","天全县");
        codeKeyMap.put("511826","芦山县");
        codeKeyMap.put("511827","宝兴县");
        codeKeyMap.put("511900","巴中");
        codeKeyMap.put("511902","巴州区");
        codeKeyMap.put("511903","恩阳区");
        codeKeyMap.put("511921","通江县");
        codeKeyMap.put("511922","南江县");
        codeKeyMap.put("511923","平昌县");
        codeKeyMap.put("511971","巴中经济开发区");
        codeKeyMap.put("512000","资阳");
        codeKeyMap.put("512002","雁江区");
        codeKeyMap.put("512021","安岳县");
        codeKeyMap.put("512022","乐至县");
        codeKeyMap.put("513200","阿坝藏族羌族自治州");
        codeKeyMap.put("513201","马尔康");
        codeKeyMap.put("513221","汶川县");
        codeKeyMap.put("513222","理县");
        codeKeyMap.put("513223","茂县");
        codeKeyMap.put("513224","松潘县");
        codeKeyMap.put("513225","九寨沟县");
        codeKeyMap.put("513226","金川县");
        codeKeyMap.put("513227","小金县");
        codeKeyMap.put("513228","黑水县");
        codeKeyMap.put("513230","壤塘县");
        codeKeyMap.put("513231","阿坝县");
        codeKeyMap.put("513232","若尔盖县");
        codeKeyMap.put("513233","红原县");
        codeKeyMap.put("513300","甘孜藏族自治州");
        codeKeyMap.put("513301","康定");
        codeKeyMap.put("513322","泸定县");
        codeKeyMap.put("513323","丹巴县");
        codeKeyMap.put("513324","九龙县");
        codeKeyMap.put("513325","雅江县");
        codeKeyMap.put("513326","道孚县");
        codeKeyMap.put("513327","炉霍县");
        codeKeyMap.put("513328","甘孜县");
        codeKeyMap.put("513329","新龙县");
        codeKeyMap.put("513330","德格县");
        codeKeyMap.put("513331","白玉县");
        codeKeyMap.put("513332","石渠县");
        codeKeyMap.put("513333","色达县");
        codeKeyMap.put("513334","理塘县");
        codeKeyMap.put("513335","巴塘县");
        codeKeyMap.put("513336","乡城县");
        codeKeyMap.put("513337","稻城县");
        codeKeyMap.put("513338","得荣县");
        codeKeyMap.put("513400","凉山彝族自治州");
        codeKeyMap.put("513401","西昌");
        codeKeyMap.put("513422","木里藏族自治县");
        codeKeyMap.put("513423","盐源县");
        codeKeyMap.put("513424","德昌县");
        codeKeyMap.put("513425","会理县");
        codeKeyMap.put("513426","会东县");
        codeKeyMap.put("513427","宁南县");
        codeKeyMap.put("513428","普格县");
        codeKeyMap.put("513429","布拖县");
        codeKeyMap.put("513430","金阳县");
        codeKeyMap.put("513431","昭觉县");
        codeKeyMap.put("513432","喜德县");
        codeKeyMap.put("513433","冕宁县");
        codeKeyMap.put("513434","越西县");
        codeKeyMap.put("513435","甘洛县");
        codeKeyMap.put("513436","美姑县");
        codeKeyMap.put("513437","雷波县");
        codeKeyMap.put("520000","贵州");
        codeKeyMap.put("520100","贵阳");
        codeKeyMap.put("520102","南明区");
        codeKeyMap.put("520103","云岩区");
        codeKeyMap.put("520111","花溪区");
        codeKeyMap.put("520112","乌当区");
        codeKeyMap.put("520113","白云区");
        codeKeyMap.put("520115","观山湖区");
        codeKeyMap.put("520121","开阳县");
        codeKeyMap.put("520122","息烽县");
        codeKeyMap.put("520123","修文县");
        codeKeyMap.put("520181","清镇");
        codeKeyMap.put("520200","六盘水");
        codeKeyMap.put("520201","钟山区");
        codeKeyMap.put("520203","六枝特区");
        codeKeyMap.put("520221","水城县");
        codeKeyMap.put("520281","盘州");
        codeKeyMap.put("520300","遵义");
        codeKeyMap.put("520302","红花岗区");
        codeKeyMap.put("520303","汇川区");
        codeKeyMap.put("520304","播州区");
        codeKeyMap.put("520322","桐梓县");
        codeKeyMap.put("520323","绥阳县");
        codeKeyMap.put("520324","正安县");
        codeKeyMap.put("520325","道真仡佬族苗族自治县");
        codeKeyMap.put("520326","务川仡佬族苗族自治县");
        codeKeyMap.put("520327","凤冈县");
        codeKeyMap.put("520328","湄潭县");
        codeKeyMap.put("520329","余庆县");
        codeKeyMap.put("520330","习水县");
        codeKeyMap.put("520381","赤水");
        codeKeyMap.put("520382","仁怀");
        codeKeyMap.put("520400","安顺");
        codeKeyMap.put("520402","西秀区");
        codeKeyMap.put("520403","平坝区");
        codeKeyMap.put("520422","普定县");
        codeKeyMap.put("520423","镇宁布依族苗族自治县");
        codeKeyMap.put("520424","关岭布依族苗族自治县");
        codeKeyMap.put("520425","紫云苗族布依族自治县");
        codeKeyMap.put("520500","毕节");
        codeKeyMap.put("520502","七星关区");
        codeKeyMap.put("520521","大方县");
        codeKeyMap.put("520522","黔西县");
        codeKeyMap.put("520523","金沙县");
        codeKeyMap.put("520524","织金县");
        codeKeyMap.put("520525","纳雍县");
        codeKeyMap.put("520526","威宁彝族回族苗族自治县");
        codeKeyMap.put("520527","赫章县");
        codeKeyMap.put("520600","铜仁");
        codeKeyMap.put("520602","碧江区");
        codeKeyMap.put("520603","万山区");
        codeKeyMap.put("520621","江口县");
        codeKeyMap.put("520622","玉屏侗族自治县");
        codeKeyMap.put("520623","石阡县");
        codeKeyMap.put("520624","思南县");
        codeKeyMap.put("520625","印江土家族苗族自治县");
        codeKeyMap.put("520626","德江县");
        codeKeyMap.put("520627","沿河土家族自治县");
        codeKeyMap.put("520628","松桃苗族自治县");
        codeKeyMap.put("522300","黔西南布依族苗族自治州");
        codeKeyMap.put("522301","兴义");
        codeKeyMap.put("522302","兴仁");
        codeKeyMap.put("522323","普安县");
        codeKeyMap.put("522324","晴隆县");
        codeKeyMap.put("522325","贞丰县");
        codeKeyMap.put("522326","望谟县");
        codeKeyMap.put("522327","册亨县");
        codeKeyMap.put("522328","安龙县");
        codeKeyMap.put("522600","黔东南苗族侗族自治州");
        codeKeyMap.put("522601","凯里");
        codeKeyMap.put("522622","黄平县");
        codeKeyMap.put("522623","施秉县");
        codeKeyMap.put("522624","三穗县");
        codeKeyMap.put("522625","镇远县");
        codeKeyMap.put("522626","岑巩县");
        codeKeyMap.put("522627","天柱县");
        codeKeyMap.put("522628","锦屏县");
        codeKeyMap.put("522629","剑河县");
        codeKeyMap.put("522630","台江县");
        codeKeyMap.put("522631","黎平县");
        codeKeyMap.put("522632","榕江县");
        codeKeyMap.put("522633","从江县");
        codeKeyMap.put("522634","雷山县");
        codeKeyMap.put("522635","麻江县");
        codeKeyMap.put("522636","丹寨县");
        codeKeyMap.put("522700","黔南布依族苗族自治州");
        codeKeyMap.put("522701","都匀");
        codeKeyMap.put("522702","福泉");
        codeKeyMap.put("522722","荔波县");
        codeKeyMap.put("522723","贵定县");
        codeKeyMap.put("522725","瓮安县");
        codeKeyMap.put("522726","独山县");
        codeKeyMap.put("522727","平塘县");
        codeKeyMap.put("522728","罗甸县");
        codeKeyMap.put("522729","长顺县");
        codeKeyMap.put("522730","龙里县");
        codeKeyMap.put("522731","惠水县");
        codeKeyMap.put("522732","三都水族自治县");
        codeKeyMap.put("530000","云南");
        codeKeyMap.put("530100","昆明");
        codeKeyMap.put("530102","五华区");
        codeKeyMap.put("530103","盘龙区");
        codeKeyMap.put("530111","官渡区");
        codeKeyMap.put("530112","西山区");
        codeKeyMap.put("530113","东川区");
        codeKeyMap.put("530114","呈贡区");
        codeKeyMap.put("530115","晋宁区");
        codeKeyMap.put("530124","富民县");
        codeKeyMap.put("530125","宜良县");
        codeKeyMap.put("530126","石林彝族自治县");
        codeKeyMap.put("530127","嵩明县");
        codeKeyMap.put("530128","禄劝彝族苗族自治县");
        codeKeyMap.put("530129","寻甸回族彝族自治县");
        codeKeyMap.put("530181","安宁");
        codeKeyMap.put("530300","曲靖");
        codeKeyMap.put("530302","麒麟区");
        codeKeyMap.put("530303","沾益区");
        codeKeyMap.put("530304","马龙区");
        codeKeyMap.put("530322","陆良县");
        codeKeyMap.put("530323","师宗县");
        codeKeyMap.put("530324","罗平县");
        codeKeyMap.put("530325","富源县");
        codeKeyMap.put("530326","会泽县");
        codeKeyMap.put("530381","宣威");
        codeKeyMap.put("530400","玉溪");
        codeKeyMap.put("530402","红塔区");
        codeKeyMap.put("530403","江川区");
        codeKeyMap.put("530422","澄江县");
        codeKeyMap.put("530423","通海县");
        codeKeyMap.put("530424","华宁县");
        codeKeyMap.put("530425","易门县");
        codeKeyMap.put("530426","峨山彝族自治县");
        codeKeyMap.put("530427","新平彝族傣族自治县");
        codeKeyMap.put("530428","元江哈尼族彝族傣族自治县");
        codeKeyMap.put("530500","保山");
        codeKeyMap.put("530502","隆阳区");
        codeKeyMap.put("530521","施甸县");
        codeKeyMap.put("530523","龙陵县");
        codeKeyMap.put("530524","昌宁县");
        codeKeyMap.put("530581","腾冲");
        codeKeyMap.put("530600","昭通");
        codeKeyMap.put("530602","昭阳区");
        codeKeyMap.put("530621","鲁甸县");
        codeKeyMap.put("530622","巧家县");
        codeKeyMap.put("530623","盐津县");
        codeKeyMap.put("530624","大关县");
        codeKeyMap.put("530625","永善县");
        codeKeyMap.put("530626","绥江县");
        codeKeyMap.put("530627","镇雄县");
        codeKeyMap.put("530628","彝良县");
        codeKeyMap.put("530629","威信县");
        codeKeyMap.put("530681","水富");
        codeKeyMap.put("530700","丽江");
        codeKeyMap.put("530702","古城区");
        codeKeyMap.put("530721","玉龙纳西族自治县");
        codeKeyMap.put("530722","永胜县");
        codeKeyMap.put("530723","华坪县");
        codeKeyMap.put("530724","宁蒗彝族自治县");
        codeKeyMap.put("530800","普洱");
        codeKeyMap.put("530802","思茅区");
        codeKeyMap.put("530821","宁洱哈尼族彝族自治县");
        codeKeyMap.put("530822","墨江哈尼族自治县");
        codeKeyMap.put("530823","景东彝族自治县");
        codeKeyMap.put("530824","景谷傣族彝族自治县");
        codeKeyMap.put("530825","镇沅彝族哈尼族拉祜族自治县");
        codeKeyMap.put("530826","江城哈尼族彝族自治县");
        codeKeyMap.put("530827","孟连傣族拉祜族佤族自治县");
        codeKeyMap.put("530828","澜沧拉祜族自治县");
        codeKeyMap.put("530829","西盟佤族自治县");
        codeKeyMap.put("530900","临沧");
        codeKeyMap.put("530902","临翔区");
        codeKeyMap.put("530921","凤庆县");
        codeKeyMap.put("530922","云县");
        codeKeyMap.put("530923","永德县");
        codeKeyMap.put("530924","镇康县");
        codeKeyMap.put("530925","双江拉祜族佤族布朗族傣族自治县");
        codeKeyMap.put("530926","耿马傣族佤族自治县");
        codeKeyMap.put("530927","沧源佤族自治县");
        codeKeyMap.put("532300","楚雄彝族自治州");
        codeKeyMap.put("532301","楚雄");
        codeKeyMap.put("532322","双柏县");
        codeKeyMap.put("532323","牟定县");
        codeKeyMap.put("532324","南华县");
        codeKeyMap.put("532325","姚安县");
        codeKeyMap.put("532326","大姚县");
        codeKeyMap.put("532327","永仁县");
        codeKeyMap.put("532328","元谋县");
        codeKeyMap.put("532329","武定县");
        codeKeyMap.put("532331","禄丰县");
        codeKeyMap.put("532500","红河哈尼族彝族自治州");
        codeKeyMap.put("532501","个旧");
        codeKeyMap.put("532502","开远");
        codeKeyMap.put("532503","蒙自");
        codeKeyMap.put("532504","弥勒");
        codeKeyMap.put("532523","屏边苗族自治县");
        codeKeyMap.put("532524","建水县");
        codeKeyMap.put("532525","石屏县");
        codeKeyMap.put("532527","泸西县");
        codeKeyMap.put("532528","元阳县");
        codeKeyMap.put("532529","红河县");
        codeKeyMap.put("532530","金平苗族瑶族傣族自治县");
        codeKeyMap.put("532531","绿春县");
        codeKeyMap.put("532532","河口瑶族自治县");
        codeKeyMap.put("532600","文山壮族苗族自治州");
        codeKeyMap.put("532601","文山");
        codeKeyMap.put("532622","砚山县");
        codeKeyMap.put("532623","西畴县");
        codeKeyMap.put("532624","麻栗坡县");
        codeKeyMap.put("532625","马关县");
        codeKeyMap.put("532626","丘北县");
        codeKeyMap.put("532627","广南县");
        codeKeyMap.put("532628","富宁县");
        codeKeyMap.put("532800","西双版纳傣族自治州");
        codeKeyMap.put("532801","景洪");
        codeKeyMap.put("532822","勐海县");
        codeKeyMap.put("532823","勐腊县");
        codeKeyMap.put("532900","大理白族自治州");
        codeKeyMap.put("532901","大理");
        codeKeyMap.put("532922","漾濞彝族自治县");
        codeKeyMap.put("532923","祥云县");
        codeKeyMap.put("532924","宾川县");
        codeKeyMap.put("532925","弥渡县");
        codeKeyMap.put("532926","南涧彝族自治县");
        codeKeyMap.put("532927","巍山彝族回族自治县");
        codeKeyMap.put("532928","永平县");
        codeKeyMap.put("532929","云龙县");
        codeKeyMap.put("532930","洱源县");
        codeKeyMap.put("532931","剑川县");
        codeKeyMap.put("532932","鹤庆县");
        codeKeyMap.put("533100","德宏傣族景颇族自治州");
        codeKeyMap.put("533102","瑞丽");
        codeKeyMap.put("533103","芒");
        codeKeyMap.put("533122","梁河县");
        codeKeyMap.put("533123","盈江县");
        codeKeyMap.put("533124","陇川县");
        codeKeyMap.put("533300","怒江傈僳族自治州");
        codeKeyMap.put("533301","泸水");
        codeKeyMap.put("533323","福贡县");
        codeKeyMap.put("533324","贡山独龙族怒族自治县");
        codeKeyMap.put("533325","兰坪白族普米族自治县");
        codeKeyMap.put("533400","迪庆藏族自治州");
        codeKeyMap.put("533401","香格里拉");
        codeKeyMap.put("533422","德钦县");
        codeKeyMap.put("533423","维西傈僳族自治县");
        codeKeyMap.put("540000","西藏自治区");
        codeKeyMap.put("540100","拉萨");
        codeKeyMap.put("540102","城关区");
        codeKeyMap.put("540103","堆龙德庆区");
        codeKeyMap.put("540104","达孜区");
        codeKeyMap.put("540121","林周县");
        codeKeyMap.put("540122","当雄县");
        codeKeyMap.put("540123","尼木县");
        codeKeyMap.put("540124","曲水县");
        codeKeyMap.put("540127","墨竹工卡县");
        codeKeyMap.put("540171","格尔木藏青工业园区");
        codeKeyMap.put("540172","拉萨经济技术开发区");
        codeKeyMap.put("540173","西藏文化旅游创意园区");
        codeKeyMap.put("540174","达孜工业园区");
        codeKeyMap.put("540200","日喀则");
        codeKeyMap.put("540202","桑珠孜区");
        codeKeyMap.put("540221","南木林县");
        codeKeyMap.put("540222","江孜县");
        codeKeyMap.put("540223","定日县");
        codeKeyMap.put("540224","萨迦县");
        codeKeyMap.put("540225","拉孜县");
        codeKeyMap.put("540226","昂仁县");
        codeKeyMap.put("540227","谢通门县");
        codeKeyMap.put("540228","白朗县");
        codeKeyMap.put("540229","仁布县");
        codeKeyMap.put("540230","康马县");
        codeKeyMap.put("540231","定结县");
        codeKeyMap.put("540232","仲巴县");
        codeKeyMap.put("540233","亚东县");
        codeKeyMap.put("540234","吉隆县");
        codeKeyMap.put("540235","聂拉木县");
        codeKeyMap.put("540236","萨嘎县");
        codeKeyMap.put("540237","岗巴县");
        codeKeyMap.put("540300","昌都");
        codeKeyMap.put("540302","卡若区");
        codeKeyMap.put("540321","江达县");
        codeKeyMap.put("540322","贡觉县");
        codeKeyMap.put("540323","类乌齐县");
        codeKeyMap.put("540324","丁青县");
        codeKeyMap.put("540325","察雅县");
        codeKeyMap.put("540326","八宿县");
        codeKeyMap.put("540327","左贡县");
        codeKeyMap.put("540328","芒康县");
        codeKeyMap.put("540329","洛隆县");
        codeKeyMap.put("540330","边坝县");
        codeKeyMap.put("540400","林芝");
        codeKeyMap.put("540402","巴宜区");
        codeKeyMap.put("540421","工布江达县");
        codeKeyMap.put("540422","米林县");
        codeKeyMap.put("540423","墨脱县");
        codeKeyMap.put("540424","波密县");
        codeKeyMap.put("540425","察隅县");
        codeKeyMap.put("540426","朗县");
        codeKeyMap.put("540500","山南");
        codeKeyMap.put("540502","乃东区");
        codeKeyMap.put("540521","扎囊县");
        codeKeyMap.put("540522","贡嘎县");
        codeKeyMap.put("540523","桑日县");
        codeKeyMap.put("540524","琼结县");
        codeKeyMap.put("540525","曲松县");
        codeKeyMap.put("540526","措美县");
        codeKeyMap.put("540527","洛扎县");
        codeKeyMap.put("540528","加查县");
        codeKeyMap.put("540529","隆子县");
        codeKeyMap.put("540530","错那县");
        codeKeyMap.put("540531","浪卡子县");
        codeKeyMap.put("540600","那曲");
        codeKeyMap.put("540602","色尼区");
        codeKeyMap.put("540621","嘉黎县");
        codeKeyMap.put("540622","比如县");
        codeKeyMap.put("540623","聂荣县");
        codeKeyMap.put("540624","安多县");
        codeKeyMap.put("540625","申扎县");
        codeKeyMap.put("540626","索县");
        codeKeyMap.put("540627","班戈县");
        codeKeyMap.put("540628","巴青县");
        codeKeyMap.put("540629","尼玛县");
        codeKeyMap.put("540630","双湖县");
        codeKeyMap.put("542500","阿里地区");
        codeKeyMap.put("542521","普兰县");
        codeKeyMap.put("542522","札达县");
        codeKeyMap.put("542523","噶尔县");
        codeKeyMap.put("542524","日土县");
        codeKeyMap.put("542525","革吉县");
        codeKeyMap.put("542526","改则县");
        codeKeyMap.put("542527","措勤县");
        codeKeyMap.put("610000","陕西");
        codeKeyMap.put("610100","西安");
        codeKeyMap.put("610102","新城区");
        codeKeyMap.put("610103","碑林区");
        codeKeyMap.put("610104","莲湖区");
        codeKeyMap.put("610111","灞桥区");
        codeKeyMap.put("610112","未央区");
        codeKeyMap.put("610113","雁塔区");
        codeKeyMap.put("610114","阎良区");
        codeKeyMap.put("610115","临潼区");
        codeKeyMap.put("610116","长安区");
        codeKeyMap.put("610117","高陵区");
        codeKeyMap.put("610118","鄠邑区");
        codeKeyMap.put("610122","蓝田县");
        codeKeyMap.put("610124","周至县");
        codeKeyMap.put("610200","铜川");
        codeKeyMap.put("610202","王益区");
        codeKeyMap.put("610203","印台区");
        codeKeyMap.put("610204","耀州区");
        codeKeyMap.put("610222","宜君县");
        codeKeyMap.put("610300","宝鸡");
        codeKeyMap.put("610302","渭滨区");
        codeKeyMap.put("610303","金台区");
        codeKeyMap.put("610304","陈仓区");
        codeKeyMap.put("610322","凤翔县");
        codeKeyMap.put("610323","岐山县");
        codeKeyMap.put("610324","扶风县");
        codeKeyMap.put("610326","眉县");
        codeKeyMap.put("610327","陇县");
        codeKeyMap.put("610328","千阳县");
        codeKeyMap.put("610329","麟游县");
        codeKeyMap.put("610330","凤县");
        codeKeyMap.put("610331","太白县");
        codeKeyMap.put("610400","咸阳");
        codeKeyMap.put("610402","秦都区");
        codeKeyMap.put("610403","杨陵区");
        codeKeyMap.put("610404","渭城区");
        codeKeyMap.put("610422","三原县");
        codeKeyMap.put("610423","泾阳县");
        codeKeyMap.put("610424","乾县");
        codeKeyMap.put("610425","礼泉县");
        codeKeyMap.put("610426","永寿县");
        codeKeyMap.put("610428","长武县");
        codeKeyMap.put("610429","旬邑县");
        codeKeyMap.put("610430","淳化县");
        codeKeyMap.put("610431","武功县");
        codeKeyMap.put("610481","兴平");
        codeKeyMap.put("610482","彬州");
        codeKeyMap.put("610500","渭南");
        codeKeyMap.put("610502","临渭区");
        codeKeyMap.put("610503","华州区");
        codeKeyMap.put("610522","潼关县");
        codeKeyMap.put("610523","大荔县");
        codeKeyMap.put("610524","合阳县");
        codeKeyMap.put("610525","澄城县");
        codeKeyMap.put("610526","蒲城县");
        codeKeyMap.put("610527","白水县");
        codeKeyMap.put("610528","富平县");
        codeKeyMap.put("610581","韩城");
        codeKeyMap.put("610582","华阴");
        codeKeyMap.put("610600","延安");
        codeKeyMap.put("610602","宝塔区");
        codeKeyMap.put("610603","安塞区");
        codeKeyMap.put("610621","延长县");
        codeKeyMap.put("610622","延川县");
        codeKeyMap.put("610623","子长县");
        codeKeyMap.put("610625","志丹县");
        codeKeyMap.put("610626","吴起县");
        codeKeyMap.put("610627","甘泉县");
        codeKeyMap.put("610628","富县");
        codeKeyMap.put("610629","洛川县");
        codeKeyMap.put("610630","宜川县");
        codeKeyMap.put("610631","黄龙县");
        codeKeyMap.put("610632","黄陵县");
        codeKeyMap.put("610700","汉中");
        codeKeyMap.put("610702","汉台区");
        codeKeyMap.put("610703","南郑区");
        codeKeyMap.put("610722","城固县");
        codeKeyMap.put("610723","洋县");
        codeKeyMap.put("610724","西乡县");
        codeKeyMap.put("610725","勉县");
        codeKeyMap.put("610726","宁强县");
        codeKeyMap.put("610727","略阳县");
        codeKeyMap.put("610728","镇巴县");
        codeKeyMap.put("610729","留坝县");
        codeKeyMap.put("610730","佛坪县");
        codeKeyMap.put("610800","榆林");
        codeKeyMap.put("610802","榆阳区");
        codeKeyMap.put("610803","横山区");
        codeKeyMap.put("610822","府谷县");
        codeKeyMap.put("610824","靖边县");
        codeKeyMap.put("610825","定边县");
        codeKeyMap.put("610826","绥德县");
        codeKeyMap.put("610827","米脂县");
        codeKeyMap.put("610828","佳县");
        codeKeyMap.put("610829","吴堡县");
        codeKeyMap.put("610830","清涧县");
        codeKeyMap.put("610831","子洲县");
        codeKeyMap.put("610881","神木");
        codeKeyMap.put("610900","安康");
        codeKeyMap.put("610902","汉滨区");
        codeKeyMap.put("610921","汉阴县");
        codeKeyMap.put("610922","石泉县");
        codeKeyMap.put("610923","宁陕县");
        codeKeyMap.put("610924","紫阳县");
        codeKeyMap.put("610925","岚皋县");
        codeKeyMap.put("610926","平利县");
        codeKeyMap.put("610927","镇坪县");
        codeKeyMap.put("610928","旬阳县");
        codeKeyMap.put("610929","白河县");
        codeKeyMap.put("611000","商洛");
        codeKeyMap.put("611002","商州区");
        codeKeyMap.put("611021","洛南县");
        codeKeyMap.put("611022","丹凤县");
        codeKeyMap.put("611023","商南县");
        codeKeyMap.put("611024","山阳县");
        codeKeyMap.put("611025","镇安县");
        codeKeyMap.put("611026","柞水县");
        codeKeyMap.put("620000","甘肃");
        codeKeyMap.put("620100","兰州");
        codeKeyMap.put("620102","城关区");
        codeKeyMap.put("620103","七里河区");
        codeKeyMap.put("620104","西固区");
        codeKeyMap.put("620105","安宁区");
        codeKeyMap.put("620111","红古区");
        codeKeyMap.put("620121","永登县");
        codeKeyMap.put("620122","皋兰县");
        codeKeyMap.put("620123","榆中县");
        codeKeyMap.put("620171","兰州新区");
        codeKeyMap.put("620200","嘉峪关");
        codeKeyMap.put("620201100","新城镇");
        codeKeyMap.put("620201101","峪泉镇");
        codeKeyMap.put("620201102","文殊镇");
        codeKeyMap.put("620201401","雄关区");
        codeKeyMap.put("620201402","镜铁区");
        codeKeyMap.put("620201403","长城区");
        codeKeyMap.put("620300","金昌");
        codeKeyMap.put("620302","金川区");
        codeKeyMap.put("620321","永昌县");
        codeKeyMap.put("620400","白银");
        codeKeyMap.put("620402","白银区");
        codeKeyMap.put("620403","平川区");
        codeKeyMap.put("620421","靖远县");
        codeKeyMap.put("620422","会宁县");
        codeKeyMap.put("620423","景泰县");
        codeKeyMap.put("620500","天水");
        codeKeyMap.put("620502","秦州区");
        codeKeyMap.put("620503","麦积区");
        codeKeyMap.put("620521","清水县");
        codeKeyMap.put("620522","秦安县");
        codeKeyMap.put("620523","甘谷县");
        codeKeyMap.put("620524","武山县");
        codeKeyMap.put("620525","张家川回族自治县");
        codeKeyMap.put("620600","武威");
        codeKeyMap.put("620602","凉州区");
        codeKeyMap.put("620621","民勤县");
        codeKeyMap.put("620622","古浪县");
        codeKeyMap.put("620623","天祝藏族自治县");
        codeKeyMap.put("620700","张掖");
        codeKeyMap.put("620702","甘州区");
        codeKeyMap.put("620721","肃南裕固族自治县");
        codeKeyMap.put("620722","民乐县");
        codeKeyMap.put("620723","临泽县");
        codeKeyMap.put("620724","高台县");
        codeKeyMap.put("620725","山丹县");
        codeKeyMap.put("620800","平凉");
        codeKeyMap.put("620802","崆峒区");
        codeKeyMap.put("620821","泾川县");
        codeKeyMap.put("620822","灵台县");
        codeKeyMap.put("620823","崇信县");
        codeKeyMap.put("620825","庄浪县");
        codeKeyMap.put("620826","静宁县");
        codeKeyMap.put("620881","华亭");
        codeKeyMap.put("620900","酒泉");
        codeKeyMap.put("620902","肃州区");
        codeKeyMap.put("620921","金塔县");
        codeKeyMap.put("620922","瓜州县");
        codeKeyMap.put("620923","肃北蒙古族自治县");
        codeKeyMap.put("620924","阿克塞哈萨克族自治县");
        codeKeyMap.put("620981","玉门");
        codeKeyMap.put("620982","敦煌");
        codeKeyMap.put("621000","庆阳");
        codeKeyMap.put("621002","西峰区");
        codeKeyMap.put("621021","庆城县");
        codeKeyMap.put("621022","环县");
        codeKeyMap.put("621023","华池县");
        codeKeyMap.put("621024","合水县");
        codeKeyMap.put("621025","正宁县");
        codeKeyMap.put("621026","宁县");
        codeKeyMap.put("621027","镇原县");
        codeKeyMap.put("621100","定西");
        codeKeyMap.put("621102","安定区");
        codeKeyMap.put("621121","通渭县");
        codeKeyMap.put("621122","陇西县");
        codeKeyMap.put("621123","渭源县");
        codeKeyMap.put("621124","临洮县");
        codeKeyMap.put("621125","漳县");
        codeKeyMap.put("621126","岷县");
        codeKeyMap.put("621200","陇南");
        codeKeyMap.put("621202","武都区");
        codeKeyMap.put("621221","成县");
        codeKeyMap.put("621222","文县");
        codeKeyMap.put("621223","宕昌县");
        codeKeyMap.put("621224","康县");
        codeKeyMap.put("621225","西和县");
        codeKeyMap.put("621226","礼县");
        codeKeyMap.put("621227","徽县");
        codeKeyMap.put("621228","两当县");
        codeKeyMap.put("622900","临夏回族自治州");
        codeKeyMap.put("622901","临夏");
        codeKeyMap.put("622921","临夏县");
        codeKeyMap.put("622922","康乐县");
        codeKeyMap.put("622923","永靖县");
        codeKeyMap.put("622924","广河县");
        codeKeyMap.put("622925","和政县");
        codeKeyMap.put("622926","东乡族自治县");
        codeKeyMap.put("622927","积石山保安族东乡族撒拉族自治县");
        codeKeyMap.put("623000","甘南藏族自治州");
        codeKeyMap.put("623001","合作");
        codeKeyMap.put("623021","临潭县");
        codeKeyMap.put("623022","卓尼县");
        codeKeyMap.put("623023","舟曲县");
        codeKeyMap.put("623024","迭部县");
        codeKeyMap.put("623025","玛曲县");
        codeKeyMap.put("623026","碌曲县");
        codeKeyMap.put("623027","夏河县");
        codeKeyMap.put("630000","青海");
        codeKeyMap.put("630100","西宁");
        codeKeyMap.put("630102","城东区");
        codeKeyMap.put("630103","城中区");
        codeKeyMap.put("630104","城西区");
        codeKeyMap.put("630105","城北区");
        codeKeyMap.put("630121","大通回族土族自治县");
        codeKeyMap.put("630122","湟中县");
        codeKeyMap.put("630123","湟源县");
        codeKeyMap.put("630200","海东");
        codeKeyMap.put("630202","乐都区");
        codeKeyMap.put("630203","平安区");
        codeKeyMap.put("630222","民和回族土族自治县");
        codeKeyMap.put("630223","互助土族自治县");
        codeKeyMap.put("630224","化隆回族自治县");
        codeKeyMap.put("630225","循化撒拉族自治县");
        codeKeyMap.put("632200","海北藏族自治州");
        codeKeyMap.put("632221","门源回族自治县");
        codeKeyMap.put("632222","祁连县");
        codeKeyMap.put("632223","海晏县");
        codeKeyMap.put("632224","刚察县");
        codeKeyMap.put("632300","黄南藏族自治州");
        codeKeyMap.put("632321","同仁县");
        codeKeyMap.put("632322","尖扎县");
        codeKeyMap.put("632323","泽库县");
        codeKeyMap.put("632324","河南蒙古族自治县");
        codeKeyMap.put("632500","海南藏族自治州");
        codeKeyMap.put("632521","共和县");
        codeKeyMap.put("632522","同德县");
        codeKeyMap.put("632523","贵德县");
        codeKeyMap.put("632524","兴海县");
        codeKeyMap.put("632525","贵南县");
        codeKeyMap.put("632600","果洛藏族自治州");
        codeKeyMap.put("632621","玛沁县");
        codeKeyMap.put("632622","班玛县");
        codeKeyMap.put("632623","甘德县");
        codeKeyMap.put("632624","达日县");
        codeKeyMap.put("632625","久治县");
        codeKeyMap.put("632626","玛多县");
        codeKeyMap.put("632700","玉树藏族自治州");
        codeKeyMap.put("632701","玉树");
        codeKeyMap.put("632722","杂多县");
        codeKeyMap.put("632723","称多县");
        codeKeyMap.put("632724","治多县");
        codeKeyMap.put("632725","囊谦县");
        codeKeyMap.put("632726","曲麻莱县");
        codeKeyMap.put("632800","海西蒙古族藏族自治州");
        codeKeyMap.put("632801","格尔木");
        codeKeyMap.put("632802","德令哈");
        codeKeyMap.put("632803","茫崖");
        codeKeyMap.put("632821","乌兰县");
        codeKeyMap.put("632822","都兰县");
        codeKeyMap.put("632823","天峻县");
        codeKeyMap.put("632857","大柴旦行政委员会");
        codeKeyMap.put("640000","宁夏回族自治区");
        codeKeyMap.put("640100","银川");
        codeKeyMap.put("640104","兴庆区");
        codeKeyMap.put("640105","西夏区");
        codeKeyMap.put("640106","金凤区");
        codeKeyMap.put("640121","永宁县");
        codeKeyMap.put("640122","贺兰县");
        codeKeyMap.put("640181","灵武");
        codeKeyMap.put("640200","石嘴山");
        codeKeyMap.put("640202","大武口区");
        codeKeyMap.put("640205","惠农区");
        codeKeyMap.put("640221","平罗县");
        codeKeyMap.put("640300","吴忠");
        codeKeyMap.put("640302","利通区");
        codeKeyMap.put("640303","红寺堡区");
        codeKeyMap.put("640323","盐池县");
        codeKeyMap.put("640324","同心县");
        codeKeyMap.put("640381","青铜峡");
        codeKeyMap.put("640400","固原");
        codeKeyMap.put("640402","原州区");
        codeKeyMap.put("640422","西吉县");
        codeKeyMap.put("640423","隆德县");
        codeKeyMap.put("640424","泾源县");
        codeKeyMap.put("640425","彭阳县");
        codeKeyMap.put("640500","中卫");
        codeKeyMap.put("640502","沙坡头区");
        codeKeyMap.put("640521","中宁县");
        codeKeyMap.put("640522","海原县");
        codeKeyMap.put("650000","新疆维吾尔自治区");
        codeKeyMap.put("650100","乌鲁木齐");
        codeKeyMap.put("650102","天山区");
        codeKeyMap.put("650103","沙依巴克区");
        codeKeyMap.put("650104","新区");
        codeKeyMap.put("650105","水磨沟区");
        codeKeyMap.put("650106","头屯河区");
        codeKeyMap.put("650107","达坂城区");
        codeKeyMap.put("650109","米东区");
        codeKeyMap.put("650121","乌鲁木齐县");
        codeKeyMap.put("650171","乌鲁木齐经济技术开发区");
        codeKeyMap.put("650172","乌鲁木齐高新技术产业开发区");
        codeKeyMap.put("650200","克拉玛依");
        codeKeyMap.put("650202","独山子区");
        codeKeyMap.put("650203","克拉玛依区");
        codeKeyMap.put("650204","白碱滩区");
        codeKeyMap.put("650205","乌尔禾区");
        codeKeyMap.put("650400","吐鲁番");
        codeKeyMap.put("650402","高昌区");
        codeKeyMap.put("650421","鄯善县");
        codeKeyMap.put("650422","托克逊县");
        codeKeyMap.put("650500","哈密");
        codeKeyMap.put("650502","伊州区");
        codeKeyMap.put("650521","巴里坤哈萨克自治县");
        codeKeyMap.put("650522","伊吾县");
        codeKeyMap.put("652300","昌吉回族自治州");
        codeKeyMap.put("652301","昌吉");
        codeKeyMap.put("652302","阜康");
        codeKeyMap.put("652323","呼图壁县");
        codeKeyMap.put("652324","玛纳斯县");
        codeKeyMap.put("652325","奇台县");
        codeKeyMap.put("652327","吉木萨尔县");
        codeKeyMap.put("652328","木垒哈萨克自治县");
        codeKeyMap.put("652700","博尔塔拉蒙古自治州");
        codeKeyMap.put("652701","博乐");
        codeKeyMap.put("652702","阿拉山口");
        codeKeyMap.put("652722","精河县");
        codeKeyMap.put("652723","温泉县");
        codeKeyMap.put("652800","巴音郭楞蒙古自治州");
        codeKeyMap.put("652801","库尔勒");
        codeKeyMap.put("652822","轮台县");
        codeKeyMap.put("652823","尉犁县");
        codeKeyMap.put("652824","若羌县");
        codeKeyMap.put("652825","且末县");
        codeKeyMap.put("652826","焉耆回族自治县");
        codeKeyMap.put("652827","和静县");
        codeKeyMap.put("652828","和硕县");
        codeKeyMap.put("652829","博湖县");
        codeKeyMap.put("652871","库尔勒经济技术开发区");
        codeKeyMap.put("652900","阿克苏地区");
        codeKeyMap.put("652901","阿克苏");
        codeKeyMap.put("652922","温宿县");
        codeKeyMap.put("652923","库车县");
        codeKeyMap.put("652924","沙雅县");
        codeKeyMap.put("652925","新和县");
        codeKeyMap.put("652926","拜城县");
        codeKeyMap.put("652927","乌什县");
        codeKeyMap.put("652928","阿瓦提县");
        codeKeyMap.put("652929","柯坪县");
        codeKeyMap.put("653000","克孜勒苏柯尔克孜自治州");
        codeKeyMap.put("653001","阿图什");
        codeKeyMap.put("653022","阿克陶县");
        codeKeyMap.put("653023","阿合奇县");
        codeKeyMap.put("653024","乌恰县");
        codeKeyMap.put("653100","喀什地区");
        codeKeyMap.put("653101","喀什");
        codeKeyMap.put("653121","疏附县");
        codeKeyMap.put("653122","疏勒县");
        codeKeyMap.put("653123","英吉沙县");
        codeKeyMap.put("653124","泽普县");
        codeKeyMap.put("653125","莎车县");
        codeKeyMap.put("653126","叶城县");
        codeKeyMap.put("653127","麦盖提县");
        codeKeyMap.put("653128","岳普湖县");
        codeKeyMap.put("653129","伽师县");
        codeKeyMap.put("653130","巴楚县");
        codeKeyMap.put("653131","塔什库尔干塔吉克自治县");
        codeKeyMap.put("653200","和田地区");
        codeKeyMap.put("653201","和田");
        codeKeyMap.put("653221","和田县");
        codeKeyMap.put("653222","墨玉县");
        codeKeyMap.put("653223","皮山县");
        codeKeyMap.put("653224","洛浦县");
        codeKeyMap.put("653225","策勒县");
        codeKeyMap.put("653226","于田县");
        codeKeyMap.put("653227","民丰县");
        codeKeyMap.put("654000","伊犁哈萨克自治州");
        codeKeyMap.put("654002","伊宁");
        codeKeyMap.put("654003","奎屯");
        codeKeyMap.put("654004","霍尔果斯");
        codeKeyMap.put("654021","伊宁县");
        codeKeyMap.put("654022","察布查尔锡伯自治县");
        codeKeyMap.put("654023","霍城县");
        codeKeyMap.put("654024","巩留县");
        codeKeyMap.put("654025","新源县");
        codeKeyMap.put("654026","昭苏县");
        codeKeyMap.put("654027","特克斯县");
        codeKeyMap.put("654028","尼勒克县");
        codeKeyMap.put("654200","塔城地区");
        codeKeyMap.put("654201","塔城");
        codeKeyMap.put("654202","乌苏");
        codeKeyMap.put("654221","额敏县");
        codeKeyMap.put("654223","沙湾县");
        codeKeyMap.put("654224","托里县");
        codeKeyMap.put("654225","裕民县");
        codeKeyMap.put("654226","和布克赛尔蒙古自治县");
        codeKeyMap.put("654300","阿勒泰地区");
        codeKeyMap.put("654301","阿勒泰");
        codeKeyMap.put("654321","布尔津县");
        codeKeyMap.put("654322","富蕴县");
        codeKeyMap.put("654323","福海县");
        codeKeyMap.put("654324","哈巴河县");
        codeKeyMap.put("654325","青河县");
        codeKeyMap.put("654326","吉木乃县");
        codeKeyMap.put("659000","自治区直辖县级行政区划");
        codeKeyMap.put("659001","石河子");
        codeKeyMap.put("659002","阿拉尔");
        codeKeyMap.put("659003","图木舒克");
        codeKeyMap.put("659004","五家渠");
        codeKeyMap.put("659006","铁门关");
        codeKeyMap.put("710000","台湾");
        codeKeyMap.put("710100","台湾");
        codeKeyMap.put("710101","台湾");
        codeKeyMap.put("810000","香港特别行政区");
        codeKeyMap.put("810100","香港");
        codeKeyMap.put("810101","香港");
        codeKeyMap.put("820000","澳门特别行政区");
        codeKeyMap.put("820100","澳门");
        codeKeyMap.put("820101","澳门");
        codeKeyMap.put("900000","海外");
        codeKeyMap.put("900100","海外");
        codeKeyMap.put("900101","海外");
    }

    public static Integer getCodeByCity(String city){
        return Integer.parseInt(cityKeyMap.get(city));
    }
    public static String getCityByCode(String code){
        return codeKeyMap.get(code);
    }

}