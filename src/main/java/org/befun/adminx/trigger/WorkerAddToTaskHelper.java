package org.befun.adminx.trigger;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.WorkerType;
import org.befun.adminx.dto.WorkerDto;
import org.befun.task.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class WorkerAddToTaskHelper implements TransactionSynchronization {

    @Autowired
    private TaskService taskService;

    private final ThreadLocal<List<Long>> localTask = ThreadLocal.withInitial(ArrayList::new);
    private final ThreadLocal<List<Long>> localEvent = ThreadLocal.withInitial(ArrayList::new);

    public void addTaskLocal(Long taskId) {
        List<Long> events = localTask.get();
        if (events != null) { // 这里永远都不会是null
            events.add(taskId);
        }
    }

    public void addEventLocal(Long taskId) {
        List<Long> events = localEvent.get();
        if (events != null) { // 这里永远都不会是null
            events.add(taskId);
        }
    }

    @Override
    public void afterCompletion(int status) {
        if (status == STATUS_COMMITTED) {
            Optional.ofNullable(localTask.get()).ifPresent(list -> list.forEach(this::addTask));
            Optional.ofNullable(localEvent.get()).ifPresent(list -> list.forEach(this::addEvent));
        }
        localTask.remove();
        localEvent.remove();
    }

    private void addEvent(Long id) {
        WorkerDto taskDto = new WorkerDto();
        taskDto.setType(WorkerType.EVENT);
        taskDto.setWorkerId(id);
        taskDto.setConsumerAsync(false);
        taskDto.setWithNewPool(false);
        taskService.addTask(WorkerType.EVENT.getQueue(), taskDto);
    }

    private void addTask(Long id) {
        WorkerDto taskDto = new WorkerDto();
        taskDto.setType(WorkerType.TASK);
        taskDto.setWorkerId(id);
        taskDto.setConsumerAsync(false);
        taskDto.setWithNewPool(false);
        taskService.addTask(WorkerType.TASK.getQueue(), taskDto);
    }
}
