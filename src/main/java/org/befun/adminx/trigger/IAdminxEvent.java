package org.befun.adminx.trigger;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public interface IAdminxEvent {

    default List<IAdminxEventConsumer> getConsumers() {
        return null;
    }

    default void foreachConsumers(Consumer<IAdminxEventConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }


    default void adminxQuote(Long surveyId) {
        foreachConsumers(c -> c.adminxQuote(surveyId));
    }

    default void adminxRefund(Long surveyId) {
        foreachConsumers(c -> c.adminxRefund(surveyId));
    }


}
