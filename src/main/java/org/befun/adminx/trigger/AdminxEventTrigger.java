package org.befun.adminx.trigger;

import org.befun.adminx.dto.event.EventType;
import org.befun.adminx.dto.task.TaskType;
import org.befun.adminx.entity.CemEvent;
import org.befun.adminx.entity.CemTask;
import org.befun.adminx.repository.CemEventRepository;
import org.befun.adminx.repository.CemTaskRepository;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Component
public class AdminxEventTrigger {

    @Autowired
    private CemEventRepository cemEventRepository;
    @Autowired
    private CemTaskRepository cemTaskRepository;
    @Autowired
    private WorkerAddToTaskHelper workerAddToTaskHelper;

    public void triggerEvent(Long id) {
        TransactionSynchronizationManager.registerSynchronization(workerAddToTaskHelper);
        workerAddToTaskHelper.addEventLocal(id);
    }

    public void triggerTask(Long id) {
        TransactionSynchronizationManager.registerSynchronization(workerAddToTaskHelper);
        workerAddToTaskHelper.addTaskLocal(id);
    }

    public void addEventAndTrigger(Long orgId, String source, EventType eventType, Object data) {
        CemEvent entity = new CemEvent();
        entity.setOrgId(orgId);
        entity.setType(eventType);
        entity.setContent(JsonHelper.toJson(data));
        entity.setSource(source);
        cemEventRepository.save(entity);
        triggerEvent(entity.getId());
    }

    public void addTaskAndTrigger(Long orgId, String source, TaskType taskType, Object data) {
        CemTask entity = new CemTask();
        entity.setOrgId(orgId);
        entity.setType(taskType);
        entity.setContent(JsonHelper.toJson(data));
        entity.setSource(source);
        cemTaskRepository.save(entity);
        triggerTask(entity.getId());
    }
}
