CREATE TABLE `sample_order` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `s_id` bigint NOT NULL COMMENT '问卷id',
                `org_id` bigint DEFAULT NULL COMMENT '企业org_id',
                `survey_title` varchar(100) DEFAULT NULL COMMENT '问卷别名',
                `survey_real_title` varchar(100) DEFAULT NULL COMMENT '问卷标题',
                `channel_id` bigint NOT NULL COMMENT '渠道id',
                `channel_name` varchar(100) DEFAULT NULL COMMENT '渠道名称',
                `channel_status` TINYINT(2) DEFAULT NULL COMMENT '渠道状态',
                `count_question` int(10) DEFAULT '0' COMMENT '问卷题目数量',
                `quantity` int(4) DEFAULT '0' COMMENT '样本数量',
                `unit_price` int(10) DEFAULT '0' COMMENT '样本单价',
                `total_price` varchar(10) DEFAULT NULL COMMENT '订单总价',
                `contacts` varchar(10) DEFAULT NULL COMMENT '跟进人',
                `is_contacted` TINYINT(2) default 0 COMMENT '是否联系，0未联系，1已联系',
                `gender_match` varchar(50) DEFAULT NULL COMMENT '样本特征-性别',
                `age_match` varchar(100) DEFAULT NULL COMMENT '样本特征-年龄',
                `education_match` varchar(100) DEFAULT NULL COMMENT '样本特征-学历',
                `location_match` varchar(200) DEFAULT NULL COMMENT '样本特征-地区',
                `other` varchar(1000) DEFAULT NULL COMMENT '其他',
                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COMMENT='样本订单';