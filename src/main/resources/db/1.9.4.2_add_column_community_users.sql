ALTER TABLE surveyapi.community_user_additional
    ADD COLUMN `latitude` FLOAT(10, 8) DEFAULT NULL COMMENT '地理位置纬度' AFTER `education_modified`,
    ADD COLUMN `longitude` FLOAT(11, 8) DEFAULT NULL COMMENT '地理位置经度' AFTER `latitude`,
    ADD COLUMN `province` varchar(50) DEFAULT NULL COMMENT '省' AFTER `longitude`,
    ADD COLUMN `city` varchar(50) DEFAULT NULL COMMENT '市' AFTER `province`,
    ADD COLUMN `area` varchar(50) DEFAULT NULL COMMENT '区' AFTER `city`;

ALTER TABLE adminx.delivery_task
    ADD COLUMN `short_url` varchar(100) DEFAULT NULL COMMENT '问卷短链接' AFTER `survey_url`,
	ADD COLUMN `title` varchar(255) DEFAULT NULL COMMENT '问卷别名' AFTER `name`;

update adminx.delivery_task set `title` = `name` where `title` is NULL;