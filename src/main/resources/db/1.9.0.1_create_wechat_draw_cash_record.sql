CREATE TABLE adminx.`wechat_draw_cash_record` (
                      `id` bigint NOT NULL AUTO_INCREMENT,
                      `openid` varchar(100) NOT NULL DEFAULT '' COMMENT '微信openid',
                      `type` tinyint(2) DEFAULT '0' COMMENT '0现金红包，1提现到余额',
                      `amount` int(10) default 0 comment '金额',
                      `mch_billno` varchar(50) default null comment '微信订单号',
                      `return_code` varchar(50) default null comment '返回状态码',
                      `return_msg` varchar(50) default null comment '返回信息',
                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                      `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                      PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT '微信提现记录';