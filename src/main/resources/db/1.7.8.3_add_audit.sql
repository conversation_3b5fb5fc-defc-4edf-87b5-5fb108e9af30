CREATE TABLE `audit` (
                 `id` bigint NOT NULL COMMENT 'id',
                 `create_time` timestamp NULL DEFAULT NULL,
                 `modify_time` timestamp NULL DEFAULT NULL,
                 `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
                 `audit_type` int DEFAULT '0',
                 `code` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL,
                 `language` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
                 `rules` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL,
                 `threshold` int DEFAULT '0',
                 `s_id` bigint DEFAULT NULL,
                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;