ALTER TABLE `adminx`.`delivery_task`
    ADD COLUMN `task_type` int NULL DEFAULT 0 COMMENT '任务类型，0：投放任务，1：追访任务' AFTER `invite_score`;


ALTER TABLE `adminx`.`audit_record`
    ADD COLUMN `task_type` int NULL DEFAULT 0 COMMENT '任务类型，0：投放任务，1：追访任务' AFTER `type`;


ALTER TABLE `adminx`.`survey_send_record`
    MODIFY COLUMN `send_count` int NOT NULL DEFAULT 0 COMMENT '发送微信次数' AFTER `send_url`,
    ADD COLUMN `task_type` tinyint NOT NULL COMMENT '任务模式，任务、追访' AFTER `content`,
    ADD COLUMN `send_method` tinyint NOT NULL COMMENT '发送方式，微信、短信' AFTER `send_count`,
    ADD COLUMN `send_wechat_count` int NOT NULL DEFAULT 0 COMMENT '微信服务号发送成功次数' AFTER `send_method`,
    ADD COLUMN `send_message_count` int NOT NULL DEFAULT 0 COMMENT '发送短信成功次数' AFTER `send_wechat_count`,
    ADD COLUMN `submit_status` tinyint NOT NULL COMMENT '最后一次填答状态（未访问、未提交、提交）' AFTER `send_message_count`,
    ADD COLUMN `subscribe_status` tinyint NOT NULL COMMENT '最后一次关注状态（关注、取关,未关注）' AFTER `submit_status`,
    ADD COLUMN `phone_status` tinyint NOT NULL COMMENT '手机号状态（绑定、未绑定）' AFTER `subscribe_status`,
    ADD COLUMN `fail_msg` varchar(255) NULL COMMENT '最后一次失败原因' AFTER `phone_status`,
    ADD COLUMN `cuid` bigint NULL COMMENT 'cuid' AFTER `client_id`;

ALTER TABLE `adminx`.`survey_send_record`
    ADD COLUMN `base_line_sid` varchar(50) DEFAULT NULL COMMENT '基线问卷id' AFTER `type`,
    ADD COLUMN `base_line_rid` varchar(50) DEFAULT NULL COMMENT '基线答卷id' AFTER `base_line_sid`,
    ADD COLUMN `response_status` tinyint(2) DEFAULT NULL COMMENT '审核状态（未提交、审核通过、提前结束、待审核、审核不通过、配额已满）' AFTER `base_line_rid`,
    ADD COLUMN `points` int(10) DEFAULT NULL COMMENT '积分奖励' AFTER `response_status`;

DROP INDEX `client_id` ON `adminx`.`survey_send_record`;
ALTER TABLE `adminx`.`survey_send_record` ADD UNIQUE INDEX idx_client_id (`client_id`);