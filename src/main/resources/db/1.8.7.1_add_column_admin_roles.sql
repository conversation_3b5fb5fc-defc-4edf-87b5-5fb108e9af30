alter table adminx.admin add column `roles` varchar(255) COLLATE utf8mb4_general_ci NOT NULL comment '角色权限';
truncate table adminx.admin;

INSERT INTO `admin` (`id`, `create_time`, `modify_time`, `user_name`, `password`, `roles`) VALUES (1, '2022-03-03 00:55:37', '2022-03-03 00:55:41', 'admin', 'fa80908ab5c0cdb263e322552ad603dd', 'admin;sp_operator');
INSERT INTO `admin` (`id`, `create_time`, `modify_time`, `user_name`, `password`, `roles`) VALUES (2, '2022-03-03 00:55:41', '2022-11-28 14:21:58', '<EMAIL>', '0bd5966affdd2a06f07d92ba169d6489', 'sp_admin');
INSERT INTO `admin` (`id`, `create_time`, `modify_time`, `user_name`, `password`, `roles`) VALUES (3, '2022-03-03 00:55:41', '2022-03-03 00:55:41', 'manage_hy_2022', 'a51009b94c2a0b5ae873e917a8f1f682', 'xmp_operator');
INSERT INTO `admin` (`id`, `create_time`, `modify_time`, `user_name`, `password`, `roles`) VALUES (4, '2022-03-03 00:55:41', '2022-03-03 00:55:41', 'ops', '21232f297a57a5a743894a0e4a801fc3', 'sp_operator;xmp_operator');

