CREATE TABLE `survey_send_record` (
                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                          `name` varchar(45) DEFAULT '' COMMENT '接收人姓名',
                          `client_id` varchar(100) DEFAULT NULL COMMENT '客户端id',
                          `account` varchar(64) NOT NULL DEFAULT '' COMMENT '接收人账号(微信openid或者手机号)',
                          `content` varchar(500) NOT NULL DEFAULT '' COMMENT '推送内容',
                          `send_url` varchar(200) NOT NULL DEFAULT '' COMMENT '问卷地址',
                          `send_count` int NOT NULL DEFAULT '0' COMMENT '发送次数',
                          `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                          `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '完成状态(0:未完成,1:已完成,2:失败)',
                          `reply_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '答题状态(0:未访问,1:未提交,2:已提交)',
                          `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型,0问卷接受通知,1积分变动通知',
                          `result` varchar(200) NOT NULL DEFAULT '' COMMENT '发送通知返回结果',
                          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                          `s_id` bigint DEFAULT NULL COMMENT '问卷id',
                          `cuid` bigint DEFAULT NULL COMMENT '用户id',
                          `task_id` bigint DEFAULT NULL COMMENT '任务id',
                          PRIMARY KEY (`id`),
                          KEY `s_id` (`s_id`),
                          KEY `task_id` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1967191692605441 DEFAULT CHARSET=utf8mb4  COMMENT='微信通知推送记录表';