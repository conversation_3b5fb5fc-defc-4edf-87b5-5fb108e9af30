CREATE TABLE `invite_award_record` (
               `id` bigint NOT NULL AUTO_INCREMENT,
               `cuid` bigint NOT NULL COMMENT '社区cuid',
               `invite_id` bigint NOT NULL COMMENT '邀请人cuid',
               `score` int(10) NOT NULL COMMENT '奖励积分',
               `score_type` tinyint DEFAULT '0' COMMENT '奖励类型',
               `task_id` bigint NOT NULL COMMENT '任务id',
               `expire_time` timestamp NOT NULL COMMENT '过期时间',
               `status` tinyint(2) default '0' COMMENT '领取状态 0未领取，1已领取',
               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
               PRIMARY KEY (`id`),
               UNIQUE KEY unique_cuid (`cuid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='邀请奖励记录';

alter table surveyapi.`community_users` add column `survey_count` int(10) DEFAULT NULL COMMENT '问卷填写总数量' after `surveys_completed_count`;

INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `type`, `create_time`, `modify_time`) VALUES (7, '验证码通知', NULL, '您的验证码是${verifyCode}，请于5分钟内填写。如非本人操作，请忽略本短信', '[{\"name\": \"verifyCode\", \"title\": \"验证码\"}]', '976366', '976366', 1, 1, 2, '2023-08-08 13:59:14', '2023-08-08 11:09:07');
