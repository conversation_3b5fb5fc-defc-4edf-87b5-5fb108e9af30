CREATE TABLE adminx.`survey_statistics` (
            `id` bigint NOT NULL AUTO_INCREMENT,
            `date` char(10) NOT NULL COMMENT '日期',
            `cuid` bigint NOT NULL COMMENT '用户cuid',
            `total` int NOT NULL DEFAULT '0' COMMENT '问卷打开次数',
            `completed` int NOT NULL DEFAULT '0' COMMENT '问卷完成次数',
            `quota_full` int NOT NULL DEFAULT '0' COMMENT '配额结束次数',
            `early_completed` int NOT NULL DEFAULT '0' COMMENT '提前结束次数',
            `audit_pass` int NOT NULL DEFAULT '0' COMMENT '问卷通过次数',
            `audit_fail` int NOT NULL DEFAULT '0' COMMENT '问卷不通过次数',
            `completed_rate` int NOT NULL DEFAULT '0' COMMENT '问卷完成率(问卷完成次数/问卷打开次数)',
            `pass_rate` int NOT NULL DEFAULT '0' COMMENT '问卷通过率(问卷通过次数/问卷完成次数)',
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `date_cuid` (`date`,`cuid`)
) ENGINE=InnoDB AUTO_INCREMENT= 0 DEFAULT CHARSET=utf8mb4 COMMENT '社区用户问卷填答数据统计';