CREATE TABLE `survey_send_record` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `task_id` bigint DEFAULT '0' COMMENT '任务id',
                                      `s_id` varchar(30) DEFAULT '' COMMENT '问卷id',
                                      `client_id` varchar(100) DEFAULT NULL COMMENT '客户端id',
                                      `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '接收人姓名',
                                      `account` varchar(64) NOT NULL DEFAULT '' COMMENT '接收人账号(微信openid或者手机号)',
                                      `content` varchar(500) NOT NULL DEFAULT '' COMMENT '推送内容',
                                      `send_url` varchar(200) NOT NULL DEFAULT '' COMMENT '问卷地址',
                                      `send_count` int NOT NULL DEFAULT '0' COMMENT '发送次数',
                                      `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                      `send_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态(0:待发送,1:发送成功,2:发送失败)',
                                      `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '完成状态(0:未完成,1:已完成)',
                                      `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '通知类型 0问卷接受通知,1积分变动通知',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      KEY `client_id` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2878343452825601 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问卷推送记录表';