ALTER TABLE `adminx`.`send_record`
    ADD COLUMN `operator` bigint NULL COMMENT '操作人' AFTER `white_list`,
ADD COLUMN `template_id` varchar(255) NULL COMMENT '微信模板id' AFTER `operator`,
ADD COLUMN `complete_count` int NULL COMMENT '推送微信模板完成人数' AFTER `templateId`,
ADD COLUMN `total_count` int NULL COMMENT '推送微信模板总人数' AFTER `completeCount`,
ADD COLUMN `send_status` varchar(32) NULL COMMENT '推送状态：推送失败，推送中，推送成功，' AFTER `totalCount`,
ADD COLUMN `Task_send_type` varchar(32) NULL COMMENT '推送任务类型:默认任务，模板推送' AFTER `sendStatus`;

ALTER TABLE `adminx`.`send_record`
    ADD COLUMN `message` varchar(1000) NULL COMMENT '记录推送成功、失败相关信息' AFTER `send_status`;