ALTER TABLE adminx.audit_record ADD COLUMN cuid BIGINT DEFAULT NULL COMMENT '用户cuid';
ALTER TABLE adminx.audit_record ADD COLUMN title VARCHAR(200) DEFAULT NULL COMMENT '问卷标题';
ALTER TABLE adminx.audit_record ADD COLUMN `s_id` BIGINT NOT NULL COMMENT '问卷id';
ALTER TABLE adminx.audit_record ADD COLUMN `score` int(10) DEFAULT NULL COMMENT '分数';
ALTER TABLE adminx.audit_record ADD COLUMN `fail_rule_name` varchar(100) DEFAULT NULL COMMENT '审核失败的规则名';
ALTER TABLE adminx.audit_record ADD COLUMN `log` varchar(1000) DEFAULT NULL COMMENT '审核失败日志';
ALTER TABLE adminx.audit_record ADD COLUMN `items` varchar(1000) DEFAULT NULL COMMENT '审核规则json';
ALTER TABLE adminx.audit_record ADD COLUMN `type` tinyint(1) DEFAULT '0' COMMENT '审核方式 0手动 1自动';