CREATE TABLE adminx.`customer_statistics` (
                      `org_id` bigint NOT NULL COMMENT '企业组织id',
                      `survey_quantity` int NOT NULL DEFAULT '0' COMMENT '问卷总数',
                      `survey_response_quantity` int NOT NULL DEFAULT '0' COMMENT '问卷回收总数',
                      `journey_map_quantity` int NOT NULL DEFAULT '0' COMMENT '旅程总数',
                      `event_quantity` int NOT NULL DEFAULT '0' COMMENT '预警总数',
                      `bi_dashboard_quantity` int NOT NULL DEFAULT '0' COMMENT 'BI看板总数',
                      `customer_quantity` int NOT NULL DEFAULT '0' COMMENT '客户总数',
                      `account_quantity` int NOT NULL DEFAULT '0' COMMENT '账号总数',
                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                      `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                      PRIMARY KEY (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '客户数据统计';