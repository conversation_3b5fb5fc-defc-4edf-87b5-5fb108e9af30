DROP FUNCTION IF exists diff_distance;

DELIMITER $$

CREATE  FUNCTION diff_distance(s1 TEXT, s2 TEXT)
    RETURNS FLOAT
    DETERMINISTIC
BEGIN
    DECLARE total, diff INT;
    -- 创建一个动态编程表格
    DECLARE part1, rest1, part2, rest2 VARCHAR(256);

    -- 初始化变量
		SET part1 = '';
		set rest1 = s1;
		SET part2 = '';
		set rest2 = s2;

		SET total = 0;
		SET diff = 0;
		WHILE CHAR_LENGTH(rest1) > 0 AND CHAR_LENGTH(rest2) > 0 DO
			SET part1 = SUBSTRING_INDEX(rest1, '^^', 1);
			SET rest1 = SUBSTRING(rest1, CHAR_LENGTH(SUBSTRING_INDEX(rest1, '^^', 1)) + 2);

			SET part2 = SUBSTRING_INDEX(rest2, '^^', 1);
			SET rest2 = SUBSTRING(rest2, CHAR_LENGTH(SUBSTRING_INDEX(rest2, '^^', 1)) + 2);

			set total = total + 1;
			IF part2 != part1 THEN
				set diff = diff + 1;
END IF;
END WHILE;

return 1 - ROUND(diff / total, 2);
END$$

DELIMITER ;