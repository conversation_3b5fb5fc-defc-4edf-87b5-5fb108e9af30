-- Create syntax for TABLE 'survey_audit_record'
CREATE TABLE `adminx`.`survey_audit_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint NULL DEFAULT NULL COMMENT '企业id',
  `survey_id` bigint NULL DEFAULT NULL COMMENT '问卷id',
  `source_id` bigint NULL DEFAULT NULL COMMENT '来源id: contentAudit, contentAuditId; report, responseId',
  `source` varchar(20) NULL DEFAULT NULL COMMENT '来源：contentAudit 内容审核，report 举报',
  `reason` varchar(128) DEFAULT NULL COMMENT '审核原因',
  `content` text COMMENT '审核内容',
  `request_time` datetime COMMENT '提交时间',
  `audit_time` datetime COMMENT '审核时间',
  `status` varchar(20) DEFAULT NULL COMMENT '审核结果：init 未审核、pass 通过、noPass 不通过、disabled 禁用问卷、blacklist 禁用账号',
  `remark` text DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='问卷审核记录';