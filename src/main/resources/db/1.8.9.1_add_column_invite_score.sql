alter table adminx.delivery_task add column enable_invite TINYINT(2) default '0' COMMENT '开启邀请积分奖励' after `score`;
alter table adminx.delivery_task add column invite_score int(10)  default '0' COMMENT '邀请积分' after `enable_invite`;

alter table surveyapi.community_users add column invite_award tinyint(2) default 0 comment '是否领取邀请奖励 1表示已经领取'  after `invite_id`;
alter table surveyapi.community_user_scores add column s_id varchar(50) default null comment '问卷id' after `type`;
alter table surveyapi.community_user_scores add column survey_title varchar(100) default null comment '问卷标题' after `s_id`;

CREATE TABLE adminx.`wechat_subscribe_record` (
              `id` bigint NOT NULL AUTO_INCREMENT,
              `cuid` BIGINT DEFAULT NULL COMMENT '社区cuid',
              `openid` varchar(100) NOT NULL DEFAULT '' COMMENT '微信openid',
              `type` tinyint(2) DEFAULT '0' COMMENT '0 取关，1关注',
              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
              PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT '微信关注取关记录';


update organization set app_types = '["cem","surveyplus"]' where version not like '%\"cem_version\":\"empty\"%' and version not like '%\"surveyplus_version\":\"empty\"%';
