CREATE TABLE `thirdparty_template` (
                   `id` bigint NOT NULL AUTO_INCREMENT,
                   `name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模版名称',
                   `description` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模版描述',
                   `example` text COLLATE utf8mb4_general_ci COMMENT '模版示例',
                   `parameters` json DEFAULT NULL COMMENT '三方模版参数',
                   `open_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方模版id',
                   `signature_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模版签名id',
                   `send_method` tinyint NOT NULL COMMENT '发送方式，微信、短信',
                   `status` int DEFAULT '1' COMMENT '模板状态：0 待审核 1 审核通过',
                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                   `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='自定义发送模版';

INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `create_time`, `modify_time`) VALUES (1, '追访模板1（未访问）', NULL, '您好，感谢您${projectTime}参与了由${projectManager}发起的${projectName}的调查。这是该项目的${projectRound}追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有${projectReward}元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${code}', '[{\"name\": \"projectTime\", \"title\": \"时间\"}, {\"name\": \"projectManager\", \"title\": \"委托方\"}, {\"name\": \"projectName\", \"title\": \"项目名\"}, {\"name\": \"projectRound\", \"title\": \"轮次\"}, {\"name\": \"projectReward\", \"title\": \"酬谢金\"}]', '932735', '932735', 1, 1, '2023-05-28 13:59:14', '2023-05-28 18:32:02');
INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `create_time`, `modify_time`) VALUES (2, '追访模板2（访问未提交）', NULL, '您好，感谢您${projectTime}参与了由${projectManager}发起的${projectName}的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的${projectReward}元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${code}', '[{\"name\": \"projectTime\", \"title\": \"时间\"}, {\"name\": \"projectManager\", \"title\": \"委托方\"}, {\"name\": \"projectName\", \"title\": \"项目名\"}, {\"name\": \"projectReward\", \"title\": \"酬谢金\"}]', '932736', '932736', 1, 1, '2023-05-28 13:59:14', '2023-05-28 18:32:23');
INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `create_time`, `modify_time`) VALUES (3, '追访酬金到账通知', NULL, '您好，感谢您最近参与了${projectName}调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！', '[{\"name\": \"projectName\", \"title\": \"项目名\"}]', '932737', '932737', 1, 1, '2023-05-28 13:59:14', '2023-05-28 18:32:28');
INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `create_time`, `modify_time`) VALUES (4, '问卷接收成功通知', NULL, '您好，问卷已经接收成功\n问卷主题：关于食堂就餐问题\n发起时间：2017年8月26日\n结束时间：2017年8月31日\n问卷详细内容请点击此信息进入查阅\n', '[{\"name\": \"first\", \"title\": \"开始语\"}, {\"name\": \"keyword1\", \"title\": \"问卷主题\"}, {\"name\": \"keyword2\", \"title\": \"发起时间\"}, {\"name\": \"keyword3\", \"title\": \"结束时间\"}, {\"name\": \"remark\", \"title\": \"结束语\"}]', '5MUNcVUuTtm2_DAuChj8Ur2WZyyIjtyeZvTZkM0neco', NULL, 0, 1, '2023-05-28 14:15:38', '2023-05-29 10:23:55');
INSERT INTO `adminx`.`thirdparty_template` (`id`, `name`, `description`, `example`, `parameters`, `open_id`, `signature_id`, `send_method`, `status`, `create_time`, `modify_time`) VALUES (5, '审核结果通知', NULL, '物料申请已审核通过\r\n审核信息：2张海报，2个展架，共100元\r\n审核人：李晓\r\n通过时间：2014-11-28 16:11\r\n感谢你的使用', '[{\"name\": \"first\", \"title\": \"开始语\"},{\"name\": \"keyword1\", \"title\": \"审核信息\"}, {\"name\": \"keyword2\", \"title\": \"审核人\"}, {\"name\": \"keyword3\", \"title\": \"通过时间\"}, {\"name\": \"remark\", \"title\": \"结束语\"}]', 'jw1rxIeougIEAibRilKkLt2wekfa1YWsjTw1YIIiG5s', NULL, 0, 1, '2023-05-28 14:17:19', '2023-05-28 14:17:22');