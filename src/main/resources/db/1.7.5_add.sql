ALTER TABLE adminx.delivery_task ADD COLUMN  `audit_wait_count` int unsigned NOT NULL DEFAULT '0' COMMENT '待审核人数';
ALTER TABLE adminx.delivery_task MODIFY COLUMN  `s_id` varchar(50) DEFAULT NULL COMMENT '问卷id';
ALTER TABLE adminx.delivery_task ADD COLUMN  `channel_id`BIGINT DEFAULT NULL COMMENT '渠道id';
ALTER TABLE adminx.delivery_task ADD COLUMN reply_count int unsigned NOT NULL DEFAULT '0' COMMENT '填答人数';
ALTER TABLE adminx.delivery_task ADD COLUMN type TINYINT(1) DEFAULT NULL COMMENT '问卷类型 0体验家问卷 1老调研家问卷';
ALTER TABLE adminx.delivery_task ADD COLUMN survey_status TINYINT(1) DEFAULT '0' COMMENT '问卷状态';
ALTER TABLE adminx.delivery_task ADD COLUMN `start_time` timestamp NULL DEFAULT NULL COMMENT '问卷调查开始时间';
ALTER TABLE adminx.delivery_task ADD COLUMN `over_time` timestamp NULL DEFAULT NULL COMMENT '问卷调查截止时间';
ALTER TABLE adminx.delivery_task ADD COLUMN `survey_url` varchar(200) DEFAULT NULL COMMENT '问卷地址';


CREATE TABLE adminx.survey_completed_record(
               `id` bigint NOT NULL COMMENT '主键',
               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
               `cuid` BIGINT DEFAULT NULL COMMENT '用户cuid',
               `open_id` varchar(255) NOT NULL DEFAULT '' COMMENT 'openid',
               `response_id` bigint DEFAULT NULL COMMENT '答卷id',
               `s_id` varchar(50) NOT NULL DEFAULT '' COMMENT '问卷id',
               PRIMARY key (`id`),
               key idx_sid_openid(`s_id`,`open_id`)
)ENGINE=INNODB  COMMENT '问卷填答记录';

