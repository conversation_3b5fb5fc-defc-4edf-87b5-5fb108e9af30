CREATE TABLE `feature`
(
    `id`          bigint       NOT NULL,
    `create_time` timestamp    NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
    `modify_time` timestamp NULL DEFAULT NULL,
    `name`        varchar(100) NOT NULL,
    `enabled`     bit(1) NULL DEFAULT b'0',
    `title`       varchar(100) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of feature
-- ----------------------------
INSERT INTO `feature`
VALUES (5124718734914560, '2023-10-23 10:29:21', '2023-10-23 10:29:22', 'withdraw', b'1', '提现');
