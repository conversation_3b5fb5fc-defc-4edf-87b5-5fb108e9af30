CREATE TABLE adminx.`tags` (
                               `id` bigint NOT NULL AUTO_INCREMENT,
                               `name` varchar(50) NOT NULL COMMENT '标签名',
                               `deleted` TINYINT NOT NULL DEFAULT '0' COMMENT '软删除',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT= 0 DEFAULT CHARSET=utf8mb4 COMMENT '标签';

CREATE TABLE adminx.`send_record` (
                                      `id` bigint NOT NULL COMMENT 'id',
                                      `task_id` BIGINT not null comment '任务id',
                                      `s_id` VARCHAR(50) DEFAULT NULL comment '问卷id',
                                      `send_type` TINYINT default '0' comment '样本推送类型(0:全部,1:指定样本)',
                                      `enable_push` TINYINT default '0' comment '是否公众号模板推送',
                                      `rules` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL comment '发送规则',
                                      `latest` TINYINT default '0' comment '1最新的任务发送',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      key idx_task_latest(`task_id`,`latest`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '任务发送记录';

alter table api_dev.address add column `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
alter table api_dev.address add column `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间';