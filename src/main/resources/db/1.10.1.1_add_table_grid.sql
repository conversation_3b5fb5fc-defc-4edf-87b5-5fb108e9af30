CREATE TABLE `adminx`.`community_grid`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT,
    `create_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `grid_id`     varchar(32) NOT NULL COMMENT '网格id',
    `cuid`        int NULL DEFAULT NULL COMMENT '网格员cuid',
    `grid_Name`    varchar(100) NULL DEFAULT NULL COMMENT '网格名称',
    `grid_inspector_Name`    varchar(100) NULL DEFAULT NULL COMMENT '网格员名称',
    `contract`    varchar(32) NULL DEFAULT NULL COMMENT '联系方式',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB;

ALTER TABLE `adminx`.`community_grid`
    ADD UNIQUE INDEX `idx_grid_id`(`grid_id`) USING BTREE;

ALTER TABLE `adminx`.`community_users`
    ADD COLUMN `grid_id` varchar(64) NULL COMMENT '所属网格id' AFTER `cuid`;

ALTER TABLE `adminx`.`community_users`
    ADD INDEX `idx_grid_id`(`grid_id`) USING BTREE;


ALTER TABLE `adminx`.`community_users`
    ADD COLUMN `is_grid` tinyint NULL DEFAULT 0 COMMENT '是否是网格员' AFTER `grid_id`;


ALTER TABLE `adminx`.`delivery_task`
    ADD COLUMN `open_supervisor` tinyint NULL DEFAULT 0 COMMENT '是否开启督导' AFTER `task_type`;

ALTER TABLE `adminx`.`survey_completed_record`
    ADD COLUMN `grid_id` varchar(64) NULL COMMENT '通过哪个网格id填答' AFTER `s_id`;