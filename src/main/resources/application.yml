
server:
  port: ${PORT:8091}
  servlet:
    context-path: /api/adminx/
  error:
    whitelabel:
      enabled: false

spring:
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB
  config:
    import:
      - optional:befun-sms-chuanglan.yml
      - optional:befun-wechat-cp.yml
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:**********************************************************************}
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:Health2020@SZ}
    maximum-pool-size: ${MYSQL_MAX_POOL_SIZE:10} # 最大连接数, 核心数2倍+核心数
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  jpa:
    generate-ddl: false
    show-sql: ${SHOW_SQL:true}
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5Dialect
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  jackson:
    date-format: com.fasterxml.jackson.databind.util.ISO8601DateFormat
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:}

springdoc:
  api-docs:
    #是否开启文档功能，默认为true，可不配置
    enabled: ${ENABLE_DOC:true}

befun.extension.http-log.enable: ${LOG_HTTP:false}
logging:
  level:
    root: ${LOG_LEVEL:info}
    org:
      befun:
        task:
          worker: error
          service: error
      hibernate:
        SQL: info
        type: info

hanyi:
  common:
    ip-resolver:
      default-platform: ${IP_RESOLVER_PLATFORM:baidu}
      local:
        algorithm: memory
      amap:
        url: ${AMAP_IP_RESOLVER_URL:https://restapi.amap.com/v3/ip?ip=%s&key=%s}
        api-key: ${AMAP_IP_RESOLVER_KEY:881658d2e6a034ebc09c82bce16ee6c2}
        api-secret: ${AMAP_IP_RESOLVER_SECRET:secret}
      baidu:
        url: ${BAIDU_IP_RESOLVER_URL:http://api.map.baidu.com/location/ip?ip=%s&ak=%s}
        api-key: ${BAIDU_IP_RESOLVER_KEY:Hsh5kqNCOQyu8CqO6eyVbdIl0HjVOqBG}
        api-secret: ${BAIDU_IP_RESOLVER_SECRET:secret}
      tencent:
        urlIP: ${TENCENT_IP_RESOLVER_URL:https://apis.map.qq.com/ws/location/v1/ip?ip=%s&key=%s}
        url: ${TENCENT_LOCATION_RESOLVER_URL:https://apis.map.qq.com/ws/geocoder/v1/?location=%s&key=%s}
        api-key: ${TENCENT_RESOLVER_KEY:SMXBZ-PSQWZ-N7FXD-ZFLS6-AMZTJ-6YFWG}
        api-secret: ${TENCENT_RESOLVER_SECRET:}
    file-storage:
      default-platform: ${FILE_PLATFORM:oss}
      local:
        - platform: local
          enable-storage: true
          enable-access: true
          domain: ""
          base-path: /tmp/adminx/
          path-patterns: /tmp/adminx/**
      aliyun-oss:
        - platform: oss
          enable-storage: true
          access-key: ${ALICLOUD_ACCESS_KEY:LTAI4G5RfyxPtajMojKJPvmM}
          secret-key: ${ALICLOUD_SECRET_KEY:******************************}
          end-point: ${ALICLOUD_OSS_ENDPOINT:https://oss-cn-shenzhen.aliyuncs.com}
          bucket-name: ${ALICLOUD_OSS_BUCKET:dev-assets-sp}
          domain: ${ALICLOUD_OSS_DOMAIN:http://dev-assets.surveyplus.cn/}
          base-path: ${ALICLOUD_OSS_BASEPATH:lite/}

community:
  wechat-reply-message: ${WECHAT_REPLY_MESSAGE:true}
  enable-kafka: ${ENABLE_KAFKA:false}
  event:
    enable-notify: ${ENABLE_NOTIFY:true}
    default-group: ${DEFAULT_GROUP:community}
    target-url: /cem/event/operateData?eventId=${id}
    survey-response-topic: survey_response
    survey-plus-response-topic: queuing-survey-anwser
    survey-change-topic: survey_change
    user-create-topic: queuing-user-create
    cloud-user-update-topic: sync-cloud
  source:
    org-id: ${SOURCE_ORGID:1846709764819968}
    user-id: ${SOURCE_USERID:1678339507731456}
  uri:
    copy-survey: ${COPY_SURVEY_URI:https://dev.xmplus.cn/api/survey/surveys/copy}
    copy-journey-map: ${COPY_JOURNEY_MAP_URI:https://dev.xmplus.cn/api/ctm/open/journey/copy}
    change-org-version: ${CHANGE_ORG_VERSION_URI:http://cem-worker-svc:5000/api/worker/task/add/changeOrgVersion}
    user-scores-home-page: ${USER_SCORES_HOME_PAGE:https://dev.surveyplus.cn/spc/my/integralRecord}
    register-user: ${REGISTER_USER_URI:https://dev.xmplus.cn/api/auth/register-by-api}
    sample-order-notice: ${SAMPLE_ORDER_NOTICE:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8ec113ed-e8b4-4870-a5d7-d92d33990ebd}
    adminx-login: ${ADMINX_LOGIN_URI:https://test.xmplus.cn/adminx/login}
    response-quota-rollback: ${RESPONSE_QUOTA_ROLLBACK_URI:https://dev.xmplus.cn/api/survey/surveys/quota-rollback}
    sync-quota: ${SYNC_QUOTA_URI:https://dev.xmplus.cn/api/survey/surveys/%d/quotas/sync?token=bc7c85319a63499bbae490bb020067b5}
  enable-sync-all: ${ENABLE_SYNC_ALL:false}
  enable-sync-location: ${ENABLE_SYNC_LOCATION:true}
  complete-user-info-score: ${COMPLETE_USER_INFO_SCORE:10}
  invite-score-percent: ${INVITE_SCORE_PERCENT:0.05}
  invite-score-max: ${INVITE_SCORE_MAX:10000}
befun:
  task:
    prefix: ${BEFUN_TASK_PREFIX:befun.task}
    trim:
      enabled: true
      cron: "*/10 * * * * *"
      max-len: ${BEFUN_TASK_TRIM_MAX_LEN:5000}
    scheduler:
      enabled: true
      cron: "*/10 * * * * *"
    worker:
      enabled: true
      interval-seconds: 5
      group: community
    queue:
      response-submit-key: ${RESPONSE_SUBMIT_REDIS_KEY:befun.task.queue.adminx-response-submit}
      response-view-key: ${RESPONSE_VIEW_REDIS_KEY:befun.task.queue.adminx-response-view}
      channel-operation-key: ${CHANNEL_OPERATION_KEY:befun.task.queue.adminx-channel-operation}
      adminx-sync-quota-key: ${ADMINX_SYNC_QUOTA_KEY:befun.task.queue.adminx-sync-quota}
  admin:
    survey-plus-db-name: ${API_DATA_BASE_NAME:api_dev}
    survey-plus-url: ${SURVEY_PLUS_URL:https://dev.surveyplus.cn/spc/sample?sid=%s}
    xm-plus-url: ${XM_PLUS_URL:https://dev.xmplus.cn/lite/%s?channelId=%d&collectorMethod=SURVEY_PLUS}
    invite-url: ${INVITE_URL:https://test-scp.surveyplus.cn/spc/home?invite=%s}
    front-route-url: ${FRONT_ROUTE_URL:https://test-scp.surveyplus.cn/spc/task/share?trackId=%s&inviteurl=%s} #前端填答问卷路由地址
  extension:
    wechat-mp:
      enable: true
      app-id: ${WECHAT_MP_CEM_APP_ID:wx38eb115cf5014c74}
      app-secret: ${WECHAT_MP_CEM_APP_SECRET:03eca9a58e86f8e376e6cc2ea33a06c0}
      token: ${WECHAT_MP_CEM_TOKEN:surveyplus20190710surveyplus}
      audit-notice-template: ${AUDIT_NOTICE_TEMPLATE:-SHleOivuU-dmWJBC8lKAm_0AuGrSuT5X519X-5eupU}
      survey-accept-template: ${SURVEY_ACCEPT_TEMPLATE:aFW95ZTeQ1rt9bsplzvT_0VXT1S_msb1SGX6kvwV3PI}
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:true}
      app-id: ${WECHAT_PAY_APP_ID:wx927b20872844e740}
      mch-id: ${WECHAT_PAY_MCH_ID:1522047931}
      v2-mch-key: ${WECHAT_PAY_V2_MCH_KEY:fRtOOBljwdZUyfCPH8Hx5h65XkaZF1tT}
      v3-mch-key: ${WECHAT_PAY_V3_MCH_KEY:oq8VJVMG3o8Fz20LkSCgbavcLiSpUy5U}
      cert-p12-path: ${WECHAT_PAY_CERT_P12_PATH:/config/cert/wx/apiclient_cert.p12}
      private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:/config/cert/wx/apiclient_key.pem}
      cert-pem-path: ${WECHAT_PAY_CERT_PEM_PATH:/config/cert/wx/apiclient_cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/placeOrder/callback/cem}
    shorturl:
      root: ${SHORTURL:https://dev-t.xmplus.cn}
  wechat-transfer:
    app-id: ${WECHAT_PAY_APP_ID:wx38eb115cf5014c74}
    transfer-scene-id: ${WECHAT_TRANSFER_SCENE_ID:1000}
    activity-name: ${WECHAT_TRANSFER_ACTIVITY_NAME:参与问卷答题，获取红包奖励}
    activity-content: ${WECHAT_TRANSFER_ACTIVITY_CONTENT:参与问卷答题，获取红包奖励}
    remark: ${WECHAT_TRANSFER_REMARK:参与问卷答题，获取红包奖励}
    call-back-url: ${WECHAT_TRANSFER_CALL_BACK_URL:https://play.svix.com/view/e_Bm4Ho5aXFWF30WDsMgXbYjqiIlx/31GUkDfNq9MYM2bQBN6dlmaFRVq}


sentry:
  dsn: https://<EMAIL>/6434796