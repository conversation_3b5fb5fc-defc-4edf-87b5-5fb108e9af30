befun:
  extension:
    sms:
      vendor: chuanglan
      enable: ${SMS_ENABLE:true}
      enable-chuanglan: ${SMS_ENABLE_CHAUNGLAN:true}
      providers:
        - name: chuanglan
          chuanglan:
            app-id: ${CHUANGLAN_APP_ID:N925547_N9266634}
            app-secret: ${CHUANGLAN_APP_SECRET:lvuVeGa247fdb1}
            signature: ${CHUANGLAN_SIGNATURE:【调研家社区】}
            real-signature: ${CHUANGLAN_REAL_SIGNATURE:【调研家社区】}
          templates:
            - name: FOLLOW_TASK_1
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_1:932735}
              content: 您好，感谢您${projectTime}参与了由${projectManager}发起的${projectName}的调查。这是该项目的${projectRound}追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有${projectReward}元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${url.code}
              pattern: ^您好，感谢您(.+)参与了由(.+)发起的(.+)的调查。这是该项目的(.+)追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有(.+)元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/(\w+)$
              origin-template: '您好，感谢您{$var}参与了由{$var}发起的{$var}的调查。这是该项目的{$var}追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有{$var}元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/{$var}'
              variables: '{$var},{$var},{$var},{$var},{$var},{$var}'
              parameters: '[{"name": "projectTime", "title": "时间"},{"name": "projectManager", "title": "委托方"},{"name": "projectName", "title": "项目名"},{"name": "projectRound", "title": "轮次"},{"name": "projectReward", "title": "酬谢金"}]'
            - name: FOLLOW_TASK_AWARD
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_1:932737}
              content: 您好，感谢您最近参与了${name}调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！
              pattern: ^您好，感谢您最近参与了(.+)调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！
              origin-template: '您好，感谢您最近参与了{$var}调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！'
              variables: '{$var}'
              parameters: '[{"name": "name", "title": "项目名"}]'
            - name: FOLLOW_TASK_2
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_2:932736}
              content: 您好，感谢您${projectTime}参与了由${projectManager}发起的${projectName}的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的${projectReward}元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${url.code}
              pattern: ^您好，感谢您(.+)参与了由(.+)发起的(.+)的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的(.+)元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/(\w+)$
              origin-template: '您好，感谢您{$var}参与了由{$var}发起的{$var}的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的{$var}元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/{$var}'
              variables: '{$var},{$var},{$var},{$var},{$var}'
              parameters: '[{"name": "projectTime", "title": "时间"},{"name": "projectManager", "title": "委托方"},{"name": "projectName", "title": "项目名"},{"name": "projectReward", "title": "酬谢金"}]'
