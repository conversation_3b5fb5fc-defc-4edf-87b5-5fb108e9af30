befun:
  extension:
    wechat:
      vendor: wechat
      enable: ${WECHAT_ENABLE:true}
      enable-wechat: ${SMS_ENABLE_WECHAT:true}
      providers:
        - name: wechat
          wechat:
            app-id: ${CHUANGLAN_APP_ID:N925547_N9266634}
            app-secret: ${CHUANGLAN_APP_SECRET:lvuVeGa247fdb1}
            signature: ${CHUANGLAN_SIGNATURE:【调研家社区】}
            real-signature: ${CHUANGLAN_REAL_SIGNATURE:【调研家社区】}
          templates:
            - name: FOLLOW_TASK_1
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_1:932735}
              title: 追访模板1（未访问）
              content: 您好，感谢您${FollowTaskInfoDto.time}参与了由${FollowTaskInfoDto.manager}发起的${FollowTaskInfoDto.name}的调查。这是该项目的${FollowTaskInfoDto.round}追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有${FollowTaskInfoDto.reward}元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${url.code}
              pattern: ^您好，感谢您(.+)参与了由(.+)发起的(.+)的调查。这是该项目的(.+)追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有(.+)元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/(\w+)$
              origin-template: '您好，感谢您{$var}参与了由{$var}发起的{$var}的调查。这是该项目的{$var}追踪访问，希望您能够继续参加，谢谢您的参与！此次调查有{$var}元薄酬，将在您完成问卷后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/{$var}'
              variables: '{$var},{$var},{$var},{$var},{$var},{$var}'
              parameters: '[{"name": "FollowTaskInfoDto.time", "title": "时间"},{"name": "FollowTaskInfoDto.manager", "title": "委托方"}
                {"name": "FollowTaskInfoDto.name", "title": "项目名"},{"name": "FollowTaskInfoDto.round", "title": "轮次"},
                {"name": "FollowTaskInfoDto.reward", "title": "酬谢金"}]'
            - name: FOLLOW_TASK_AWARD
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_1:932737}
              title: 追访酬金到账通知
              content: 您好，感谢您最近参与了${FollowTaskInfoDto.name}调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！
              pattern: ^您好，感谢您最近参与了(.+)调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！
              origin-template: '您好，感谢您最近参与了{$var}调查；您的酬金奖励已到账，您可在微信内搜索并关注调研家社区公众号进入提现页面直接领取酬金，如有疑问可加研仔微信（survey-diaoyanjia）咨询，谢谢您的参与！'
              variables: '{$var}'
              parameters: '[{"name": "FollowTaskInfoDto.name", "title": "项目名"}]'
            - name: FOLLOW_TASK_2
              id: ${CHUANGLAN_TEMPLATE_ID_FOLLOW_TASK_2:932736}
              title: 追访模板2（访问未提交）
              content: 您好，感谢您${FollowTaskInfoDto.time}参与了由${FollowTaskInfoDto.manager}发起的${FollowTaskInfoDto.name}的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的${FollowTaskInfoDto.reward}元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/${url.code}
              pattern: ^您好，感谢您(.+)参与了由(.+)发起的(.+)的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的(.+)元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/(\w+)$
              origin-template: '您好，感谢您{$var}参与了由{$var}发起的{$var}的调查，并参与了最近的一轮追踪访问。您的问卷尚未提交，恳请您继续填答。您的{$var}元酬金将在问卷完成后发放，谢谢您的参与！点击链接开始答题：https://t.xmplus.cn/{$var}'
              variables: '{$var},{$var},{$var},{$var},{$var}'
              parameters: '[{"name": "FollowTaskInfoDto.time", "title": "时间"},{"name": "FollowTaskInfoDto.manager", "title": "委托方"}
                {"name": "FollowTaskInfoDto.name", "title": "项目名"},{"name": "FollowTaskInfoDto.reward", "title": "酬谢金"}]'
